/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 5A35C9567E184BDB61E748D4E656C367F3B0D627
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemRevision_ModelTestSuite_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #ChangedWorkItems
    (
        WorkItemId   INT NOT NULL,
        ProjectGuid  UNIQUEIDENTIFIER,
        Revision     INT NOT NULL

        INDEX IX_#ChangedWorkItems_WorkItemId_ProjectId CLUSTERED (WorkItemId, ProjectGuid)
    )

    CREATE TABLE #ChangedTestSuiteTitles
    (
        TestSuiteId INT NOT NULL PRIMARY KEY,
        Title       NVARCHAR(256) COLLATE DATABASE_DEFAULT,
        Depth       INT
    )

    DECLARE @changedTestSuiteWorkItems INT = 0;
    SET @updatedCount = 0;

    -- First get all the changed work item revisions along with their types
    -- Here we are sub-batching just on work item IDs and not (WorkItemId, Revision) touple. Hence adding WITH TIES too.
    INSERT #ChangedWorkItems (WorkItemId, Revision, ProjectGuid)
    SELECT s.System_Id, MAX(s.System_Rev), s.System_ProjectGuid
    FROM (
             SELECT TOP (@batchSizeMax) WITH TIES wir.System_Id, wir.System_Rev, System_ProjectGuid
             FROM AnalyticsStage.tbl_WorkItemRevision wir WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
             WHERE wir.PartitionId = @partitionId
                   AND wir.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                   AND wir.System_Id > ISNULL(@stateData, 0)
             ORDER BY wir.System_Id
         ) s
    GROUP BY s.System_Id, s.System_ProjectGuid
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete     = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT TOP 1 WorkItemId FROM #ChangedWorkItems ORDER BY WorkItemId DESC)

    -- Update test plan title
    UPDATE t
    SET    AnalyticsUpdatedDate = @batchDt,
           AnalyticsBatchId     = @batchId,
           TestPlanTitle        = wir.System_Title
    FROM   #ChangedWorkItems s
    INNER LOOP JOIN AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_ProjectSK_TestPlanId))
    ON     t.PartitionId = @partitionId
           AND t.ProjectSK   = s.ProjectGuid
           AND t.TestPlanId  = s.WorkItemId
    INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision wir WITH (INDEX (CI_tbl_WorkItemRevision))
    ON     wir.PartitionId = @partitionId
           AND wir.System_Id   = s.WorkItemId
           AND wir.System_Rev  = s.Revision
    WHERE  NOT EXISTS
           (
               SELECT t.TestPlanTitle
               INTERSECT
               SELECT wir.System_Title
           )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER); -- FORCE ORDER to first let the test suites join. This join itself would return 0 rows in most cases.

    SET @updatedCount += @@ROWCOUNT;

    -- Update test suite titles
    UPDATE t
    SET    AnalyticsUpdatedDate = @batchDt,
           AnalyticsBatchId     = @batchId,
           Title                = wir.System_Title
    OUTPUT INSERTED.TestSuiteId,
           INSERTED.Title,
           INSERTED.Depth
    INTO   #ChangedTestSuiteTitles
           (
               TestSuiteId,
               Title,
               Depth
           )
    FROM   #ChangedWorkItems s
    INNER LOOP JOIN AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_ProjectSK_TestSuiteId))
    ON     t.PartitionId = @partitionId
           AND t.ProjectSK   = s.ProjectGuid
           AND t.TestSuiteId = s.WorkItemId
    INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision wir WITH (INDEX (CI_tbl_WorkItemRevision))
    ON     wir.PartitionId = @partitionId
           AND wir.System_Id   = s.WorkItemId
           AND wir.System_Rev  = s.Revision
    WHERE  NOT EXISTS
           (
               SELECT t.Title
               INTERSECT
               SELECT wir.System_Title
           )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER); -- FORCE ORDER to first let the test suites join. This join itself would return 0 rows in most cases.

    SET @changedTestSuiteWorkItems = @@ROWCOUNT;
    SET @updatedCount += @changedTestSuiteWorkItems;

    IF (@changedTestSuiteWorkItems > 0)
    BEGIN
        DECLARE @DistinctChangedDepths dbo.typ_Int32Table

        INSERT INTO @DistinctChangedDepths (Val)
        SELECT DISTINCT Depth FROM #ChangedTestSuiteTitles

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 1)
        BEGIN
            -- Update the level 1 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel1          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel1), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel1 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel1
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 2)
        BEGIN
            -- Update the level 2 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel2          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel2), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel2 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel2
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 3)
        BEGIN
            -- Update the level 3 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel3          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel3), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel3 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel3
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 4)
        BEGIN
            -- Update the level 4 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel4          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel4), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel4 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel4
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 5)
        BEGIN
            -- Update the level 5 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel5          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel5), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel5 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel5
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 6)
        BEGIN
            -- Update the level 6 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel6          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel6), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel6 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel6
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 7)
        BEGIN
            -- Update the level 7 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel7          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel7), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel7 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel7
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 8)
        BEGIN
            -- Update the level 8 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel8          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel8), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel8 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel8
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 9)
        BEGIN
            -- Update the level 9 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel9          = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel9), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId  = @partitionId
                   AND mts.IdLevel9 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel9
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 10)
        BEGIN
            -- Update the level 10 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel10         = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel10), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId   = @partitionId
                   AND mts.IdLevel10 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel10
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 11)
        BEGIN
            -- Update the level 11 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel11         = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel11), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId   = @partitionId
                   AND mts.IdLevel11 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel11
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 12)
        BEGIN
            -- Update the level 12 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel12         = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel12), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId   = @partitionId
                   AND mts.IdLevel12 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel12
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 13)
        BEGIN
            -- Update the level 13 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel13         = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel13), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId   = @partitionId
                   AND mts.IdLevel13 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel13
                   )

            SET @updatedCount += @@ROWCOUNT;
        END

        IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 14)
        BEGIN
            -- Update the level 14 suites in model table
            UPDATE mts
            SET    AnalyticsUpdatedDate = @batchDt,
                   AnalyticsBatchId     = @batchId,
                   TitleLevel14         = s.Title
            FROM   AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_IdLevel14), FORCESEEK)
            JOIN   #ChangedTestSuiteTitles s
            ON     mts.PartitionId   = @partitionId
                   AND mts.IdLevel14 = s.TestSuiteId
            WHERE  NOT EXISTS
                   (
                       SELECT s.Title
                       INTERSECT
                       SELECT mts.TitleLevel14
                   )

            SET @updatedCount += @@ROWCOUNT;
        END
    END

    DROP TABLE #ChangedWorkItems
    DROP TABLE #ChangedTestSuiteTitles

    RETURN 0
END

GO

