/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FE9CF6BC6F66701D875A51DF4D14D561D174E200
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageProject_ModelTag_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    )

    ;WITH Src AS
    (
        SELECT  t.PartitionId,
                t.TagId,
                t.Name AS TagName,
                p.ProjectGuid AS ProjectSK,
                ISNULL(t.IsDeleted, 0) AS IsDeleted
        FROM    AnalyticsStage.tbl_Tag t
        JOIN    AnalyticsStage.tbl_Project p
        ON      p.PartitionId = t.PartitionId
                AND (t.Scope = p.ProjectGuid OR t.IsGlobalScope = 1)
        WHERE   p.PartitionId = @partitionId
                AND p.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    )
    MERGE   TOP (@batchSizeMax) AnalyticsModel.tbl_Tag AS t
    USING   Src AS s
    ON      t.PartitionId = s.PartitionId
            AND t.TagId = s.TagId
            AND t.ProjectSK = s.ProjectSK
    WHEN MATCHED AND NOT EXISTS (
            SELECT  s.TagName,
                    s.IsDeleted
            INTERSECT
            SELECT  t.TagName,
                    t.IsDeleted
            ) THEN
    UPDATE SET
            AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            TagName        = s.TagName,
            IsDeleted      = s.IsDeleted
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
            PartitionId,
            AnalyticsBatchId,
            AnalyticsCreatedDate,
            AnalyticsUpdatedDate,
            TagId,
            TagName,
            IsDeleted,
            ProjectSK
            )
    VALUES (
            s.PartitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.TagId,
            s.TagName,
            s.IsDeleted,
            s.ProjectSK
            )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount  = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount  = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    RETURN 0
END

GO

