/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 393AD822DA25EDA6765C0C2ECFDFD472A03CC573
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemIteration_ModelIteration_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    DECLARE @localizedUnknown   NVARCHAR(230)

    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_UNKNOWN',
                                                            @defaultValue = 'Unknown',
                                                            @localizedValue =  @localizedUnknown OUTPUT

    CREATE TABLE #ImpactedWithParents
    (
        PartitionId         INT              NOT NULL,
        IterationGuid       UNIQUEIDENTIFIER NOT NULL,
        IterationId         INT              NULL,
        IterationName       NVARCHAR(256)    COLLATE DATABASE_DEFAULT NULL,
        StartDate           DATE             NULL,
        EndDate             DATE             NULL,
        ParentIterationGuid UNIQUEIDENTIFIER NULL,
        ProjectGuid         UNIQUEIDENTIFIER NULL,
        IsDeleted           BIT              NULL,
        IsRootNode          BIT              NOT NULL,
        Impacted            INT              NOT NULL
    )

    CREATE TABLE #Trees
    (
        PartitionId         INT              NOT NULL,
        IterationGuid       UNIQUEIDENTIFIER NOT NULL,
        IterationId         INT              NULL,
        IterationName       NVARCHAR(256)    COLLATE DATABASE_DEFAULT NULL,
        StartDate           DATE             NULL,
        EndDate             DATE             NULL,
        IterationPath       NVARCHAR(4000)   COLLATE DATABASE_DEFAULT NULL,
        ParentIterationGuid UNIQUEIDENTIFIER NULL,
        ProjectGuid         UNIQUEIDENTIFIER NULL,
        IsDeleted           BIT              NULL,
        IsRootNode          BIT              NOT NULL,
        Impacted            INT              NOT NULL
    )

    CREATE UNIQUE CLUSTERED INDEX CL_Trees ON #Trees (PartitionId, IterationGuid)

    DECLARE @changedIterationGuids TABLE
    (
        IterationGuid UNIQUEIDENTIFIER PRIMARY KEY
    )

    IF (@triggerTableName = 'Project')
    BEGIN
        INSERT INTO @changedIterationGuids
        SELECT i.IterationGuid
        FROM AnalyticsStage.tbl_Project p
        JOIN AnalyticsStage.tbl_WorkItemIteration i
            ON i.PartitionId = p.PartitionId
            AND i.ProjectGuid = p.ProjectGuid
        WHERE p.PartitionId = @partitionId
            AND p.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE IF (@triggerTableName = 'WorkItemIteration')
    BEGIN
        INSERT INTO @changedIterationGuids
        SELECT i.IterationGuid
        FROM AnalyticsStage.tbl_WorkItemIteration i
        WHERE i.PartitionId = @partitionId
            AND i.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    -- Recursive CTE to chase up to roots from impacted guids
    ;WITH ImpactedWithParents AS
    (
        SELECT i.*, IIF(i.ParentIterationGuid = i.ProjectGuid OR i.ParentIterationGuid IS NULL, 1, 0) AS IsRootNode, 1 AS Impacted
        FROM AnalyticsStage.tbl_WorkItemIteration i
        WHERE i.PartitionId = @partitionId AND i.IterationGuid IN (SELECT IterationGuid FROM @changedIterationGuids)

        UNION ALL

        SELECT i.*, IIF(i.ParentIterationGuid = i.ProjectGuid OR i.ParentIterationGuid IS NULL, 1, 0) AS IsRootNode, 0 AS Impacted
        FROM ImpactedWithParents ch
        JOIN AnalyticsStage.tbl_WorkItemIteration i ON i.PartitionId = ch.PartitionId AND i.IterationGuid = ch.ParentIterationGuid
    )
    INSERT #ImpactedWithParents
    SELECT PartitionId, IterationGuid, IterationId, IterationName,
        CAST(StartDate AS DATE), -- TFS sends only date part
        CAST(EndDate AS DATE), -- TFS sends only date part
        ParentIterationGuid, ProjectGuid, IsDeleted, IsRootNode, MAX(Impacted) AS Impacted
    FROM ImpactedWithParents
    GROUP BY PartitionId, IterationGuid, IterationId, IterationName, StartDate, EndDate, ParentIterationGuid, ProjectGuid, IsDeleted, IsRootNode
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- first insert the roots
    INSERT #Trees
    SELECT PartitionId, IterationGuid, IterationId, IterationName, StartDate, EndDate, NULL AS IterationPath, ParentIterationGuid, ProjectGuid, IsDeleted, IsRootNode, Impacted
    FROM #ImpactedWithParents
    WHERE IsRootNode = 1

    -- now insert other impacted nodes and parents
    WHILE (@@ROWCOUNT > 0)
    BEGIN
        INSERT #Trees
        SELECT i.PartitionId, i.IterationGuid, i.IterationId, i.IterationName, i.StartDate, i.EndDate, IIF(p.IsRootNode = 1, i.IterationName, CONCAT(p.IterationPath, '\', i.IterationName)) AS IterationPath, i.ParentIterationGuid, i.ProjectGuid, i.IsDeleted, 0 AS IsRootNode, IIF(c.IterationGuid IS NOT NULL, 1, p.Impacted) AS Impacted
        FROM #ImpactedWithParents i
        JOIN #Trees p ON i.PartitionId = p.PartitionId AND i.ParentIterationGuid = p.IterationGuid
        LEFT JOIN #Trees s ON s.PartitionId = i.PartitionId AND s.IterationGuid = i.IterationGuid --self
        LEFT JOIN @changedIterationGuids c ON c.IterationGuid = i.IterationGuid
        WHERE s.IterationGuid IS NULL -- make sure it's not already in the table - working on new leaves only
    END

    DECLARE @Noop INT = (SELECT 1) -- to inialize @@ROWCOUNT

    -- now insert children of impacted nodes
    WHILE (@@ROWCOUNT > 0)
    BEGIN
        INSERT #Trees
        SELECT i.PartitionId, i.IterationGuid, i.IterationId, i.IterationName, i.StartDate, i.EndDate, IIF(p.IsRootNode = 1, i.IterationName, CONCAT(p.IterationPath, '\', i.IterationName)) AS IterationPath, i.ParentIterationGuid, i.ProjectGuid, i.IsDeleted, 0 AS IsRootNode, 1 AS Impacted
        FROM AnalyticsStage.tbl_WorkItemIteration i
        JOIN #Trees p ON i.PartitionId = p.PartitionId AND i.ParentIterationGuid = p.IterationGuid --parent
        LEFT JOIN #Trees s ON s.PartitionId = i.PartitionId AND s.IterationGuid = i.IterationGuid --self
        WHERE s.IterationGuid IS NULL -- make sure it's not already in the table - working on new leaves only
            AND p.Impacted = 1
            AND p.IsRootNode = 0 -- root nodes changes don't impact children (name is not used)
    END

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    ;WITH Splits AS
    (
        SELECT
            i.PartitionId,
            IterationGuid,
            MAX(CASE WHEN Ordinal = 0 THEN Value END) IterationLevel2,
            MAX(CASE WHEN Ordinal = 1 THEN Value END) IterationLevel3,
            MAX(CASE WHEN Ordinal = 2 THEN Value END) IterationLevel4,
            MAX(CASE WHEN Ordinal = 3 THEN Value END) IterationLevel5,
            MAX(CASE WHEN Ordinal = 4 THEN Value END) IterationLevel6,
            MAX(CASE WHEN Ordinal = 5 THEN Value END) IterationLevel7,
            MAX(CASE WHEN Ordinal = 6 THEN Value END) IterationLevel8,
            MAX(CASE WHEN Ordinal = 7 THEN Value END) IterationLevel9,
            MAX(CASE WHEN Ordinal = 8 THEN Value END) IterationLevel10,
            MAX(CASE WHEN Ordinal = 9 THEN Value END) IterationLevel11,
            MAX(CASE WHEN Ordinal = 10 THEN Value END) IterationLevel12,
            MAX(CASE WHEN Ordinal = 11 THEN Value END) IterationLevel13,
            MAX(CASE WHEN Ordinal = 12 THEN Value END) IterationLevel14,
            MAX(Ordinal) AS Depth
        FROM #Trees i
        CROSS APPLY AnalyticsInternal.func_SplitString(i.IterationPath, '\')
        WHERE i.Impacted = 1
        GROUP BY i.PartitionId, IterationGuid
    )
    , Src AS
    (
        SELECT
            i.PartitionId,
            i.IterationGuid AS IterationId,
            CAST(i.StartDate AS DATETIME) AT TIME ZONE @timeZone AS StartDate,
            -- DateTimeOffset/DateTime2 has 100ns accuracy, but Datetime Accuracy: Rounded to increments of .000, .003, or .007 seconds
            -- Using Datetime2 in coversions
            IIF(i.EndDate < '9999-01-01', DATEADD(ms, 86399999, CAST(i.EndDate AS DATETIME2)), CAST(i.EndDate AS DATETIME2)) AT TIME ZONE @timeZone AS EndDate,
            ISNULL(i.IterationName, ISNULL(p.ProjectName, @localizedUnknown)) AS IterationName,
            i.IterationId AS Number,
            CASE WHEN i.IterationName IS NULL THEN ISNULL(p.ProjectName, @localizedUnknown) ELSE CONCAT(ISNULL(p.ProjectName, @localizedUnknown), '\', i.IterationPath) END AS IterationPath,
            ISNULL(p.ProjectName, @localizedUnknown) AS IterationLevel1,
            IterationLevel2,
            IterationLevel3,
            IterationLevel4,
            IterationLevel5,
            IterationLevel6,
            IterationLevel7,
            IterationLevel8,
            IterationLevel9,
            IterationLevel10,
            IterationLevel11,
            IterationLevel12,
            IterationLevel13,
            IterationLevel14,
            IIF(i.IterationName IS NULL, Depth, Depth + 1) AS Depth,
            ISNULL(i.IsDeleted, 0) AS IsDeleted,
            i.ProjectGuid AS ProjectSK
        FROM #Trees i
        LEFT JOIN AnalyticsStage.tbl_Project p ON p.PartitionId = i.PartitionId AND p.ProjectGuid = i.ProjectGuid
        LEFT JOIN Splits ON i.PartitionId = Splits.PartitionId AND i.IterationGuid = Splits.IterationGuid
        WHERE i.Impacted = 1
    )
    MERGE TOP (@batchSizeMax) AnalyticsModel.tbl_Iteration AS t
    USING Src AS s
    ON (t.PartitionId = s.PartitionId AND t.IterationId = s.IterationId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.IterationName
        , s.Number
        , s.StartDate
        , s.EndDate
        , CAST(s.StartDate AS DATETIME2)
        , CAST(s.EndDate AS DATETIME2)
        , s.IterationPath
        , s.IterationLevel1
        , s.IterationLevel2
        , s.IterationLevel3
        , s.IterationLevel4
        , s.IterationLevel5
        , s.IterationLevel6
        , s.IterationLevel7
        , s.IterationLevel8
        , s.IterationLevel9
        , s.IterationLevel10
        , s.IterationLevel11
        , s.IterationLevel12
        , s.IterationLevel13
        , s.IterationLevel14
        , s.Depth
        , s.IsDeleted
        , s.ProjectSK
        INTERSECT
        SELECT
        t.IterationName
        , t.Number
        , t.StartDate
        , t.EndDate
        , CAST(t.StartDate AS DATETIME2)
        , CAST(t.EndDate AS DATETIME2)
        , t.IterationPath
        , t.IterationLevel1
        , t.IterationLevel2
        , t.IterationLevel3
        , t.IterationLevel4
        , t.IterationLevel5
        , t.IterationLevel6
        , t.IterationLevel7
        , t.IterationLevel8
        , t.IterationLevel9
        , t.IterationLevel10
        , t.IterationLevel11
        , t.IterationLevel12
        , t.IterationLevel13
        , t.IterationLevel14
        , t.Depth
        , t.IsDeleted
        , t.ProjectSK
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt
        , AnalyticsBatchId = @batchId
        , IterationName = s.IterationName
        , Number = s.Number
        , StartDate = s.StartDate
        , EndDate = s.EndDate
        , IterationPath = s.IterationPath
        , IterationLevel1 = s.IterationLevel1
        , IterationLevel2 = s.IterationLevel2
        , IterationLevel3 = s.IterationLevel3
        , IterationLevel4 = s.IterationLevel4
        , IterationLevel5 = s.IterationLevel5
        , IterationLevel6 = s.IterationLevel6
        , IterationLevel7 = s.IterationLevel7
        , IterationLevel8 = s.IterationLevel8
        , IterationLevel9 = s.IterationLevel9
        , IterationLevel10 = s.IterationLevel10
        , IterationLevel11 = s.IterationLevel11
        , IterationLevel12 = s.IterationLevel12
        , IterationLevel13 = s.IterationLevel13
        , IterationLevel14 = s.IterationLevel14
        , Depth = s.Depth
        , IsDeleted = s.IsDeleted
        , ProjectSK = s.ProjectSK
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId
        , AnalyticsBatchId
        , AnalyticsCreatedDate
        , AnalyticsUpdatedDate
        , IterationId
        , StartDate
        , EndDate
        , IterationName
        , Number
        , IterationPath
        , IterationLevel1
        , IterationLevel2
        , IterationLevel3
        , IterationLevel4
        , IterationLevel5
        , IterationLevel6
        , IterationLevel7
        , IterationLevel8
        , IterationLevel9
        , IterationLevel10
        , IterationLevel11
        , IterationLevel12
        , IterationLevel13
        , IterationLevel14
        , Depth
        , IsDeleted
        , ProjectSK
    )
    VALUES (
        s.PartitionId
        , @batchId
        , @batchDt
        , @batchDt
        , s.IterationId
        , s.StartDate
        , s.EndDate
        , s.IterationName
        , s.Number
        , s.IterationPath
        , s.IterationLevel1
        , s.IterationLevel2
        , s.IterationLevel3
        , s.IterationLevel4
        , s.IterationLevel5
        , s.IterationLevel6
        , s.IterationLevel7
        , s.IterationLevel8
        , s.IterationLevel9
        , s.IterationLevel10
        , s.IterationLevel11
        , s.IterationLevel12
        , s.IterationLevel13
        , s.IterationLevel14
        , s.Depth
        , s.IsDeleted
        , s.ProjectSK
    )
    OUTPUT $action INTO @changes;

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    RETURN 0
END

GO

