/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 62FD9770AE24D5463C08DA5B8974107772EE677A
CREATE PROCEDURE AnalyticsInternal.prc_iGenerateCloneFieldsExpressions
   @mainTableName NVARCHAR(255),
   @columnDefinitions AnalyticsInternal.typ_ColumnServicingDefinition READONLY,
   @allFields NVARCHAR(MAX) OUTPUT,
   @allFieldsWithPrefix NVARCHAR(MAX) OUTPUT,
   @allFieldsWithTargetPrefix NVARCHAR(MAX) OUTPUT,
   @updateFields NVARCHAR(MAX) OUTPUT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @newLine CHAR(2) = CHAR(10)+CHAR(13) -- CR+LF

    SET @allFields = ''
    SET @allFieldsWithPrefix  = ''
    SET @allFieldsWithTargetPrefix = ''
    SET @updateFields  = ''

    SELECT      @allFields += @newLine + c.Name + ',',
                @allFieldsWithPrefix += @newLine + ISNULL(e.ColumnExpression, 's.' + c.Name) + ',',
                @allFieldsWithTargetPrefix += @newLine + 't.' + c.Name + ',',
                @updateFields += IIF(c.is_identity != 1, @newLine + c.Name + ' = ' + ISNULL(e.ColumnExpression, 's.' + c.Name) + ',' ,'')
    FROM        sys.columns c
    JOIN        sys.types t
    ON          c.system_type_id=t.system_type_id
    AND         c.user_type_id=t.user_type_id
    LEFT JOIN   @columnDefinitions e
    ON          c.Name = e.ColumnName
    WHERE       c.object_id = OBJECT_ID(@mainTableName + '_Temp')
    ORDER BY    column_id

    SET @allFields = SUBSTRING(@allFields, 1, LEN(@allFields) - 1)
    SET @allFieldsWithPrefix = SUBSTRING(@allFieldsWithPrefix, 1, LEN(@allFieldsWithPrefix) - 1)
    SET @allFieldsWithTargetPrefix = SUBSTRING(@allFieldsWithTargetPrefix, 1, LEN(@allFieldsWithTargetPrefix) - 1)
    SET @updateFields = SUBSTRING(@updateFields, 1, LEN(@updateFields) - 1)

    RETURN 0
END

GO

