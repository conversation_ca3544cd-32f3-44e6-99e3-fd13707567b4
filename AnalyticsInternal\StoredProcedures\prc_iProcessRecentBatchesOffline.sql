/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FCA8A4EA3B96A9AEEE0C3B671FF0FB7E2B762562
CREATE PROCEDURE AnalyticsInternal.prc_iProcessRecentBatchesOffline
   @maintenanceId INT,
   @tableName NVARCHAR(255),
   @holdReason NVARCHAR(255),
   @columnDefinitions AnalyticsInternal.typ_ColumnServicingDefinition READONLY
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON
    SET DEADLOCK_PRIORITY HIGH -- Maintenance is expensive in case of deadlock kill transform that will be retried

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @newLine CHAR(2) = CHAR(10)+CHAR(13) -- CR+LF
    DECLARE @indexName NVARCHAR(256)
    DECLARE @mainTableName NVARCHAR(256)
    DECLARE @transformTableName NVARCHAR(256)
    DECLARE @applyMaxBatchFilter NVARCHAR(256)
    DECLARE @keyColumn NVARCHAR(256)
    DECLARE @matchPredicate NVARCHAR(256)
    DECLARE @hasIdentity BIT
    DECLARE @optimizeBatchCalculation BIT
    DECLARE @optimizeForInsertOnly BIT

    SELECT @tableName, @maintenanceId

    SELECT  @mainTableName = TableName,
            @applyMaxBatchFilter = ApplyMaxBatchFilter,
            @keyColumn = KeyColumn,
            @hasIdentity = HasIdentity,
            @matchPredicate = MatchPredicate,
            @indexName= IndexName,
            @transformTableName  = TransformTableName,
            @optimizeBatchCalculation = OptimizeBatchCalculation,
            @optimizeForInsertOnly = InsertOnlyOptimization
    FROM    AnalyticsInternal.func_iGetTableMaintenanceDefinitions()
    WHERE   TableName = @tableName

    DECLARE @allFields NVARCHAR(MAX) = ''
    DECLARE @allFieldsWithPrefix NVARCHAR(MAX) = ''
    DECLARE @allFieldsWithTargetPrefix NVARCHAR(MAX) = ''
    DECLARE @updateFields NVARCHAR(MAX) = ''

    DECLARE @currentPhase VARCHAR(20) = 'Offline Merge Recent'
    DECLARE @nextPhase VARCHAR(20) = 'Done'

    EXEC AnalyticsInternal.prc_iGenerateCloneFieldsExpressions     @mainTableName,
                                                                   @columnDefinitions,
                                                                   @allFields OUTPUT,
                                                                   @allFieldsWithPrefix OUTPUT,
                                                                   @allFieldsWithTargetPrefix OUTPUT,
                                                                   @updateFields OUTPUT

    -- Almost all data was copied
    -- Stop transformation and do delta
    DECLARE @reason NVARCHAR(255) = CONCAT('Start hold for ', @holdReason, ' transform table:', + @transformTableName )
    DECLARE @startPartitionId INT
    DECLARE @endPartitionId INT
    SELECT @startPartitionId = MIN(StartPartitionId), @endPartitionId = MAX(EndPartitionId) FROM AnalyticsInternal.tbl_TableMaintenancePartition WHERE TableMaintenanceId = @maintenanceId
    EXEC AnalyticsInternal.prc_iSetTransformHold @hold=1, @reason = @reason, @targetTableName=@transformTableName, @firstPartitionId =  @startPartitionId, @lastPartitionId = @endPartitionId

    DECLARE @updateCommand NVARCHAR(MAX) = N'
    DECLARE @lastEndSK BIGINT
    DECLARE @batchSize INT = 1024*1024
    DECLARE @batchStartSK BIGINT = 0
    DECLARE @batchEndSK BIGINT
    DECLARE @maxSK BIGINT
    DECLARE @minSK BIGINT
    DECLARE @minCount INT = 1024 * 100

    '
    + IIF(@hasIdentity = 1, 'SET IDENTITY_INSERT ' + @mainTableName +'_Temp ON','')
    +'

    -- Since we started new partitions might be created
    INSERT INTO AnalyticsInternal.tbl_TableMaintenancePartition
    SELECT @maintenanceId,
           PartitionId,
           @currentPhase,
           PartitionId,
           PartitionId,
           0,
           NULL
    FROM   ' + @mainTableName +' t
    WHERE  PartitionId > 0
           AND t.PartitionId BETWEEN @startPartitionId AND @endPartitionId
           AND PartitionId NOT IN (SELECT ProcessPartitionId FROM AnalyticsInternal.tbl_TableMaintenancePartition WHERE TableMaintenanceId = @maintenanceId)
    GROUP BY PartitionId

    -- TESTPATTERN: BEFORELOOP

    -- Phase 2
    WHILE EXISTS (SELECT * FROM AnalyticsInternal.tbl_TableMaintenancePartition WHERE TableMaintenanceId = @maintenanceId AND Phase = @currentPhase)
    BEGIN
        DECLARE @partitionId INT
        DECLARE @lastBatchId BIGINT
        SELECT  TOP(1)
                @partitionId = ProcessPartitionId,
                @lastBatchId = LastBatchId,
                @lastEndSK = LastSK
        FROM    AnalyticsInternal.tbl_TableMaintenancePartition
        WHERE TableMaintenanceId = @maintenanceId AND Phase = @currentPhase
        ORDER BY ProcessPartitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SELECT @partitionId, @lastBatchId

        -- Delete removed records.
        -- Doing that in all cases, becuase we could have match on count but still need to remove records
        -- (for example,  from WorkItems table where new revisions replacing old ones)

        DELETE  t
        FROM    ' + @mainTableName +'_Temp AS t
        LEFT JOIN ' + @mainTableName +' AS s
        ON      s.PartitionId = t.PartitionId
                AND '+ @matchPredicate +'
        WHERE   t.PartitionId = @partitionId
                AND s.PartitionId IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SELECT  @maxSK = MAX(' + @keyColumn +')
        FROM    ' + @mainTableName +' WITH (INDEX (' + @indexName +'))
        WHERE   PartitionId = @partitionId
                AND AnalyticsBatchId >= @lastBatchId

        SET  @batchStartSK = ISNULL(@lastEndSK - 1, 0)

        WHILE @batchStartSK < @maxSK
        BEGIN

            SELECT  @batchEndSK = MAX(' + @keyColumn +')
            FROM    (
                        SELECT  TOP (@batchSize) ' + @keyColumn +'
                        FROM    ' + @mainTableName +'  WITH (INDEX (' + @indexName +'))
                        WHERE   PartitionId = @partitionId
                                AND ' + @keyColumn +' >= @batchStartSK
                                AND AnalyticsBatchId >= @lastBatchId
                        ORDER   BY ' + @keyColumn +'
                    ) T
            OPTION (RECOMPILE)

            '
            + IIF (@optimizeForInsertOnly = 0,
            '
            --UPDATE
            UPDATE t SET
            '+@updateFields+'
            FROM    ' + @mainTableName +' s WITH (INDEX (' + @indexName +'))
            JOIN    ' + @mainTableName +'_Temp t
            ON      t.PartitionId = s.PartitionId
                    AND '+ @matchPredicate +'
            WHERE   s.PartitionId = @partitionId
                    AND s.' + @keyColumn +' BETWEEN @batchStartSK AND @batchEndSK
                    AND s.AnalyticsBatchId >= @lastBatchId
                    AND NOT EXISTS (
                    SELECT
                    '+ @allFieldsWithPrefix + '
                    INTERSECT
                    SELECT
                    '+ @allFieldsWithTargetPrefix + '

                    )
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
            ',
            '
            -- Skip UPDATE table is read-onlu
            ')
            +
            '
            --INSERT
            INSERT INTO ' + @mainTableName +'_Temp
            (
            ' + @allFields +'
            )
            SELECT
            ' + @allFieldsWithPrefix +'
            FROM ' + @mainTableName +' s WITH(INDEX(' + @indexName +'))
            LEFT JOIN ' + @mainTableName +'_Temp t
            ON      t.PartitionId = s.PartitionId
                    AND '+ @matchPredicate +'
            WHERE   s.PartitionId = @partitionId
                    AND s.' + @keyColumn +' BETWEEN @batchStartSK AND @batchEndSK
                    AND s.AnalyticsBatchId >= @lastBatchId
                    AND t.PartitionId IS NULL
            ORDER BY ' + @keyColumn +'
            OPTION (OPTIMIZE FOR (@PartitionId UNKNOWN))

            SET @batchStartSK = @batchEndSK + 1

            UPDATE  AnalyticsInternal.tbl_TableMaintenancePartition
            SET     LastSK = ISNULL(@batchEndSK, LastSK)
            WHERE   Phase = @currentPhase AND TableMaintenanceId = @maintenanceId AND ProcessPartitionId = @partitionId
            OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

            EXEC prc_iSleepIfBusy
        END

        UPDATE  AnalyticsInternal.tbl_TableMaintenancePartition
        SET     Phase = @nextPhase
        WHERE   Phase = @currentPhase AND TableMaintenanceId = @maintenanceId AND ProcessPartitionId = @partitionId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    END
    '
    + IIF(@hasIdentity = 1, 'SET IDENTITY_INSERT ' + @mainTableName +'_Temp OFF','')

    SELECT @updateCommand AS Command FOR XML PATH

    BEGIN TRY
        EXEC sp_executesql @updateCommand
        ,N'@startPartitionId INT, @endPartitionId INT, @maintenanceId INT, @currentPhase VARCHAR(20), @nextPhase VARCHAR(20)'
        , @startPartitionId = @startPartitionId
        , @endPartitionId = @endPartitionId
        , @maintenanceId = @maintenanceId
        , @currentPhase = @currentPhase
        , @nextPhase = @nextPhase
    END TRY
    BEGIN CATCH
       -- When failed to copy => exit to avoid data loss
       SET @errorMessage = ERROR_MESSAGE()
       SET @status = 1670017
       SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @mainTableName, @errorMessage)
       RETURN -1
    END CATCH

    RETURN 0
END

GO

