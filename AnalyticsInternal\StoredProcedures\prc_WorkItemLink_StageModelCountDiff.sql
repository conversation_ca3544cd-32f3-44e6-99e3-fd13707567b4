/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 7DC8A0EF0997E43D17728ADF874637F5CB7571EC
CREATE PROCEDURE AnalyticsInternal.prc_WorkItemLink_StageModelCountDiff
    @partitionId INT,
    @compareStartDate DATETIME,
    @compareEndDate DATETIME,
    @expectedCount BIGINT OUTPUT,
    @actualCount BIGINT OUTPUT,
    @kpiValue FLOAT OUTPUT,
    @failed BIT OUTPUT
AS
BEGIN

    DECLARE @fwdCount BIGINT
    DECLARE @revCount BIGINT

    SELECT  @expectedCount = COUNT(DISTINCT(TargetId))
    FROM    AnalyticsStage.tbl_WorkItemLink
    WHERE   PartitionId = @PartitionId
            AND ChangedDate < @compareEndDate
            AND IsActive = 1
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- exclude any running batches
    DECLARE @maxBatchId BIGINT
    SELECT  TOP 1 @maxBatchId = BatchId
    FROM    AnalyticsInternal.tbl_Batch
    WHERE   PartitionId = @partitionId
            AND TableName = 'Model.WorkItemLinkHistory'
            AND Ready = 1
    ORDER BY BatchId DESC
    OPTION(OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @maxBatchId = ISNULL(@maxBatchId, 9223372036854775807)

    SELECT  @actualCount = COUNT(DISTINCT(SourceWorkItemId))
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory h WITH (NOLOCK)
    WHERE   PartitionId = @partitionId
            AND CreatedDate < @compareEndDate
            AND h.AnalyticsBatchId <= @maxBatchId
            AND h.LinkTypeId < 0
            AND h.LinkTypeReferenceName != 'Unknown'
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SELECT @failed = CASE WHEN @expectedCount != @actualCount THEN 1 ELSE 0 END
    SELECT @kpiValue = @expectedCount - @actualCount

    RETURN 0

END

GO

