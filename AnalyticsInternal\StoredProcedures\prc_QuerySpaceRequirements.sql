/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 9194150F271ED1516CC9A991487DE6342099A9B3
CREATE PROCEDURE AnalyticsInternal.prc_QuerySpaceRequirements
    @partitionId INT = NULL
AS
BEGIN
    SET @partitionId = ISNULL(@partitionId, 1)

    DECLARE @results TABLE (
        DataType NVARCHAR(255),
        EstimateInMB INT,
        CurrentInMB INT,
        ModelReady BIT,
        Details NVARCHAR(MAX)
    )

    IF (OBJECT_ID('dbo.[tbl_WorkItemCoreLatest]') IS NOT NULL)
    BEGIN
        -- WIT
        DECLARE @witCount INT = (SELECT COUNT(*) FROM [dbo].[tbl_WorkItemCoreLatest] WHERE PartitionId = @partitionId)
        DECLARE @wirCount INT, @wirMax INT, @wirAverage FLOAT, @wirDev FLOAT

        SELECT @wirCount = SUM(Rev), @wirMax = MAX(Rev), @wirAverage = AVG(Rev), @wirDev = STDEVP(Rev)
        FROM [dbo].[tbl_WorkItemCoreLatest]
        WHERE PartitionId = @partitionId

        DECLARE @witSizeMB INT = (
                SELECT  SUM(ps.reserved_page_count) / 128
                FROM    sys.tables t
                JOIN    sys.indexes i
                ON      t.object_id = i.object_id
                JOIN    sys.schemas s
                ON      t.schema_id = s.schema_id
                JOIN    sys.dm_db_partition_stats ps
                ON      ps.object_id = t.object_id
                        AND ps.index_id = i.index_id
                WHERE   s.name NOT LIKE 'Analytics%'
                AND     t.name LIKE 'tbl_WorkItem%'
        )

        DECLARE @axWitSizeMB DECIMAL = (
                SELECT  SUM(ps.reserved_page_count) / 128
                FROM    sys.tables t
                JOIN    sys.indexes i
                ON      t.object_id = i.object_id
                JOIN    sys.schemas s
                ON      t.schema_id = s.schema_id
                JOIN    sys.dm_db_partition_stats ps
                ON      ps.object_id = t.object_id
                        AND ps.index_id = i.index_id
                WHERE   s.name LIKE 'Analytics%'
                AND     t.name LIKE 'tbl_WorkItem%'
        )

        DECLARE @witModelReady BIT = 0
        IF OBJECT_ID('AnalyticsInternal.tbl_DataQualityResult') IS NOT NULL
        BEGIN
            (SELECT @witModelReady =  ~CAST(MIN(CAST(Failed AS INT)) AS BIT) FROM AnalyticsInternal.tbl_DataQualityResult WHERE PartitionId = @partitionId AND Name LIKE '%ModelReady%' AND Latest=1 AND TargetTable LIKE '%WorkItem%')
        END

        INSERT INTO @results
        VALUES('WIT', @witSizeMB, @axWitSizeMB, @witModelReady,
                (SELECT @witCount AS 'Work items',
                        @wirCount AS 'Work item revisions',
                        @wirMax AS 'Max work item revisions',
                        @wirAverage AS 'Average work item revisions',
                        @wirDev AS 'Stdev work item revisions: ',
                        @witSizeMB  AS 'WIT size, MB'
                FOR JSON PATH, INCLUDE_NULL_VALUES)
        )
    END

    IF (OBJECT_ID('[TestResult].[tbl_TestCaseReference]') IS NOT NULL)
    BEGIN
        -- TCM
        DECLARE @avgTestCaseRefStageRowSizeInKB FLOAT = 0.25
        DECLARE @avgTestCaseRefModelRowSizeInKB FLOAT = 0.4
        DECLARE @testRunCount                    INT = 0
        DECLARE @testResultCount                BIGINT = 0
        DECLARE @avgTestRunDailyCount            INT = 0
        DECLARE @avgTestResultDailyCount        BIGINT = 0
        DECLARE @testRunModelRetentionDays        INT = 180
        DECLARE @testResultModelRetentionDays    INT = 30
        DECLARE @avgTestRunStageRowSizeInKB        FLOAT = 0.33
        DECLARE @avgTestRunModelRowSizeInKB        FLOAT = 0.14
        DECLARE @avgTestResultStageRowSizeInKB    FLOAT = 0.125
        DECLARE @avgTestResultModelRowSizeInKB    FLOAT = 0.05
        DECLARE @avgTestResultDailyModelRowSizeInKB    FLOAT = 0.02
        DECLARE    @lastNDays                        INT = 7        -- Specify how much lookback we need to look for daily distribution.

        --Test Case Reference Size
        DECLARE @testCaseRefCount                INT = (SELECT COUNT(1) FROM [TestResult].[tbl_TestCaseReference] WHERE PartitionId = @partitionId)
        DECLARE @testCaseRefStageTableSizeInKB    FLOAT = @avgTestCaseRefStageRowSizeInKB * @testCaseRefCount
        DECLARE @testCaseRefModelTableSizeInKB    FLOAT = @avgTestCaseRefModelRowSizeInKB * @testCaseRefCount

        --Test Run and Test result Size
        SELECT @testRunCount = COUNT(1), @testResultCount = SUM(CAST(TotalTests AS BIGINT))
        FROM [dbo].[tbl_TestRun]
        WHERE PartitionId = @partitionId
        AND [State] IN (3, 6) -- (Completed, NeedsInvestigation)
        AND [TotalTests] > 0

        DECLARE @testRunStageTableSizeInKB        FLOAT = @testRunCount * @avgTestRunStageRowSizeInKB
        DECLARE @testResultStageTableSizeInKB    FLOAT = @testResultCount * @avgTestResultStageRowSizeInKB

        --Distribution of test runs and test results in last n days
        SELECT @avgTestRunDailyCount = AVG(TestRunDailyCount), @avgTestResultDailyCount = AVG(TestResultDailyCount)
        FROM
        (
            SELECT DATEPART(DAY, LastUpdated) AS DayNumber, COUNT(1) AS TestRunDailyCount, SUM(CAST(TotalTests AS BIGINT)) AS TestResultDailyCount
            FROM [dbo].[tbl_TestRun]
            WHERE PartitionId = @partitionId
            AND [State] IN (3, 6) -- (Completed, NeedsInvestigation)
            AND [TotalTests] > 0
            AND LastUpdated > DATEADD(DAY, -@lastNDays, GETUTCDATE())
            GROUP BY DATEPART(DAY, LastUpdated)
        ) AS DayDistribution

        DECLARE    @testRunModelTableSizeInKB        FLOAT = @avgTestRunDailyCount * @avgTestRunModelRowSizeInKB * @testRunModelRetentionDays
        DECLARE    @testResultModelTableSizeInKB    FLOAT = @avgTestResultDailyCount * @avgTestResultModelRowSizeInKB * @testResultModelRetentionDays

        -- Test Result daily. Size there is one row per test/pipeline/day =~ size of test case ref(each test execute daily) * retention of table.
        DECLARE @testResultDailySizeInKB        FLOAT =  @testCaseRefCount * @avgTestResultDailyModelRowSizeInKB * @testResultModelRetentionDays

        DECLARE @axTcmSizeMB DECIMAL = (
                SELECT  SUM(ps.reserved_page_count) / 128
                FROM    sys.tables t
                JOIN    sys.indexes i
                ON      t.object_id = i.object_id
                JOIN    sys.schemas s
                ON      t.schema_id = s.schema_id
                JOIN    sys.dm_db_partition_stats ps
                ON      ps.object_id = t.object_id
                        AND ps.index_id = i.index_id
                WHERE   s.name LIKE 'Analytics%'
                AND     t.name LIKE 'tbl_Test%'
        )

        DECLARE @tcmStageEstimate INT = (@testCaseRefStageTableSizeInKB
                + (@testRunStageTableSizeInKB + @testResultStageTableSizeInKB)
                )

        DECLARE @tcmModelEstimate INT = ((@testCaseRefModelTableSizeInKB)
                + (@testRunModelTableSizeInKB + @testResultModelTableSizeInKB)
                + (@testResultDailySizeInKB))

        DECLARE @tcmEstimate DECIMAL =     (@tcmStageEstimate + @tcmModelEstimate)/1024

        DECLARE @tcmModelReady BIT = 0
        IF OBJECT_ID('AnalyticsInternal.tbl_DataQualityResult') IS NOT NULL
        BEGIN
            (SELECT @tcmModelReady =  ~CAST(MIN(CAST(Failed AS INT)) AS BIT) FROM AnalyticsInternal.tbl_DataQualityResult WHERE PartitionId = @partitionId AND Name LIKE '%ModelReady%' AND Latest=1 AND TargetTable LIKE '%Test%')
        END

        INSERT INTO @Results
        VALUES ('TCM', @tcmEstimate, @axTcmSizeMB, @tcmModelReady,
                (SELECT
                    @tcmEstimate AS 'Estimate',
                    @tcmStageEstimate/1024  AS 'Stage estimate',
                    @tcmModelEstimate/1024  AS 'Model estimate',
                    @testResultCount AS 'Test results',
                    @testRunCount AS 'Test runs',
                    @testCaseRefCount AS 'Tests',
                    @avgTestRunDailyCount AS 'Test runs per day',
                    @testRunModelRetentionDays AS 'Test run retention',
                    @testResultModelRetentionDays AS 'Test result retention'
                FOR JSON PATH, INCLUDE_NULL_VALUES)

        )
    END

    SELECT * FROM @results
END

GO

