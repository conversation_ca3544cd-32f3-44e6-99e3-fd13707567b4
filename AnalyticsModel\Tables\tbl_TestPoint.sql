CREATE TABLE [AnalyticsModel].[tbl_TestPoint] (
    [PartitionId]                 INT                NOT NULL,
    [AnalyticsCreatedDate]        DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate]        DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]            BIGINT             NOT NULL,
    [TestPointSK]                 INT                IDENTITY (1, 1) NOT NULL,
    [ProjectSK]                   UNIQUEIDENTIFIER   NULL,
    [TestSuiteSK]                 INT                NULL,
    [TestPlanId]                  INT                NULL,
    [TestSuiteId]                 INT                NULL,
    [TestPointId]                 INT                NOT NULL,
    [TestConfigurationSK]         INT                NULL,
    [TestConfigurationId]         INT                NULL,
    [TestCaseId]                  INT                NULL,
    [State]                       TINYINT            NULL,
    [Revision]                    INT                NULL,
    [TesterUserSK]                UNIQUEIDENTIFIER   NULL,
    [AssignedToUserSK]            UNIQUEIDENTIFIER   NULL,
    [Priority]                    INT                NULL,
    [AutomationStatus]            NVARCHAR (256)     NULL,
    [LastResultState]             TINYINT            NULL,
    [LastResultOutcome]           TINYINT            NULL,
    [ChangedDate]                 DATETIMEOFFSET (0) NOT NULL,
    [ChangedDateSK]               INT                NULL,
    [InternalForSnapshotHashJoin] BIT                NOT NULL,
    [IsDeleted]                   BIT                NOT NULL,
    [DataSourceId]                INT                NOT NULL,
    [HasWorkItemProperties]       BIT                DEFAULT ((1)) NOT NULL
) ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_TestPoint_TestPlanId_TestPointId]
    ON [AnalyticsModel].[tbl_TestPoint]([PartitionId] ASC, [TestPlanId] ASC, [TestPointId] ASC, [DataSourceId] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestPoint_TestPointId]
    ON [AnalyticsModel].[tbl_TestPoint]([PartitionId] ASC, [TestPointId] ASC)
    INCLUDE([Revision], [TestPointSK], [TestPlanId], [TestSuiteId], [TestConfigurationId], [IsDeleted], [HasWorkItemProperties], [AssignedToUserSK], [Priority], [AutomationStatus]) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (8))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestPoint_TestCaseId]
    ON [AnalyticsModel].[tbl_TestPoint]([PartitionId] ASC, [TestCaseId] ASC)
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestPoint_AnalyticsBatchId]
    ON [AnalyticsModel].[tbl_TestPoint]([PartitionId] ASC, [AnalyticsBatchId] ASC)
    INCLUDE([TestPointId], [HasWorkItemProperties]) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11), DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_TestPoint]
    ON [AnalyticsModel].[tbl_TestPoint]
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

