CREATE TABLE [AnalyticsModel].[tbl_TeamToTeamField] (
    [PartitionId]          INT              NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7)    NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7)    NOT NULL,
    [AnalyticsBatchId]     BIGINT           NOT NULL,
    [TeamSK]               UNIQUEIDENTIFIER NOT NULL,
    [TeamFieldSK]          INT              NOT NULL,
    [Depth]                INT              NULL
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_TeamToTeamField_TeamFieldSK]
    ON [AnalyticsModel].[tbl_TeamToTeamField]([PartitionId] ASC, [TeamFieldSK] ASC, [TeamSK] ASC);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_TeamToTeamField]
    ON [AnalyticsModel].[tbl_TeamToTeamField]([PartitionId] ASC, [Team<PERSON><PERSON>] ASC, [TeamFieldSK] ASC);


GO

