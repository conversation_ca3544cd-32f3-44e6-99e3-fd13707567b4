CREATE TABLE [AnalyticsModel].[tbl_WorkItemProcess] (
    [PartitionId]          INT      NOT NULL,
    [AnalyticsCreatedDate] DATETIME NOT NULL,
    [AnalyticsUpdatedDate] DATETIME NOT NULL,
    [AnalyticsBatchId]     BIGINT   NOT NULL,
    [WorkItemRevisionSK]   INT      NOT NULL,
    [ProcessSK]            INT      NOT NULL
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_WorkItemProcess_ProcessSK]
    ON [AnalyticsModel].[tbl_WorkItemProcess]([PartitionId] ASC, [ProcessSK] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_WorkItemProcess]
    ON [AnalyticsModel].[tbl_WorkItemProcess]([PartitionId] ASC, [WorkItemRevisionSK] ASC, [ProcessSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

