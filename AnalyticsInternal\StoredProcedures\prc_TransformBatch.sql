/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D65B2809545E51C0E983BF539D1984000690B894
--------------------------------------------------------------------
-- Execute the transformation for the provided batch id
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_TransformBatch
    @partitionId    INT,
    @batchId        BIGINT,
    @settings       typ_KeyValuePairStringTable READONLY
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)
    DECLARE @maxAttemptCount INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = CONCAT('/Service/Analytics/Settings/Transform/', 'TransformNextBatch/MaxAttempts')), 3)

    DECLARE @batchDt DATETIME
    DECLARE @operationSubBatchCount INT
    DECLARE @triggerTableName VARCHAR(64)
    DECLARE @triggerBatchIdStart BIGINT
    DECLARE @triggerBatchIdEnd BIGINT
    DECLARE @priority INT
    DECLARE @attemptCount INT
    DECLARE @targetTableName VARCHAR(64)
    DECLARE @sproc VARCHAR(100)
    DECLARE @state VARCHAR(10)
    DECLARE @stateData BIGINT
    DECLARE @reworkAttemptCount INT
    DECLARE @lastFailedSubBatchCount INT
    DECLARE @subBatchCount INT
    DECLARE @failedCount INT
    DECLARE @tableLoading BIT

    DECLARE @held BIT = 0

    DECLARE @allComplete BIT = 1
    DECLARE @lowPriorityDeferred BIT = 0
    DECLARE @lowPriorityDeferredReason NVARCHAR(256) = NULL
    DECLARE @failedAttempt BIT = 0
    DECLARE @now DATETIME = GETUTCDATE()
    DECLARE @maxLowPriorityBlockHours INT = 72

    ------------------------------------------------------------
    -- Look for batch
    ------------------------------------------------------------
    SELECT TOP 1
        @triggerTableName = b.OperationTriggerTableName,
        @triggerBatchIdStart = b.OperationTriggerBatchIdStart,
        @triggerBatchIdEnd = b.OperationTriggerBatchIdEnd,
        @priority = b.OperationPriority,
        @reworkAttemptCount = ReworkAttemptCount,
        @operationSubBatchCount = b.OperationSubBatchCount,
        @attemptCount = b.AttemptCount,
        @targetTableName = b.TableName,
        @sproc = b.OperationSproc,
        @state = b.OperationState,
        @stateData = b.OperationStateData,
        @batchDt = b.CreateDateTime,
        @held = ISNULL(ts.Hold, 0),
        @lastFailedSubBatchCount = b.LastFailedOperationSubBatchCount,
        @subBatchCount = b.OperationSubBatchCount,
        @failedCount = ISNULL(b.FailedCount, 0),
        @tableLoading = IIF(ts.Loaded = 1, 0, 1)
    FROM AnalyticsInternal.tbl_Batch b WITH (READPAST)
    JOIN AnalyticsInternal.tbl_TransformState ts
        ON b.PartitionId = ts.PartitionId
        AND b.OperationTriggerTableName = ts.TriggerTableName
        AND b.TableName = ts.TargetTableName
        AND b.Operation = ts.TargetOperation
        AND b.OperationSproc = ts.SProcName
    WHERE b.PartitionId = @partitionId
        AND BatchId = @batchId
        AND Ready = 0
        AND Failed = 0
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF (@batchId IS NOT NULL AND @priority <= 1)
    BEGIN
        SET @allComplete = 0

        DECLARE @priorityPartitionId INT
        SELECT TOP 1 @priorityPartitionId = PartitionId
        FROM AnalyticsInternal.tbl_Batch b WITH (NOLOCK)
        JOIN AnalyticsInternal.tbl_TransformDefinition def
            ON b.OperationTriggerTableName = def.TriggerTableName
            AND b.TableName = def.TargetTableName
            AND b.Operation = def.TargetOperation
            AND b.OperationSproc = def.SProcName
        WHERE PartitionId > 0 -- looking cross partition for priority rework batches on the same table
            AND Ready = 0
            AND Failed = 0
            AND OperationTriggerTableName IS NOT NULL
            AND OperationPriority <= 1
            AND TableName = @targetTableName
            AND (
                -- candidates for high-priority blockers must be recent
                DATEDIFF(hour, CreateDateTime, @now) < @maxLowPriorityBlockHours
                -- or self
                OR PartitionId = @partitionId
                )
         -- active batches are highest priority, then be selfish
         ORDER BY OperationActive DESC, IIF(PartitionId = @partitionId, 1, 0) DESC
         OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        IF (@priorityPartitionId <> @partitionId)
        BEGIN
            SET @batchId = NULL
            SET @lowPriorityDeferred = 1
            SET @lowPriorityDeferredReason = CONCAT(N'Concurrent rework limit for table: ', @targetTableName)
        END
    END

    IF (@held = 1)
    BEGIN
        SET @batchId = NULL
    END

    IF (@batchId IS NOT NULL)
    BEGIN
        DECLARE @failed BIT
        DECLARE @insertedCount INT
        DECLARE @updatedCount INT
        DECLARE @deletedCount INT
        DECLARE @operationDurationMS INT
        DECLARE @startDate DATETIME2
        DECLARE @endDate DATETIME2
        DECLARE @durationMS INT
        DECLARE @failedMessage NVARCHAR(1000)
        DECLARE @nextstate VARCHAR(10)
        DECLARE @nextstateData BIGINT
        DECLARE @batchComplete BIT
        DECLARE @sprocSettings typ_KeyValuePairStringTable

        SET @allComplete = 0
        SET @startDate = SYSUTCDATETIME()
        SET @insertedCount = NULL
        SET @updatedCount = NULL
        SET @deletedCount = NULL
        SET @attemptCount = IIF(@reworkAttemptCount > 0 AND ISNULL(@operationSubBatchCount, 0) = 0, @maxAttemptCount + 1, ISNULL(@attemptCount, 0) + 1) -- on rework, initialize the attemptcount to reflect a previous failure
        SET @failedAttempt = 0
        SET @failed = 0
        SET @failedMessage = NULL
        SET @subBatchCount = ISNULL(@subBatchCount, 0) + 1

        -- extract settings in the form of /Service/Analytics/Settings/Transform/[sproc]/[setting]
        INSERT @sprocSettings ([Key], Value)
        SELECT SUBSTRING([Key], LEN(CONCAT('/Service/Analytics/Settings/Transform/', @sproc, '/')) + 1, 10000), Value
        FROM @settings
        WHERE CHARINDEX(CONCAT('/Service/Analytics/Settings/Transform/', @sproc, '/'), [Key]) = 1

        -- extract settings in the form of /Service/Analytics/Settings/Transform/[table_name]/[setting] for target tables
        INSERT @sprocSettings ([Key], Value)
        SELECT CONCAT('Target.', SUBSTRING([Key], LEN(CONCAT('/Service/Analytics/Settings/Transform/', @targetTableName, '/')) + 1, 10000)), Value
        FROM @settings
        WHERE CHARINDEX(CONCAT('/Service/Analytics/Settings/Transform/', @targetTableName, '/'), [Key]) = 1

        -- this is the value that will remain if next transaction is rolled back
        EXEC AnalyticsInternal.prc_iUpdateBatch
        @partitionId,
        @batchId,
        @operationState = NULL,
        @operationStateData = NULL,
        @operationActive = 1,
        @ready = NULL,
        @failed = 0,
        @incFailedCount = 0,
        @failedMessage = NULL,
        @attemptCount = @attemptCount,
        @incInsertedCount = NULL,
        @incUpdatedCount = NULL,
        @incDeletedCount = NULL,
        @incOperationSubBatchCount = 1,
        @incOperationDurationMS = NULL,
        @tableLoading = @tableLoading

        -- There is a small chance another thread could mark this batch as failed and inactive here, before the transaction.
        -- No harm.  We write the same initial state again inside the transaction.

        DECLARE @execSproc VARCHAR(256) =
            CASE @sproc
                WHEN 'StageProject_ModelProject_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageProject_ModelProject_BatchMerge]'
                WHEN 'StageProject_ModelProject_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageProject_ModelProject_TableDelete]'
                WHEN 'Any_InternalWorkItemTypeState_BatchReplace' THEN '[AnalyticsInternal].[prc_iiTrans_Any_InternalWorkItemTypeState_BatchReplace]'
                WHEN 'StageProcess_InternalProcessField_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageProcess_InternalProcessField_BatchMerge]'
                WHEN 'Any_ModelWorkItemTypeField_BatchReplace' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelWorkItemTypeField_BatchReplace]'
                WHEN 'StageWorkItemDestroyed_StageWorkItemRevision_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemDestroyed_StageWorkItemRevision_BatchDelete]'
                WHEN 'StageWorkItemDestroyed_StageWorkItemLink_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemDestroyed_StageWorkItemLink_BatchDelete]'
                WHEN 'StageWorkItemUser_ModelUser_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemUser_ModelUser_BatchMerge]'
                WHEN 'StageWorkItemRevision_InternalWorkItemRevisionKanban_BatchMerge' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_InternalWorkItemRevisionKanban_BatchMerge]'
                WHEN 'StageWorkItemRevision_InternalWorkItemRevisionKanban_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemRevision_InternalWorkItemRevisionKanban_BatchDelete]'
                WHEN 'StageWorkItemRevision_ModelTag_BatchInsert' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelTag_BatchInsert]'
                WHEN 'StageWorkItemRevision_ModelWorkItemTag_BatchMerge' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelWorkItemTag_BatchMerge]'
                WHEN 'StageWorkItemArea_ModelArea_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemArea_ModelArea_BatchMerge]'
                WHEN 'StageWorkItemArea_ModelArea_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemArea_ModelArea_TableDelete]'
                WHEN 'StageWorkItemIteration_ModelIteration_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemIteration_ModelIteration_BatchMerge]'
                WHEN 'StageWorkItemIteration_ModelIteration_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemIteration_ModelIteration_TableDelete]'
                WHEN 'StageTeamSetting_ModelTeam_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTeamSetting_ModelTeam_BatchMerge]'
                WHEN 'StageTeamSetting_ModelTeam_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageTeamSetting_ModelTeam_TableDelete]'
                WHEN 'StageTeamSetting_ModelBoardLocation_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageTeamSetting_ModelBoardLocation_TableDelete]'
                WHEN 'StageTag_ModelTag_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTag_ModelTag_BatchMerge]'
                WHEN 'StageTag_ModelTag_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageTag_ModelTag_TableDelete]'
                WHEN 'ModelTag_ModelWorkItem_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTag_ModelWorkItem_BatchUpdate]'
                WHEN 'StageProject_ModelTag_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageProject_ModelTag_BatchMerge]'
                WHEN 'StageProject_ModelTag_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageProject_ModelTag_TableDelete]'
                WHEN 'ModelWorkItem_ModelWorkItemTag_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelWorkItem_ModelWorkItemTag_BatchDelete]'
                WHEN 'ModelTag_ModelWorkItemTag_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTag_ModelWorkItemTag_TableDelete]'
                WHEN 'Any_ModelBoardLocation_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelBoardLocation_BatchMerge]'
                WHEN 'StageKanbanBoardColumn_ModelBoardLocation_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageKanbanBoardColumn_ModelBoardLocation_TableDelete]'
                WHEN 'StageKanbanBoardRow_ModelBoardLocation_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageKanbanBoardRow_ModelBoardLocation_TableDelete]'
                WHEN 'InternalWorkItemRevisionKanban_ModelWorkItemBoardLocation_BatchMerge' THEN '[AnalyticsInternal].[prc_iTrans_InternalWorkItemRevisionKanban_ModelWorkItemBoardLocation_BatchMerge]'
                WHEN 'InternalWorkItemRevisionKanban_ModelWorkItemBoardLocation_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_InternalWorkItemRevisionKanban_ModelWorkItemBoardLocation_BatchDelete]'
                WHEN 'ModelBoardLocation_ModelWorkItemBoardLocation_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_ModelBoardLocation_ModelWorkItemBoardLocation_BatchMerge]'
                WHEN 'ModelBoardLocation_ModelWorkItemBoardLocation_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelBoardLocation_ModelWorkItemBoardLocation_TableDelete]'
                WHEN 'ModelWorkItem_ModelWorkItemBoardLocation_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelWorkItem_ModelWorkItemBoardLocation_BatchDelete]'
                WHEN 'StageTeamSetting_ModelTeamIteration_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTeamSetting_ModelTeamIteration_BatchMerge]'
                WHEN 'ModelIteration_ModelTeamIteration_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_ModelIteration_ModelTeamIteration_BatchInsert]'
                WHEN 'ModelIteration_ModelTeamIteration_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelIteration_ModelTeamIteration_TableDelete]'
                WHEN 'ModelTeam_ModelTeamIteration_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTeam_ModelTeamIteration_TableDelete]'
                WHEN 'StageTeamSetting_ModelTeamArea_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTeamSetting_ModelTeamArea_BatchMerge]'
                WHEN 'ModelArea_ModelTeamArea_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_ModelArea_ModelTeamArea_BatchMerge]'
                WHEN 'ModelArea_ModelTeamArea_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelArea_ModelTeamArea_TableDelete]'
                WHEN 'ModelTeam_ModelTeamArea_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTeam_ModelTeamArea_TableDelete]'
                WHEN 'StageWorkItemLink_WorkItemLinkHistory_BatchMerge' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemLink_WorkItemLinkHistory_BatchMerge]'
                WHEN 'StageWorkItemLink_WorkItemLinkHistory_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemLink_WorkItemLinkHistory_BatchDelete]'
                WHEN 'StageWorkItemLinkType_WorkItemLinkHistory_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemLinkType_WorkItemLinkHistory_BatchMerge]'
                WHEN 'StageWorkItemLinkType_WorkItemLinkHistory_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemLinkType_WorkItemLinkHistory_TableDelete]'
                WHEN 'ModelWorkItemLinkHistory_ModelWorkItem_BatchUpdate' THEN '[AnalyticsInternal].[prc_iTrans_ModelWorkItemLinkHistory_ModelWorkItem_BatchUpdate]'
                WHEN 'StageCollection_InternalCollectionTimeZone_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageCollection_InternalCollectionTimeZone_BatchMerge]'
                WHEN 'InternalCollectionTimeZone_ModelDate_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelDate_TableUpdate]'
                WHEN 'InternalCollectionTimeZone_ModelIteration_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelIteration_TableUpdate]'
                WHEN 'InternalCollectionTimeZone_ModelWorkItem_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelWorkItem_TableUpdate]'
                WHEN 'InternalCollectionTimeZone_ModelWorkItemLinkHistory_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelWorkItemLinkHistory_TableUpdate]'
                WHEN 'InternalWorkItemTypeState_ModelWorkItem_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalWorkItemTypeState_ModelWorkItem_BatchUpdate]'
                WHEN 'StageWorkItemRevision_ModelWorkItem_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemRevision_ModelWorkItem_BatchDelete]'
                WHEN 'StageTestCaseReference_ModelTest_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestCaseReference_ModelTest_BatchMerge]'
                WHEN 'StageTestRun_ModelBranch_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_ModelBranch_BatchInsert]'
                WHEN 'StageTestRun_ModelRelease_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_ModelRelease_BatchInsert]'
                WHEN 'StageTestRun_ModelReleasePipeline_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_ModelReleasePipeline_BatchInsert]'
                WHEN 'StageTestRun_ModelReleaseEnvironment_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_ModelReleaseEnvironment_BatchInsert]'
                WHEN 'StageTestRun_ModelReleaseStage_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_ModelReleaseStage_BatchInsert]'
                WHEN 'StageTestRun_ModelBuild_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_ModelBuild_BatchInsert]'
                WHEN 'StageTestRun_ModelBuildPipeline_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_ModelBuildPipeline_BatchInsert]'
                WHEN 'StageTestRun_ModelTestRun_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_ModelTestRun_BatchMerge]'
                WHEN 'StageTestResult_ModelTest_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestResult_ModelTest_BatchInsert]'
                WHEN 'ModelTestResult_ModelTestResultDaily_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTestResult_ModelTestResultDaily_BatchMerge]'
                WHEN 'StageTestResult_ModelTestResult_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestResult_ModelTestResult_BatchInsert]'
                WHEN 'ModelTestRun_ModelTestResult_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTestRun_ModelTestResult_BatchInsert]'
                WHEN 'StageTestResult_ModelTestRun_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestResult_ModelTestRun_BatchUpdate]'
                WHEN 'InternalCollectionTimeZone_ModelTestRun_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelTestRun_TableUpdate]'
                WHEN 'InternalCollectionTimeZone_ModelTestResult_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelTestResult_TableUpdate]'
                WHEN 'StageTestRun_StageTestResult_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestRun_StageTestResult_BatchDelete]'
                WHEN 'Any_ModelProcess_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelProcess_BatchMerge]'
                WHEN 'StageTeamSetting_ModelProcess_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageTeamSetting_ModelProcess_TableDelete]'
                WHEN 'ModelWorkItem_ModelWorkItemProcess_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelWorkItem_ModelWorkItemProcess_BatchDelete]'
                WHEN 'ModelProcess_ModelWorkItemProcess_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelProcess_ModelWorkItemProcess_TableDelete]'
                WHEN 'ModelTestRun_ModelRelease_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTestRun_ModelRelease_TableDelete]'
                WHEN 'ModelTestRun_ModelReleaseEnvironment_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTestRun_ModelReleaseEnvironment_TableDelete]'
                WHEN 'Daily_ModelTestResult_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_Daily_ModelTestResult_TableDelete]'
                WHEN 'Daily_ModelTestResultDaily_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_Daily_ModelTestResultDaily_TableDelete]'
                WHEN 'Daily_ModelTestRun_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_Daily_ModelTestRun_TableDelete]'
                WHEN 'Daily_ModelTest_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_Daily_ModelTest_TableDelete]'
                WHEN 'InternalProcessField_ModelWorkItem_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalProcessField_ModelWorkItem_BatchUpdate]'
                WHEN 'Any_ModelTeamField_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelTeamField_BatchMerge]'
                WHEN 'StageWorkItemRevision_ModelTeamField_BatchInsert' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelTeamField_BatchInsert]'
                WHEN 'StageTeamSetting_ModelTeamToTeamField_BatchReplace' THEN '[AnalyticsInternal].[prc_iiTrans_StageTeamSetting_ModelTeamToTeamField_BatchReplace]'
                WHEN 'ModelArea_ModelTeamToTeamField_BatchReplace' THEN '[AnalyticsInternal].[prc_iiTrans_ModelArea_ModelTeamToTeamField_BatchReplace]'
                WHEN 'ModelTeam_ModelTeamToTeamField_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTeam_ModelTeamToTeamField_TableDelete]'
                WHEN 'ModelTeamField_ModelTeamToTeamField_TableDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTeamField_ModelTeamToTeamField_TableDelete]'
                WHEN 'ModelProject_ModelWorkItem_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_ModelProject_ModelWorkItem_TableUpdate]'
                WHEN 'StageWorkItemRevision_ModelWorkItemProcess_BatchReplace_v2' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelWorkItemProcess_BatchReplace_v2]'
                WHEN 'ModelProcess_ModelWorkItemProcess_BatchInsert_v2' THEN '[AnalyticsInternal].[prc_iiTrans_ModelProcess_ModelWorkItemProcess_BatchInsert_v2]'
                WHEN 'ModelTeamToTeamField_ModelWorkItemProcess_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTeamToTeamField_ModelWorkItemProcess_BatchDelete]'
                WHEN 'ModelProject_ModelTeamField_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_ModelProject_ModelTeamField_BatchInsert]'
                WHEN 'ModelProject_ModelWorkItemProcess_BatchReplace' THEN '[AnalyticsInternal].[prc_iiTrans_ModelProject_ModelWorkItemProcess_BatchReplace]'
                WHEN 'StageWorkItemRevision_InternalWorkItemRevisionReserved_BatchInsert' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_InternalWorkItemRevisionReserved_BatchInsert]'
                WHEN 'StageWorkItemRevision_ModelWorkItem_BatchMerge_FromInsert' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelWorkItem_BatchMerge_FromInsert]'
                WHEN 'StageWorkItemRevision_ModelWorkItem_BatchMerge_FromUpdate' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelWorkItem_BatchMerge_FromUpdate]'
                WHEN 'ModelWorkItemLinkHistory_ModelWorkItemDescendant_BatchReplace' THEN '[AnalyticsInternal].[prc_iTrans_ModelWorkItemLinkHistory_ModelWorkItemDescendant_BatchReplace]'
                WHEN 'StageWorkItemRevision_ModelWorkItemDescendant_BatchInsert' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelWorkItemDescendant_BatchInsert]'
                WHEN 'StageBuild_ModelBranch_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageBuild_ModelBranch_BatchInsert]'
                WHEN 'StageWorkItemLink_ModelWorkItemDescendant_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemLink_ModelWorkItemDescendant_BatchDelete]'
                WHEN 'ModelWorkItemLinkHistory_ModelWorkItemDescendantHistory_BatchMerge' THEN '[AnalyticsInternal].[prc_iTrans_ModelWorkItemLinkHistory_ModelWorkItemDescendantHistory_BatchMerge]'
                WHEN 'StageWorkItemRevision_ModelWorkItemDescendantHistory_BatchInsert' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelWorkItemDescendantHistory_BatchInsert]'
                WHEN 'StageWorkItemLink_ModelWorkItemDescendantHistory_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemLink_ModelWorkItemDescendantHistory_BatchDelete]'
                WHEN 'InternalCollectionTimeZone_ModelWorkItemDescendantHistory_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelWorkItemDescendantHistory_TableUpdate]'
                WHEN 'StageWorkItemRevision_ModelWorkItemDescendant_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemRevision_ModelWorkItemDescendant_BatchDelete]'
                WHEN 'StageWorkItemRevision_ModelWorkItemDescendantHistory_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemRevision_ModelWorkItemDescendantHistory_BatchDelete]'
                WHEN 'StageBuildDefinition_ModelBuildPipeline_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageBuildDefinition_ModelBuildPipeline_BatchMerge]'
                WHEN 'StageTaskDefinitionReference_ModelBuildPipelineTask_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTaskDefinitionReference_ModelBuildPipelineTask_BatchInsert]'
                WHEN 'StageBuild_ModelBuildPipeline_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageBuild_ModelBuildPipeline_BatchInsert]'
                WHEN 'StageBuild_ModelBuild_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageBuild_ModelBuild_BatchMerge]'
                WHEN 'StageTaskTimelineRecord_ModelBuildTaskResult_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTaskTimelineRecord_ModelBuildTaskResult_BatchInsert]'
                WHEN 'Any_ModelBuildTaskResult_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelBuildTaskResult_BatchInsert]'
                WHEN 'Any_ModelPipelineJob_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelPipelineJob_BatchInsert]'
                WHEN 'Any_ModelPipelineJob_Stage_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelPipelineJob_Stage_BatchUpdate]'
                WHEN 'Any_ModelPipelineJob_Phase_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelPipelineJob_Phase_BatchUpdate]'
                WHEN 'StageTaskDefinitionReference_ModelBuildTaskResult_BatchInsert' THEN '[AnalyticsInternal].[prc_iiTrans_StageTaskDefinitionReference_ModelBuildTaskResult_BatchInsert]'
                WHEN 'Weekly_InternalWorkitemCompletedTimePredictModel_TableInsert' THEN '[AnalyticsInternal].[prc_iTrans_Weekly_InternalWorkitemCompletedTimePredictModel_TableInsert]'
                WHEN 'InternalCollectionTimeZone_ModelBuild_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelBuild_TableUpdate]'
                WHEN 'InternalCollectionTimeZone_ModelBuildTaskResult_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelBuildTaskResult_TableUpdate]'
                WHEN 'StageWorkItemRevision_ModelWorkItem_BatchMerge_FromInsert_noop' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelWorkItem_BatchMerge_FromInsert_noop]'
                WHEN 'StageWorkItemRevision_ModelWorkItem_BatchMerge_FromUpdate_noop' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelWorkItem_BatchMerge_FromUpdate_noop]'
                WHEN 'StageTestConfiguration_ModelTestConfiguration_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestConfiguration_ModelTestConfiguration_BatchMerge]'
                WHEN 'StageTaskPlan_StageTimeLineRecord_BatchDelete' THEN '[AnalyticsInternal].[prc_iiTrans_StageTaskPlan_StageTimeLineRecord_BatchDelete]'
                WHEN 'StageWorkItemRevision_ModelUser_BatchMerge' THEN '[AnalyticsInternal].[prc_iTrans_StageWorkItemRevision_ModelUser_BatchMerge]'
                WHEN 'StageTestSuite_ModelTestSuite_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestSuite_ModelTestSuite_BatchMerge]'
                WHEN 'StageWorkItemRevision_ModelTestSuite_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemRevision_ModelTestSuite_BatchUpdate]'
                WHEN 'StageTestPlan_ModelTestSuite_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestPlan_ModelTestSuite_BatchUpdate]'
                WHEN 'StageTestPoint_ModelTestPoint_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestPoint_ModelTestPoint_BatchMerge]'
                WHEN 'ModelTestSuite_ModelTestPoint_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTestSuite_ModelTestPoint_BatchUpdate]'
                WHEN 'ModelTestConfiguration_ModelTestPoint_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_ModelTestConfiguration_ModelTestPoint_BatchUpdate]'
                WHEN 'StageWorkItemRevision_ModelTestPoint_BatchUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_StageWorkItemRevision_ModelTestPoint_BatchUpdate]'
                WHEN 'InternalCollectionTimeZone_ModelTestPoint_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelTestPoint_TableUpdate]'
                WHEN 'StageTestPoint_ModelUser_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestPoint_ModelUser_BatchMerge]'
                WHEN 'Any_ModelTestPointHistory_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_Any_ModelTestPointHistory_BatchMerge]'
                WHEN 'InternalCollectionTimeZone_ModelTestPointHistory_TableUpdate' THEN '[AnalyticsInternal].[prc_iiTrans_InternalCollectionTimeZone_ModelTestPointHistory_TableUpdate]'
                WHEN 'StageTestPointHistory_ModelUser_BatchMerge' THEN '[AnalyticsInternal].[prc_iiTrans_StageTestPointHistory_ModelUser_BatchMerge]'
                WHEN 'Fail' THEN '[AnalyticsInternal].[prc_iTrans_Fail]'
                WHEN 'Slow' THEN '[AnalyticsInternal].[prc_iTrans_Slow]'
                ELSE NULL
            END

        IF (@execSproc IS NULL)
        BEGIN
            SET @tfError = dbo.func_GetMessage(1670020); RAISERROR(@tfError, 16, -1, @procedureName, @sproc)
            RETURN 1670020
        END

        IF (CHARINDEX('[prc_iiTrans', @execSproc) > 0)
        BEGIN
            -- if sproc requires transaction management, do it
            BEGIN TRAN

            BEGIN TRY
                SET @startDate = SYSUTCDATETIME()

                EXEC @execSproc
                    @partitionId,
                    @batchId,
                    @startDate,
                    @sprocSettings,
                    @triggerTableName,
                    @triggerBatchIdStart,
                    @triggerBatchIdEnd,
                    @state,
                    @stateData,
                    @attemptCount,
                    @subBatchCount,
                    @lastFailedSubbatchCount,
                    @failedCount,
                    @endState = @nextState OUTPUT,
                    @endStateData = @nextStateData OUTPUT,
                    @complete = @batchComplete OUTPUT,
                    @insertedCount = @insertedCount OUTPUT,
                    @updatedCount = @updatedCount OUTPUT,
                    @deletedCount = @deletedCount OUTPUT
                COMMIT TRAN
            END TRY
            BEGIN CATCH
                SET @failedAttempt = 1
                SET @failedMessage = LEFT(ERROR_MESSAGE(), 1000)
                ROLLBACK TRAN
            END CATCH
        END
        ELSE
        BEGIN
            BEGIN TRY
                SET @startDate = SYSUTCDATETIME()

                EXEC @execSproc
                    @partitionId,
                    @batchId,
                    @startDate,
                    @sprocSettings,
                    @triggerTableName,
                    @triggerBatchIdStart,
                    @triggerBatchIdEnd,
                    @state,
                    @stateData,
                    @attemptCount,
                    @subBatchCount,
                    @lastFailedSubbatchCount,
                    @failedCount,
                    @endState = @nextState OUTPUT,
                    @endStateData = @nextStateData OUTPUT,
                    @complete = @batchComplete OUTPUT,
                    @insertedCount = @insertedCount OUTPUT,
                    @updatedCount = @updatedCount OUTPUT,
                    @deletedCount = @deletedCount OUTPUT
            END TRY
            BEGIN CATCH
                SET @failedAttempt = 1
                SET @failedMessage = LEFT(ERROR_MESSAGE(), 1000)

                IF (XACT_STATE() = -1)
                BEGIN
                    ROLLBACK TRAN
                END
            END CATCH
        END

        SET @endDate = SYSUTCDATETIME()
        SET @durationMS = (SELECT DATEDIFF(millisecond, @startDate, @endDate))
        SET @operationSubBatchCount = ISNULL(@operationSubBatchCount, 0) + 1
        SET @failed = IIF(@failedAttempt = 1 AND ISNULL(@attemptCount, @maxAttemptCount) >= @maxAttemptCount, 1, 0)
        SET @batchComplete = ISNULL(@batchComplete, 0)

        DECLARE @nextAttemptCount INT = IIF(@batchComplete = 0 AND @failedAttempt = 0, 0, @attemptCount) -- clear the attempt count for the next pass (if progress was made)

        EXEC AnalyticsInternal.prc_iUpdateBatch
            @partitionId,
            @batchId,
            @operationState = @nextState,
            @operationStateData = @nextStateData,
            @operationActive = 0,
            @ready = @batchComplete,
            @failed = @failed,
            @incFailedCount = @failedAttempt,
            @failedMessage = @failedMessage,
            @attemptCount = @nextAttemptCount,
            @incInsertedCount = @insertedCount,
            @incUpdatedCount = @updatedCount,
            @incDeletedCount = @deletedCount,
            @incOperationSubBatchCount = NULL,
            @incOperationDurationMS = @durationMS,
            @tableLoading = @tableLoading

    END

    SELECT TOP 1
        @partitionId AS PartitionId,
        @allComplete AS Complete,
        @lowPriorityDeferred AS LowPriorityDeferred,
        @lowPriorityDeferredReason AS LowPriorityDeferredReason,
        @batchId AS BatchId,
        OperationSubBatchCount AS SubBatchCount,
        @attemptCount AS AttemptCount,
        @startDate AS StartDate,
        OperationSproc AS Sproc,
        OperationSprocVersion AS SprocVersion,
        OperationTriggerTableName AS TriggerTableName,
        TableName AS TableName,
        OperationTriggerBatchIdStart AS TriggerBatchIdStart,
        OperationTriggerBatchIdEnd AS TriggerBatchIdEnd,
        @state AS [State],
        @stateData AS StateData,
        @nextState AS EndState,
        @nextStateData AS EndStateData,
        @held AS Held,
        Ready AS Ready,
        Failed AS Failed,
        @failedAttempt AS FailedAttempt,
        @failedMessage AS FailedMessage,
        @insertedCount AS InsertedCount,
        @updatedCount AS UpdatedCount,
        @deletedCount AS DeletedCount,
        @durationMS AS DurationMS,
        InsertedCount AS TotalInsertedCount,
        UpdatedCount AS TotalUpdatedCount,
        DeletedCount AS TotalDeletedCount,
        OperationDurationMS AS TotalDurationMS,
        FailedCount AS TotalFailedCount,
        @reworkAttemptCount AS ReworkAttemptCount,
        @priority AS TransformPriority
    FROM AnalyticsInternal.tbl_Batch b WITH (READPAST)
    WHERE PartitionId = @partitionId
        AND BatchId = @batchId

END

GO

