/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: DE0E4EC86DEBEC1C5BBB37DE87B6AE96D6234E3E
CREATE PROCEDURE AnalyticsInternal.prc_iiSetWatermark
    @partitionId    INT,
    @tableName VARCHAR(64),
    @providerShardId INT,
    @streamId INT,
    @watermark NVARCHAR(255),
    @contentVersion INT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @now DATETIME = GETUTCDATE()

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    UPDATE AnalyticsInternal.tbl_TableProviderShardStream WITH (ROWLOCK)
    SET Watermark = @watermark,
        InitialContentVersion = ISNULL(InitialContentVersion, @contentVersion),
        LatestContentVersion = ISNULL(@contentVersion, LatestContentVersion),
        UpdateTime = @now
    WHERE PartitionId = @partitionId
      AND TableName = @tableName
      AND AnalyticsProviderShardId = @providerShardId
      AND AnalyticsStreamId = @streamId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

END

GO

