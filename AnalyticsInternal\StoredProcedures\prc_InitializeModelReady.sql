/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 9E7F8A4EDB089DC4D9FCC84C8DAD88EB6D1CC945
CREATE PROCEDURE AnalyticsInternal.prc_InitializeModelReady
    @partitionId INT
AS
BEGIN
    DECLARE @results AnalyticsInternal.typ_DataQualityResult3
    DECLARE @now DATETIME = GETUTCDATE();

    ;WITH ModelTableNames AS
    (
        SELECT  DISTINCT TargetTableName
        FROM    AnalyticsInternal.tbl_TransformDefinition d
        WHERE   d.TargetTableName like 'Model.%'
                AND d.FeatureFlagged = 0 -- don't initialize for model tables whose transforms are all featured flagged (assume not rolled out yet)
                AND d.TransformPriority > 3
    )
    INSERT  @results (Scope, TargetTable, RunDate, StartDate, EndDate, ExpectedValue, ActualValue, KpiValue, Failed)
    SELECT  TargetTableName,
            TargetTableName,
            @now,
            @now,
            @now,
            0,
            0,
            -99999998,
            1
    FROM ModelTableNames

    -- Blacklist
    DELETE
    FROM    @results
    WHERE   TargetTable = 'Model.User' -- Irregular trasnformation path with no streams.  Remove once staging from SPS.

    EXEC AnalyticsInternal.prc_iRecordDataQuality
        @partitionId,
        'ModelReady',
        @results

END

GO

