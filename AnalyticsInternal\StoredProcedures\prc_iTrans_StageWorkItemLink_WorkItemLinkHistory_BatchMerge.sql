/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B7CE5B695D7F857A029EAF13DF304647885DB482
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemLink_WorkItemLinkHistory_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @localizedUnknown   NVARCHAR(230)

    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_UNKNOWN',
                                                            @defaultValue = 'Unknown',
                                                            @localizedValue =  @localizedUnknown OUTPUT

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)
    DECLARE @batchSizeLarge INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeLarge'), 150)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    CREATE TABLE #TriggerWorkItemId
    (
         TargetId INT NOT NULL PRIMARY KEY,
         [Count] INT NOT NULL
    )

    IF (@stateData IS NULL AND @triggerBatchIdStart > 1) -- use NCI for small batches - assume first batch is likely small
    BEGIN
        INSERT  #TriggerWorkItemId
        SELECT  TargetId, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) TargetId
            FROM    AnalyticsStage.tbl_WorkItemLink
            WHERE   PartitionId = @partitionId
                    AND TargetId IN
                (
                SELECT  TargetId
                FROM    AnalyticsStage.tbl_WorkItemLink wi WITH (INDEX (IX_tbl_WorkItemLink_AxBatchId))
                WHERE   wi.PartitionId = @partitionId
                        AND wi.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                )
            ORDER BY TargetId
        ) core
        GROUP BY TargetId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN -- use CI for larger batches
        DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)

        INSERT  #TriggerWorkItemId
        SELECT  TargetId, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) TargetId
            FROM    AnalyticsStage.tbl_WorkItemLink
            WHERE   PartitionId = @partitionId
                    AND TargetId IN
                (
                SELECT  TargetId
                FROM    AnalyticsStage.tbl_WorkItemLink wi WITH (INDEX (CI_tbl_WorkItemLink))
                WHERE   wi.PartitionId = @partitionId
                        AND wi.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND wi.TargetId >= @workItemIdStart
                )
            ORDER BY TargetId
        ) core
        GROUP BY TargetId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    SET @endStateData = (SELECT MAX(TargetId) FROM #TriggerWorkItemId)
    DECLARE @triggerCount INT = ISNULL((SELECT SUM([Count]) FROM #TriggerWorkItemId), 0)
    SET @complete = IIF(@triggerCount < @batchSizeMax, 1, 0)

    -- Strategy is to re-insert and de-dupe for all links associated with a targetId.
    -- The reason is that parent-child links that might have been deleted as dupe in the
    -- past may be valid now that related links are changing

    SET @insertedCount = 0
    SET @updatedCount = 0
    SET @deletedCount = 0

    CREATE TABLE #Src
    (
        SourceWorkItemId            INT                 NOT NULL,
        TargetWorkItemId            INT                 NOT NULL,
        -- CreatedBy_PersonGUID        INT                 NULL,
        CreatedDate                 DATETIMEOFFSET      NOT NULL,
        -- DeletedBy_PersonGUID        INT                 NULL,
        DeletedDate                 DATETIMEOFFSET      NOT NULL,
        IsCurrent                   BIT                 NOT NULL,
        Comment                     NVARCHAR(256)       COLLATE DATABASE_DEFAULT  NULL,
        LinkTypeId                  INT                 NOT NULL,
        LinkTypeReferenceName       NVARCHAR(128)       COLLATE DATABASE_DEFAULT NULL,
        LinkTypeName                NVARCHAR(128)       COLLATE DATABASE_DEFAULT NULL,
        LinkTypeIsAcyclic           BIT                 NULL,
        LinkTypeIsDirectional       BIT                 NULL,
        ProjectSK                   UNIQUEIDENTIFIER    NULL,
        PRIMARY KEY CLUSTERED (SourceWorkItemId, TargetWorkItemId, LinkTypeId, CreatedDate)
    )

    -- fwd
    INSERT #Src
    SELECT SourceWorkItemId,
           TargetWorkItemId,
           CreatedDate,
           DeletedDate,
           IsCurrent,
           Comment,
           LinkTypeId,
           LinkTypeReferenceName,
           LinkTypeName,
           LinkTypeIsAcyclic,
           LinkTypeIsDirectional,
           ProjectSK
    FROM
        (
                SELECT  SourceWorkItemId,
                        TargetWorkItemId,
                        CreatedDate AT TIME ZONE @timeZone AS CreatedDate,
                        DeletedDate AT TIME ZONE @timeZone AS DeletedDate,
                        IIF(DeletedDate < '9999-01-01', 0, 1) IsCurrent,
                        Comment,
                        LinkTypeId,
                        LinkTypeReferenceName,
                        LinkTypeName,
                        LinkTypeIsAcyclic,
                        LinkTypeIsDirectional,
                        ProjectSK,
                        ROW_NUMBER() OVER (PARTITION BY SourceWorkItemId, TargetWorkItemId, LinkTypeId, CreatedDate ORDER BY OrderPref, DeletedDate DESC) AS RN
                FROM
                    (
                    SELECT  l.SourceId [SourceWorkItemId],
                            l.TargetId [TargetWorkItemId],
                            ChangedDate [CreatedDate],
                            ISNULL(
                                IIF(l.ProviderVersion = 1,
                                    LEAD(ChangedDate) OVER (PARTITION BY l.PartitionId, l.TargetId, l.SourceId, l.LinkTypeId ORDER BY ChangedDate, IsActive),
                                    l.DeletedDate),
                                '9999-01-01') [DeletedDate],
                            Comment,
                            l.LinkTypeId [LinkTypeId],
                            ISNULL(ReferenceName + '-Forward', 'Unknown') [LinkTypeReferenceName],
                            ISNULL(ForwardName, @localizedUnknown)  [LinkTypeName],
                            IsAcyclic [LinkTypeIsAcyclic],
                            IsDirectional [LinkTypeIsDirectional],
                            l.IsActive,
                            l.ProviderVersion,
                            IIF(l.SourceProjectGuid = l.TargetProjectGuid, 1, IIF(SourceProjectGuid IS NOT NULL, 2, 3)) [OrderPref],
                            SourceProjectGuid [ProjectSK]
                    FROM    AnalyticsStage.tbl_WorkItemLink l
                    LEFT JOIN AnalyticsStage.tbl_WorkItemLinkType AS lt
                    ON      lt.PartitionId = l.PartitionId
                            AND lt.LinkTypeId = l.LinkTypeId
                    WHERE   l.PartitionId = @partitionId
                            AND l.TargetId in (SELECT TargetId FROM #TriggerWorkItemId)
                    ) AS SrcWithDeletedLinks
                WHERE ISNULL(SrcWithDeletedLinks.IsActive, 1) = 1
        ) AS TEMP
        WHERE RN = 1
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- rev
    INSERT  #Src
    SELECT SourceWorkItemId,
           TargetWorkItemId,
           CreatedDate,
           DeletedDate,
           IsCurrent,
           Comment,
           LinkTypeId,
           LinkTypeReferenceName,
           LinkTypeName,
           LinkTypeIsAcyclic,
           LinkTypeIsDirectional,
           ProjectSK
    FROM
        (
                SELECT  SourceWorkItemId,
                        TargetWorkItemId,
                        CreatedDate AT TIME ZONE @timeZone AS CreatedDate,
                        DeletedDate AT TIME ZONE @timeZone AS DeletedDate,
                        IIF(DeletedDate < '9999-01-01', 0, 1) IsCurrent,
                        Comment,
                        LinkTypeId,
                        LinkTypeReferenceName,
                        LinkTypeName,
                        LinkTypeIsAcyclic,
                        LinkTypeIsDirectional,
                        ProjectSK,
                        ROW_NUMBER() OVER (PARTITION BY SourceWorkItemId, TargetWorkItemId, LinkTypeId, CreatedDate ORDER BY OrderPref, DeletedDate DESC) AS RN
                FROM
                    (
                    SELECT  l.TargetId [SourceWorkItemId],
                            l.SourceId [TargetWorkItemId],
                            ChangedDate [CreatedDate],
                            ISNULL(
                                    IIF(l.ProviderVersion = 1,
                                    LEAD(ChangedDate) OVER (PARTITION BY l.PartitionId, l.TargetId, l.SourceId, l.LinkTypeId  ORDER BY ChangedDate, IsActive),
                                    l.DeletedDate),
                            '9999-01-01') [DeletedDate],
                            Comment,
                            -- Use negative value to indicate that the link is reversed.
                            0 - l.LinkTypeId [LinkTypeId],
                            ISNULL(ReferenceName + '-Reverse', 'Unknown') [LinkTypeReferenceName],
                            ISNULL(ReverseName, @localizedUnknown) [LinkTypeName],
                            IsAcyclic [LinkTypeIsAcyclic],
                            IsDirectional [LinkTypeIsDirectional],
                            l.IsActive,
                            l.ProviderVersion,
                            IIF(l.SourceProjectGuid = l.TargetProjectGuid, 1, IIF(SourceProjectGuid IS NOT NULL, 2, 3)) [OrderPref],
                            TargetProjectGuid [ProjectSK]
                    FROM    AnalyticsStage.tbl_WorkItemLink l
                    LEFT JOIN AnalyticsStage.tbl_WorkItemLinkType AS lt
                    ON      lt.PartitionId = l.PartitionId
                            AND lt.LinkTypeId = l.LinkTypeId
                    WHERE   l.PartitionId = @partitionId
                            AND l.TargetId in (SELECT TargetId FROM #TriggerWorkItemId)
                    ) AS SrcWithDeletedLinks
                    WHERE ISNULL(SrcWithDeletedLinks.IsActive, 1) = 1
        ) AS TEMP1
    WHERE RN = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    --Set (ProjectSK) to (source work item's revision's ProjectSK)
    UPDATE  s
    SET     s.ProjectSK = l.ProjectSK
    FROM    #Src s
    CROSS APPLY
    (
        SELECT  TOP 1 System_ProjectGuid AS ProjectSK
        FROM    AnalyticsStage.tbl_WorkItemRevision r
        WHERE   r.PartitionId = @partitionId
                AND r.System_Id = s.SourceWorkItemId
                AND r.System_AuthorizedDate < s.DeletedDate
        ORDER BY System_Rev DESC
    ) l
    WHERE s.ProjectSK = NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #ChangedLinks
    (
        MergeAction             NVARCHAR(10),
        SourceWorkItemId        INT NOT NULL,
        TargetWorkItemId        INT NOT NULL,
        LinkTypeId              INT NOT NULL,
        LinkTypeReferenceName   NVARCHAR(128) COLLATE DATABASE_DEFAULT NULL,
        CreatedDate             DATETIMEOFFSET NOT NULL,
        DeletedDate             DATETIMEOFFSET NOT NULL
    )

    DECLARE @updatingCount INT = (SELECT COUNT(*) FROM #Src)

    BEGIN TRAN

    -- If updating count is small, force loop join (prevent bad query plan), otherwise let the sever decide
    IF (@updatingCount < @batchSizeLarge)
    BEGIN
        MERGE   AnalyticsModel.tbl_WorkItemLinkHistory AS t
        USING   #Src AS s
        ON      (
                    t.PartitionId = @partitionId
                    AND t.SourceWorkItemId = s.SourceWorkItemId
                    AND t.TargetWorkItemId = s.TargetWorkItemId
                    AND t.LinkTypeId = s.LinkTypeId
                    AND t.CreatedDate = s.CreatedDate
                )
        WHEN MATCHED AND NOT EXISTS (
            SELECT  CAST(s.DeletedDate AS DATETIME),
                    CAST(s.CreatedDate AS DATETIME),
                    s.IsCurrent,
                    s.Comment,
                    s.LinkTypeReferenceName,
                    s.LinkTypeName,
                    s.LinkTypeIsAcyclic,
                    s.LinkTypeIsDirectional,
                    s.ProjectSK
            INTERSECT
            SELECT  CAST(t.DeletedDate AS DATETIME),
                    CAST(t.CreatedDate AS DATETIME),
                    t.IsCurrent,
                    t.Comment,
                    t.LinkTypeReferenceName,
                    t.LinkTypeName,
                    t.LinkTypeIsAcyclic,
                    t.LinkTypeIsDirectional,
                    t.ProjectSK
            ) THEN
        UPDATE SET
            t.AnalyticsUpdatedDate = @batchDt
            , t.AnalyticsBatchId = @batchId
            , t.DeletedDate = s.DeletedDate
            , t.CreatedDate = s.CreatedDate
            , t.IsCurrent = s.IsCurrent
            , t.Comment = s.Comment
            , t.LinkTypeReferenceName = s.LinkTypeReferenceName
            , t.LinkTypeName = s.LinkTypeName
            , t.LinkTypeIsAcyclic = s.LinkTypeIsAcyclic
            , t.LinkTypeIsDirectional = s.LinkTypeIsDirectional
            , t.ProjectSK = s.ProjectSK
        WHEN NOT MATCHED BY TARGET THEN
        INSERT (
              PartitionId
            , AnalyticsBatchId
            , AnalyticsCreatedDate
            , AnalyticsUpdatedDate
            , SourceWorkItemId
            , TargetWorkItemId
            , CreatedDate
            , DeletedDate
            , IsCurrent
            , Comment
            , LinkTypeId
            , LinkTypeReferenceName
            , LinkTypeName
            , LinkTypeIsAcyclic
            , LinkTypeIsDirectional
            , ProjectSK
        )
        VALUES (
            @partitionId
            , @batchId
            , @batchDt
            , @batchDt
            , s.SourceWorkItemId
            , s.TargetWorkItemId
            , s.CreatedDate
            , s.DeletedDate
            , s.IsCurrent
            , s.Comment
            , s.LinkTypeId
            , s.LinkTypeReferenceName
            , s.LinkTypeName
            , s.LinkTypeIsAcyclic
            , s.LinkTypeIsDirectional
            , s.ProjectSK
        )
        OUTPUT  $action,
                INSERTED.SourceWorkItemId,
                INSERTED.TargetWorkItemId,
                INSERTED.LinkTypeId,
                INSERTED.LinkTypeReferenceName,
                INSERTED.CreatedDate,
                INSERTED.DeletedDate
        INTO #ChangedLinks
        OPTION (LOOP JOIN);
    END
    ELSE
    BEGIN
        MERGE   AnalyticsModel.tbl_WorkItemLinkHistory AS t
        USING   #Src AS s
        ON      (
                    t.PartitionId = @partitionId
                    AND t.SourceWorkItemId = s.SourceWorkItemId
                    AND t.TargetWorkItemId = s.TargetWorkItemId
                    AND t.LinkTypeId = s.LinkTypeId
                    AND t.CreatedDate = s.CreatedDate
                )
        WHEN MATCHED AND NOT EXISTS (
            SELECT  CAST(s.DeletedDate AS DATETIME),
                    CAST(s.CreatedDate AS DATETIME),
                    s.IsCurrent,
                    s.Comment,
                    s.LinkTypeReferenceName,
                    s.LinkTypeName,
                    s.LinkTypeIsAcyclic,
                    s.LinkTypeIsDirectional,
                    s.ProjectSK
            INTERSECT
            SELECT  CAST(t.DeletedDate AS DATETIME),
                    CAST(t.CreatedDate AS DATETIME),
                    t.IsCurrent,
                    t.Comment,
                    t.LinkTypeReferenceName,
                    t.LinkTypeName,
                    t.LinkTypeIsAcyclic,
                    t.LinkTypeIsDirectional,
                    t.ProjectSK
            ) THEN
        UPDATE SET
            t.AnalyticsUpdatedDate = @batchDt
            , t.AnalyticsBatchId = @batchId
            , t.DeletedDate = s.DeletedDate
            , t.CreatedDate = s.CreatedDate
            , t.IsCurrent = s.IsCurrent
            , t.Comment = s.Comment
            , t.LinkTypeReferenceName = s.LinkTypeReferenceName
            , t.LinkTypeName = s.LinkTypeName
            , t.LinkTypeIsAcyclic = s.LinkTypeIsAcyclic
            , t.LinkTypeIsDirectional = s.LinkTypeIsDirectional
            , t.ProjectSK = s.ProjectSK
        WHEN NOT MATCHED BY TARGET THEN
        INSERT (
              PartitionId
            , AnalyticsBatchId
            , AnalyticsCreatedDate
            , AnalyticsUpdatedDate
            , SourceWorkItemId
            , TargetWorkItemId
            , CreatedDate
            , DeletedDate
            , IsCurrent
            , Comment
            , LinkTypeId
            , LinkTypeReferenceName
            , LinkTypeName
            , LinkTypeIsAcyclic
            , LinkTypeIsDirectional
            , ProjectSK
        )
        VALUES (
            @partitionId
            , @batchId
            , @batchDt
            , @batchDt
            , s.SourceWorkItemId
            , s.TargetWorkItemId
            , s.CreatedDate
            , s.DeletedDate
            , s.IsCurrent
            , s.Comment
            , s.LinkTypeId
            , s.LinkTypeReferenceName
            , s.LinkTypeName
            , s.LinkTypeIsAcyclic
            , s.LinkTypeIsDirectional
            , s.ProjectSK
        )
        OUTPUT  $action,
                INSERTED.SourceWorkItemId,
                INSERTED.TargetWorkItemId,
                INSERTED.LinkTypeId,
                INSERTED.LinkTypeReferenceName,
                INSERTED.CreatedDate,
                INSERTED.DeletedDate
        INTO #ChangedLinks;
    END

    SET @insertedCount += (SELECT COUNT(*) FROM #ChangedLinks WHERE MergeAction = 'INSERT')
    SET @updatedCount += (SELECT COUNT(*) FROM #ChangedLinks WHERE MergeAction = 'UPDATE')
    SET @deletedCount += (SELECT COUNT(*) FROM #ChangedLinks WHERE MergeAction = 'DELETE')

    -- dedupeFwd
    CREATE TABLE #DupeFwdLinks
    (
        SourceWorkItemId    INT NOT NULL,
        TargetWorkItemId    INT NOT NULL,
        LinkTypeId          INT NOT NULL,
        CreatedDate         DATETIMEOFFSET NOT NULL
    )

    INSERT  #DupeFwdLinks
    SELECT  l.SourceWorkItemId,
            l.TargetWorkItemId,
            l.LinkTypeId,
            l.CreatedDate
    FROM    #ChangedLinks l
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemLinkHistory lp
    ON      lp.PartitionId = @partitionId
            AND lp.TargetWorkItemId = l.TargetWorkItemId
            AND lp.SourceWorkItemId != l.SourceWorkItemId
            AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
            AND lp.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
            AND lp.CreatedDate < l.DeletedDate
            AND lp.DeletedDate > l.CreatedDate
            AND (
                (lp.DeletedDate > l.DeletedDate)
                OR
                (lp.DeletedDate = l.DeletedDate AND lp.CreatedDate > l.CreatedDate)
                OR
                (lp.DeletedDate = l.DeletedDate AND lp.CreatedDate = l.CreatedDate AND lp.SourceWorkItemId > l.SourceWorkItemId)
                )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  #DupeFwdLinks
    SELECT  l.SourceWorkItemId,
            l.TargetWorkItemId,
            l.LinkTypeId,
            l.CreatedDate
    FROM    #ChangedLinks lp
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemLinkHistory l
    ON      l.PartitionId = @partitionId
            AND lp.TargetWorkItemId = l.TargetWorkItemId
            AND lp.SourceWorkItemId != l.SourceWorkItemId
            AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
            AND lp.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
            AND lp.CreatedDate < l.DeletedDate
            AND lp.DeletedDate > l.CreatedDate
            AND (
                (lp.DeletedDate > l.DeletedDate)
                OR
                (lp.DeletedDate = l.DeletedDate AND lp.CreatedDate > l.CreatedDate)
                OR
                (lp.DeletedDate = l.DeletedDate AND lp.CreatedDate = l.CreatedDate AND lp.SourceWorkItemId > l.SourceWorkItemId)
                )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF EXISTS (SELECT * FROM #DupeFwdLinks)
    BEGIN
        DELETE l
        FROM #DupeFwdLinks d
        INNER LOOP JOIN AnalyticsModel.tbl_WorkItemLinkHistory l
        ON l.PartitionId = @partitionId
            AND l.SourceWorkItemId = d.SourceWorkItemId
            AND l.TargetWorkItemId = d.TargetWorkItemId
            AND l.LinkTypeId = d.LinkTypeId
            AND l.CreatedDate = d.CreatedDate
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @deletedCount += @@ROWCOUNT
    END

    -- dedupeRev
    CREATE TABLE #DupeRevLinks
    (
        SourceWorkItemId    INT NOT NULL,
        TargetWorkItemId    INT NOT NULL,
        LinkTypeId          INT NOT NULL,
        CreatedDate         DATETIMEOFFSET NOT NULL
    )

    INSERT  #DupeRevLinks
    SELECT  l.SourceWorkItemId,
            l.TargetWorkItemId,
            l.LinkTypeId,
            l.CreatedDate
    FROM    #ChangedLinks l
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemLinkHistory lp
    ON      lp.PartitionId = @partitionId
            AND lp.SourceWorkItemId = l.SourceWorkItemId
            AND lp.TargetWorkItemId != l.TargetWorkItemId
            AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Reverse'
            AND lp.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Reverse'
            AND lp.CreatedDate < l.DeletedDate
            AND lp.DeletedDate > l.CreatedDate
            AND (
                (lp.DeletedDate > l.DeletedDate)
                OR
                (lp.DeletedDate = l.DeletedDate AND lp.CreatedDate > l.CreatedDate)
                OR
                (lp.DeletedDate = l.DeletedDate AND lp.CreatedDate = l.CreatedDate AND lp.TargetWorkItemId > l.TargetWorkItemId)
                )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  #DupeRevLinks
    SELECT  l.SourceWorkItemId,
            l.TargetWorkItemId,
            l.LinkTypeId,
            l.CreatedDate
    FROM    #ChangedLinks lp
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemLinkHistory l
    ON      l.PartitionId = @partitionId
            AND lp.SourceWorkItemId = l.SourceWorkItemId
            AND lp.TargetWorkItemId != l.TargetWorkItemId
            AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Reverse'
            AND lp.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Reverse'
            AND lp.CreatedDate < l.DeletedDate
            AND lp.DeletedDate > l.CreatedDate
            AND (
                (lp.DeletedDate > l.DeletedDate)
                OR
                (lp.DeletedDate = l.DeletedDate AND lp.CreatedDate > l.CreatedDate)
                OR
                (lp.DeletedDate = l.DeletedDate AND lp.CreatedDate = l.CreatedDate AND lp.TargetWorkItemId > l.TargetWorkItemId)
                )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF EXISTS (SELECT * FROM #DupeRevLinks)
    BEGIN
        DELETE l
        FROM #DupeRevLinks d
        INNER LOOP JOIN AnalyticsModel.tbl_WorkItemLinkHistory l
        ON l.PartitionId = @partitionId
            AND l.SourceWorkItemId = d.SourceWorkItemId
            AND l.TargetWorkItemId = d.TargetWorkItemId
            AND l.LinkTypeId = d.LinkTypeId
            AND l.CreatedDate = d.CreatedDate
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @deletedCount += @@ROWCOUNT
    END

    COMMIT TRAN

    RETURN 0
END

GO

