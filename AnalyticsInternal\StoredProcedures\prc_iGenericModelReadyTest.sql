/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 3A6B66D7C117167E76BD443BC1380AE748EAB2A7
CREATE PROCEDURE AnalyticsInternal.prc_iGenericModelReadyTest
    @partitionId INT,
    @name VARCHAR(256),
    @modelTable VARCHAR(64),
    @runDate DATETIME OUTPUT,
    @startDate DATETIME OUTPUT,
    @endDate DATETIME OUTPUT,
    @expectedValue BIGINT OUTPUT,
    @actualValue BIGINT OUTPUT,
    @kpiValue FLOAT OUTPUT,
    @failed BIT OUTPUT
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE();
    DECLARE @numBehind INT

    -- Get first run date
    DECLARE @firstRun DATETIME

    SELECT  @firstRun = ISNULL(MIN(RunDate), @now)
    FROM    AnalyticsInternal.tbl_DataQualityResult d
    WHERE   d.PartitionId = @partitionId
    AND     d.Name = @name

    -- Ensure the transform state table is current
    EXEC AnalyticsInternal.prc_iEnsureTransformState @partitionId, @definitionsOnly = 1

    -- Build transform state of just the dependent transforms
    CREATE TABLE #DependentTransformState
    (
        PartitionId             INT                                         NOT NULL,
        TriggerTableName        VARCHAR(64)     COLLATE DATABASE_DEFAULT    NOT NULL,
        TriggerOperation        VARCHAR(10)     COLLATE DATABASE_DEFAULT    NOT NULL,
        TargetTableName         VARCHAR(64)     COLLATE DATABASE_DEFAULT    NOT NULL,
        TargetOperation         VARCHAR(10)     COLLATE DATABASE_DEFAULT    NOT NULL,
        SProcName               VARCHAR(256)    COLLATE DATABASE_DEFAULT    NOT NULL,
        Loaded                  BIT                                         NOT NULL,
    )

    ; WITH cte AS (
        -- state of transforms directly writing to the model table
        SELECT  d.PartitionId,
                d.TriggerTableName,
                d.TriggerOperation,
                d.TargetTableName,
                d.TargetOperation,
                d.SProcName,
                d.Loaded
        FROM    AnalyticsInternal.tbl_TransformState d
        WHERE   d.TargetTableName = @modelTable
                AND d.PartitionId = @partitionId
                AND d.TriggerTableName != ''

        UNION ALL

        -- state of dependent transforms (since transforms are optimistically marked loaded and set unloaded as discovered)
        SELECT  d.PartitionId,
                d.TriggerTableName,
                d.TriggerOperation,
                d.TargetTableName,
                d.TargetOperation,
                d.SProcName,
                d.Loaded
        FROM    cte
        JOIN    AnalyticsInternal.tbl_TransformState d
        ON      d.TargetTableName = cte.TriggerTableName
                AND d.PartitionId = cte.PartitionId
                AND d.TriggerTableName != ''
    )
    INSERT INTO #DependentTransformState
    SELECT  PartitionId,
            TriggerTableName,
            TriggerOperation,
            TargetTableName,
            TargetOperation,
            SProcName,
            Loaded
    FROM    cte

    -- Build list of dependent stage tables
    CREATE TABLE #DependentStageTables
    (
        StageTableName        VARCHAR(64)     COLLATE DATABASE_DEFAULT    NOT NULL,
    )

    INSERT INTO #DependentStageTables
    SELECT DISTINCT
            s.TriggerTableName
    FROM    #DependentTransformState s
    JOIN    AnalyticsInternal.tbl_TransformDefinition d -- Should be at least one dependent transform that isen't behind a feature flag
    ON      s.TriggerTableName = d.TriggerTableName
            AND s.TriggerOperation = d.TriggerOperation
            AND s.TargetTableName = d.TargetTableName
            AND s.TargetOperation = d.TargetOperation
            AND s.SProcName = d.SProcName
    WHERE   s.SProcName like 'Stage%'
            AND d.FeatureFlagged = 0

    -- Each dependent stage table must have at least one stream
    IF EXISTS
    (
        SELECT      *
        FROM        #DependentStageTables d
        LEFT JOIN   (
            SELECT  *
            FROM    AnalyticsInternal.tbl_TableProviderShardStream
            WHERE   PartitionId = @partitionId
        ) s
        ON          s.TableName = d.StageTableName
        WHERE       s.TableName IS NULL
    )
    BEGIN
        SET @kpiValue = -99999998
        SET @failed = 1
    END
    -- Each dependnet stage table must have at least one stream per shard that is loaded
    ELSE IF EXISTS
    (
        SELECT      s.TableName, s.AnalyticsProviderShardId, MAX(LoadedTime)
        FROM        AnalyticsInternal.tbl_TableProviderShardStream s
        JOIN        #DependentStageTables d
        ON          s.TableName = d.StageTableName
        WHERE       s.PartitionId = @partitionId
        GROUP BY    s.TableName, s.AnalyticsProviderShardId
        HAVING      MAX(LoadedTime) IS NULL
    )
    BEGIN
        SET @kpiValue = -99999999
        SET @failed = 1
    END
    ELSE
    BEGIN
        -- check if dependent transforms are loaded
        SELECT  @numBehind = COUNT(*)
        FROM    #DependentTransformState s
        JOIN    AnalyticsInternal.tbl_TransformDefinition d -- Ignore feature flagged transforms since they may not run
        ON      s.TriggerTableName = d.TriggerTableName
                AND s.TriggerOperation = d.TriggerOperation
                AND s.TargetTableName = d.TargetTableName
                AND s.TargetOperation = d.TargetOperation
                AND s.SProcName = d.SProcName
        WHERE   s.Loaded = 0
                AND d.FeatureFlagged = 0
                AND d.TransformPriority > 3 -- Don't wait for low priority transforms (Deletes, Time zone corrections, etc)

        SET @kpiValue = @numBehind
        SET @failed = CASE WHEN @numBehind > 0 THEN 1 ELSE 0 END
    END

    SET @runDate = @now
    SET @startDate = '1900-01-01'
    SET @endDate = @now
    SET @expectedValue = 0
    SET @actualValue = 0

    -- No streams and expiration interval surpassed, expire this model ready test
    IF (@kpiValue = -99999998 AND DATEDIFF(MINUTE, @firstRun, @now) > 30)
    BEGIN
        SET @kpiValue = -99999997
        SET @failed = 0
    END

    DROP TABLE #DependentTransformState
    DROP TABLE #DependentStageTables

    RETURN 0
END

GO

