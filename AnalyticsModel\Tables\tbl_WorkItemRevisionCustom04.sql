CREATE TABLE [AnalyticsModel].[tbl_WorkItemRevisionCustom04] (
    [PartitionId]          INT                NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]     BIGINT             NOT NULL,
    [WorkItemId]           INT                NOT NULL,
    [Revision]             INT                NOT NULL,
    [WorkItemRevisionSK]   INT                NOT NULL,
    [String0601]           NVARCHAR (256)     NULL,
    [String0602]           NVARCHAR (256)     NULL,
    [String0603]           NVARCHAR (256)     NULL,
    [String0604]           NVARCHAR (256)     NULL,
    [String0605]           NVARCHAR (256)     NULL,
    [String0606]           NVARCHAR (256)     NULL,
    [String0607]           NVARCHAR (256)     NULL,
    [String0608]           NVARCHAR (256)     NULL,
    [String0609]           NVARCHAR (256)     NULL,
    [String0610]           NVARCHAR (256)     NULL,
    [String0611]           NVARCHAR (256)     NULL,
    [String0612]           NVARCHAR (256)     NULL,
    [String0613]           NVARCHAR (256)     NULL,
    [String0614]           NVARCHAR (256)     NULL,
    [String0615]           NVARCHAR (256)     NULL,
    [String0616]           NVARCHAR (256)     NULL,
    [String0617]           NVARCHAR (256)     NULL,
    [String0618]           NVARCHAR (256)     NULL,
    [String0619]           NVARCHAR (256)     NULL,
    [String0620]           NVARCHAR (256)     NULL,
    [String0621]           NVARCHAR (256)     NULL,
    [String0622]           NVARCHAR (256)     NULL,
    [String0623]           NVARCHAR (256)     NULL,
    [String0624]           NVARCHAR (256)     NULL,
    [String0625]           NVARCHAR (256)     NULL,
    [String0626]           NVARCHAR (256)     NULL,
    [String0627]           NVARCHAR (256)     NULL,
    [String0628]           NVARCHAR (256)     NULL,
    [String0629]           NVARCHAR (256)     NULL,
    [String0630]           NVARCHAR (256)     NULL,
    [String0631]           NVARCHAR (256)     NULL,
    [String0632]           NVARCHAR (256)     NULL,
    [String0633]           NVARCHAR (256)     NULL,
    [String0634]           NVARCHAR (256)     NULL,
    [String0635]           NVARCHAR (256)     NULL,
    [String0636]           NVARCHAR (256)     NULL,
    [String0637]           NVARCHAR (256)     NULL,
    [String0638]           NVARCHAR (256)     NULL,
    [String0639]           NVARCHAR (256)     NULL,
    [String0640]           NVARCHAR (256)     NULL,
    [String0641]           NVARCHAR (256)     NULL,
    [String0642]           NVARCHAR (256)     NULL,
    [String0643]           NVARCHAR (256)     NULL,
    [String0644]           NVARCHAR (256)     NULL,
    [String0645]           NVARCHAR (256)     NULL,
    [String0646]           NVARCHAR (256)     NULL,
    [String0647]           NVARCHAR (256)     NULL,
    [String0648]           NVARCHAR (256)     NULL,
    [String0649]           NVARCHAR (256)     NULL,
    [String0650]           NVARCHAR (256)     NULL,
    [String0651]           NVARCHAR (256)     NULL,
    [String0652]           NVARCHAR (256)     NULL,
    [String0653]           NVARCHAR (256)     NULL,
    [String0654]           NVARCHAR (256)     NULL,
    [String0655]           NVARCHAR (256)     NULL,
    [String0656]           NVARCHAR (256)     NULL,
    [String0657]           NVARCHAR (256)     NULL,
    [String0658]           NVARCHAR (256)     NULL,
    [String0659]           NVARCHAR (256)     NULL,
    [String0660]           NVARCHAR (256)     NULL,
    [String0661]           NVARCHAR (256)     NULL,
    [String0662]           NVARCHAR (256)     NULL,
    [String0663]           NVARCHAR (256)     NULL,
    [String0664]           NVARCHAR (256)     NULL,
    [String0665]           NVARCHAR (256)     NULL,
    [String0666]           NVARCHAR (256)     NULL,
    [String0667]           NVARCHAR (256)     NULL,
    [String0668]           NVARCHAR (256)     NULL,
    [String0669]           NVARCHAR (256)     NULL,
    [String0670]           NVARCHAR (256)     NULL,
    [String0671]           NVARCHAR (256)     NULL,
    [String0672]           NVARCHAR (256)     NULL,
    [String0673]           NVARCHAR (256)     NULL,
    [String0674]           NVARCHAR (256)     NULL,
    [String0675]           NVARCHAR (256)     NULL,
    [String0676]           NVARCHAR (256)     NULL,
    [String0677]           NVARCHAR (256)     NULL,
    [String0678]           NVARCHAR (256)     NULL,
    [String0679]           NVARCHAR (256)     NULL,
    [String0680]           NVARCHAR (256)     NULL,
    [String0681]           NVARCHAR (256)     NULL,
    [String0682]           NVARCHAR (256)     NULL,
    [String0683]           NVARCHAR (256)     NULL,
    [String0684]           NVARCHAR (256)     NULL,
    [String0685]           NVARCHAR (256)     NULL,
    [String0686]           NVARCHAR (256)     NULL,
    [String0687]           NVARCHAR (256)     NULL,
    [String0688]           NVARCHAR (256)     NULL,
    [String0689]           NVARCHAR (256)     NULL,
    [String0690]           NVARCHAR (256)     NULL,
    [String0691]           NVARCHAR (256)     NULL,
    [String0692]           NVARCHAR (256)     NULL,
    [String0693]           NVARCHAR (256)     NULL,
    [String0694]           NVARCHAR (256)     NULL,
    [String0695]           NVARCHAR (256)     NULL,
    [String0696]           NVARCHAR (256)     NULL,
    [String0697]           NVARCHAR (256)     NULL,
    [String0698]           NVARCHAR (256)     NULL,
    [String0699]           NVARCHAR (256)     NULL,
    [String0700]           NVARCHAR (256)     NULL,
    [String0701]           NVARCHAR (256)     NULL,
    [String0702]           NVARCHAR (256)     NULL,
    [String0703]           NVARCHAR (256)     NULL,
    [String0704]           NVARCHAR (256)     NULL,
    [String0705]           NVARCHAR (256)     NULL,
    [String0706]           NVARCHAR (256)     NULL,
    [String0707]           NVARCHAR (256)     NULL,
    [String0708]           NVARCHAR (256)     NULL,
    [String0709]           NVARCHAR (256)     NULL,
    [String0710]           NVARCHAR (256)     NULL,
    [String0711]           NVARCHAR (256)     NULL,
    [String0712]           NVARCHAR (256)     NULL,
    [String0713]           NVARCHAR (256)     NULL,
    [String0714]           NVARCHAR (256)     NULL,
    [String0715]           NVARCHAR (256)     NULL,
    [String0716]           NVARCHAR (256)     NULL,
    [String0717]           NVARCHAR (256)     NULL,
    [String0718]           NVARCHAR (256)     NULL,
    [String0719]           NVARCHAR (256)     NULL,
    [String0720]           NVARCHAR (256)     NULL,
    [String0721]           NVARCHAR (256)     NULL,
    [String0722]           NVARCHAR (256)     NULL,
    [String0723]           NVARCHAR (256)     NULL,
    [String0724]           NVARCHAR (256)     NULL,
    [String0725]           NVARCHAR (256)     NULL,
    [String0726]           NVARCHAR (256)     NULL,
    [String0727]           NVARCHAR (256)     NULL,
    [String0728]           NVARCHAR (256)     NULL,
    [String0729]           NVARCHAR (256)     NULL,
    [String0730]           NVARCHAR (256)     NULL,
    [String0731]           NVARCHAR (256)     NULL,
    [String0732]           NVARCHAR (256)     NULL,
    [String0733]           NVARCHAR (256)     NULL,
    [String0734]           NVARCHAR (256)     NULL,
    [String0735]           NVARCHAR (256)     NULL,
    [String0736]           NVARCHAR (256)     NULL,
    [String0737]           NVARCHAR (256)     NULL,
    [String0738]           NVARCHAR (256)     NULL,
    [String0739]           NVARCHAR (256)     NULL,
    [String0740]           NVARCHAR (256)     NULL,
    [String0741]           NVARCHAR (256)     NULL,
    [String0742]           NVARCHAR (256)     NULL,
    [String0743]           NVARCHAR (256)     NULL,
    [String0744]           NVARCHAR (256)     NULL,
    [String0745]           NVARCHAR (256)     NULL,
    [String0746]           NVARCHAR (256)     NULL,
    [String0747]           NVARCHAR (256)     NULL,
    [String0748]           NVARCHAR (256)     NULL,
    [String0749]           NVARCHAR (256)     NULL,
    [String0750]           NVARCHAR (256)     NULL,
    [String0751]           NVARCHAR (256)     NULL,
    [String0752]           NVARCHAR (256)     NULL,
    [String0753]           NVARCHAR (256)     NULL,
    [String0754]           NVARCHAR (256)     NULL,
    [String0755]           NVARCHAR (256)     NULL,
    [String0756]           NVARCHAR (256)     NULL,
    [String0757]           NVARCHAR (256)     NULL,
    [String0758]           NVARCHAR (256)     NULL,
    [String0759]           NVARCHAR (256)     NULL,
    [String0760]           NVARCHAR (256)     NULL,
    [String0761]           NVARCHAR (256)     NULL,
    [String0762]           NVARCHAR (256)     NULL,
    [String0763]           NVARCHAR (256)     NULL,
    [String0764]           NVARCHAR (256)     NULL,
    [String0765]           NVARCHAR (256)     NULL,
    [String0766]           NVARCHAR (256)     NULL,
    [String0767]           NVARCHAR (256)     NULL,
    [String0768]           NVARCHAR (256)     NULL,
    [String0769]           NVARCHAR (256)     NULL,
    [String0770]           NVARCHAR (256)     NULL,
    [String0771]           NVARCHAR (256)     NULL,
    [String0772]           NVARCHAR (256)     NULL,
    [String0773]           NVARCHAR (256)     NULL,
    [String0774]           NVARCHAR (256)     NULL,
    [String0775]           NVARCHAR (256)     NULL,
    [String0776]           NVARCHAR (256)     NULL,
    [String0777]           NVARCHAR (256)     NULL,
    [String0778]           NVARCHAR (256)     NULL,
    [String0779]           NVARCHAR (256)     NULL,
    [String0780]           NVARCHAR (256)     NULL,
    [String0781]           NVARCHAR (256)     NULL,
    [String0782]           NVARCHAR (256)     NULL,
    [String0783]           NVARCHAR (256)     NULL,
    [String0784]           NVARCHAR (256)     NULL,
    [String0785]           NVARCHAR (256)     NULL,
    [String0786]           NVARCHAR (256)     NULL,
    [String0787]           NVARCHAR (256)     NULL,
    [String0788]           NVARCHAR (256)     NULL,
    [String0789]           NVARCHAR (256)     NULL,
    [String0790]           NVARCHAR (256)     NULL,
    [String0791]           NVARCHAR (256)     NULL,
    [String0792]           NVARCHAR (256)     NULL,
    [String0793]           NVARCHAR (256)     NULL,
    [String0794]           NVARCHAR (256)     NULL,
    [String0795]           NVARCHAR (256)     NULL,
    [String0796]           NVARCHAR (256)     NULL,
    [String0797]           NVARCHAR (256)     NULL,
    [String0798]           NVARCHAR (256)     NULL,
    [String0799]           NVARCHAR (256)     NULL,
    [String0800]           NVARCHAR (256)     NULL,
    [Integer0151]          BIGINT             NULL,
    [Integer0152]          BIGINT             NULL,
    [Integer0153]          BIGINT             NULL,
    [Integer0154]          BIGINT             NULL,
    [Integer0155]          BIGINT             NULL,
    [Integer0156]          BIGINT             NULL,
    [Integer0157]          BIGINT             NULL,
    [Integer0158]          BIGINT             NULL,
    [Integer0159]          BIGINT             NULL,
    [Integer0160]          BIGINT             NULL,
    [Integer0161]          BIGINT             NULL,
    [Integer0162]          BIGINT             NULL,
    [Integer0163]          BIGINT             NULL,
    [Integer0164]          BIGINT             NULL,
    [Integer0165]          BIGINT             NULL,
    [Integer0166]          BIGINT             NULL,
    [Integer0167]          BIGINT             NULL,
    [Integer0168]          BIGINT             NULL,
    [Integer0169]          BIGINT             NULL,
    [Integer0170]          BIGINT             NULL,
    [Integer0171]          BIGINT             NULL,
    [Integer0172]          BIGINT             NULL,
    [Integer0173]          BIGINT             NULL,
    [Integer0174]          BIGINT             NULL,
    [Integer0175]          BIGINT             NULL,
    [Integer0176]          BIGINT             NULL,
    [Integer0177]          BIGINT             NULL,
    [Integer0178]          BIGINT             NULL,
    [Integer0179]          BIGINT             NULL,
    [Integer0180]          BIGINT             NULL,
    [Integer0181]          BIGINT             NULL,
    [Integer0182]          BIGINT             NULL,
    [Integer0183]          BIGINT             NULL,
    [Integer0184]          BIGINT             NULL,
    [Integer0185]          BIGINT             NULL,
    [Integer0186]          BIGINT             NULL,
    [Integer0187]          BIGINT             NULL,
    [Integer0188]          BIGINT             NULL,
    [Integer0189]          BIGINT             NULL,
    [Integer0190]          BIGINT             NULL,
    [Integer0191]          BIGINT             NULL,
    [Integer0192]          BIGINT             NULL,
    [Integer0193]          BIGINT             NULL,
    [Integer0194]          BIGINT             NULL,
    [Integer0195]          BIGINT             NULL,
    [Integer0196]          BIGINT             NULL,
    [Integer0197]          BIGINT             NULL,
    [Integer0198]          BIGINT             NULL,
    [Integer0199]          BIGINT             NULL,
    [Integer0200]          BIGINT             NULL,
    [Double0151]           FLOAT (53)         NULL,
    [Double0152]           FLOAT (53)         NULL,
    [Double0153]           FLOAT (53)         NULL,
    [Double0154]           FLOAT (53)         NULL,
    [Double0155]           FLOAT (53)         NULL,
    [Double0156]           FLOAT (53)         NULL,
    [Double0157]           FLOAT (53)         NULL,
    [Double0158]           FLOAT (53)         NULL,
    [Double0159]           FLOAT (53)         NULL,
    [Double0160]           FLOAT (53)         NULL,
    [Double0161]           FLOAT (53)         NULL,
    [Double0162]           FLOAT (53)         NULL,
    [Double0163]           FLOAT (53)         NULL,
    [Double0164]           FLOAT (53)         NULL,
    [Double0165]           FLOAT (53)         NULL,
    [Double0166]           FLOAT (53)         NULL,
    [Double0167]           FLOAT (53)         NULL,
    [Double0168]           FLOAT (53)         NULL,
    [Double0169]           FLOAT (53)         NULL,
    [Double0170]           FLOAT (53)         NULL,
    [Double0171]           FLOAT (53)         NULL,
    [Double0172]           FLOAT (53)         NULL,
    [Double0173]           FLOAT (53)         NULL,
    [Double0174]           FLOAT (53)         NULL,
    [Double0175]           FLOAT (53)         NULL,
    [Double0176]           FLOAT (53)         NULL,
    [Double0177]           FLOAT (53)         NULL,
    [Double0178]           FLOAT (53)         NULL,
    [Double0179]           FLOAT (53)         NULL,
    [Double0180]           FLOAT (53)         NULL,
    [Double0181]           FLOAT (53)         NULL,
    [Double0182]           FLOAT (53)         NULL,
    [Double0183]           FLOAT (53)         NULL,
    [Double0184]           FLOAT (53)         NULL,
    [Double0185]           FLOAT (53)         NULL,
    [Double0186]           FLOAT (53)         NULL,
    [Double0187]           FLOAT (53)         NULL,
    [Double0188]           FLOAT (53)         NULL,
    [Double0189]           FLOAT (53)         NULL,
    [Double0190]           FLOAT (53)         NULL,
    [Double0191]           FLOAT (53)         NULL,
    [Double0192]           FLOAT (53)         NULL,
    [Double0193]           FLOAT (53)         NULL,
    [Double0194]           FLOAT (53)         NULL,
    [Double0195]           FLOAT (53)         NULL,
    [Double0196]           FLOAT (53)         NULL,
    [Double0197]           FLOAT (53)         NULL,
    [Double0198]           FLOAT (53)         NULL,
    [Double0199]           FLOAT (53)         NULL,
    [Double0200]           FLOAT (53)         NULL,
    [DateTime0151]         DATETIMEOFFSET (7) NULL,
    [DateTime0152]         DATETIMEOFFSET (7) NULL,
    [DateTime0153]         DATETIMEOFFSET (7) NULL,
    [DateTime0154]         DATETIMEOFFSET (7) NULL,
    [DateTime0155]         DATETIMEOFFSET (7) NULL,
    [DateTime0156]         DATETIMEOFFSET (7) NULL,
    [DateTime0157]         DATETIMEOFFSET (7) NULL,
    [DateTime0158]         DATETIMEOFFSET (7) NULL,
    [DateTime0159]         DATETIMEOFFSET (7) NULL,
    [DateTime0160]         DATETIMEOFFSET (7) NULL,
    [DateTime0161]         DATETIMEOFFSET (7) NULL,
    [DateTime0162]         DATETIMEOFFSET (7) NULL,
    [DateTime0163]         DATETIMEOFFSET (7) NULL,
    [DateTime0164]         DATETIMEOFFSET (7) NULL,
    [DateTime0165]         DATETIMEOFFSET (7) NULL,
    [DateTime0166]         DATETIMEOFFSET (7) NULL,
    [DateTime0167]         DATETIMEOFFSET (7) NULL,
    [DateTime0168]         DATETIMEOFFSET (7) NULL,
    [DateTime0169]         DATETIMEOFFSET (7) NULL,
    [DateTime0170]         DATETIMEOFFSET (7) NULL,
    [DateTime0171]         DATETIMEOFFSET (7) NULL,
    [DateTime0172]         DATETIMEOFFSET (7) NULL,
    [DateTime0173]         DATETIMEOFFSET (7) NULL,
    [DateTime0174]         DATETIMEOFFSET (7) NULL,
    [DateTime0175]         DATETIMEOFFSET (7) NULL,
    [DateTime0176]         DATETIMEOFFSET (7) NULL,
    [DateTime0177]         DATETIMEOFFSET (7) NULL,
    [DateTime0178]         DATETIMEOFFSET (7) NULL,
    [DateTime0179]         DATETIMEOFFSET (7) NULL,
    [DateTime0180]         DATETIMEOFFSET (7) NULL,
    [DateTime0181]         DATETIMEOFFSET (7) NULL,
    [DateTime0182]         DATETIMEOFFSET (7) NULL,
    [DateTime0183]         DATETIMEOFFSET (7) NULL,
    [DateTime0184]         DATETIMEOFFSET (7) NULL,
    [DateTime0185]         DATETIMEOFFSET (7) NULL,
    [DateTime0186]         DATETIMEOFFSET (7) NULL,
    [DateTime0187]         DATETIMEOFFSET (7) NULL,
    [DateTime0188]         DATETIMEOFFSET (7) NULL,
    [DateTime0189]         DATETIMEOFFSET (7) NULL,
    [DateTime0190]         DATETIMEOFFSET (7) NULL,
    [DateTime0191]         DATETIMEOFFSET (7) NULL,
    [DateTime0192]         DATETIMEOFFSET (7) NULL,
    [DateTime0193]         DATETIMEOFFSET (7) NULL,
    [DateTime0194]         DATETIMEOFFSET (7) NULL,
    [DateTime0195]         DATETIMEOFFSET (7) NULL,
    [DateTime0196]         DATETIMEOFFSET (7) NULL,
    [DateTime0197]         DATETIMEOFFSET (7) NULL,
    [DateTime0198]         DATETIMEOFFSET (7) NULL,
    [DateTime0199]         DATETIMEOFFSET (7) NULL,
    [DateTime0200]         DATETIMEOFFSET (7) NULL,
    [Boolean0151]          BIT                NULL,
    [Boolean0152]          BIT                NULL,
    [Boolean0153]          BIT                NULL,
    [Boolean0154]          BIT                NULL,
    [Boolean0155]          BIT                NULL,
    [Boolean0156]          BIT                NULL,
    [Boolean0157]          BIT                NULL,
    [Boolean0158]          BIT                NULL,
    [Boolean0159]          BIT                NULL,
    [Boolean0160]          BIT                NULL,
    [Boolean0161]          BIT                NULL,
    [Boolean0162]          BIT                NULL,
    [Boolean0163]          BIT                NULL,
    [Boolean0164]          BIT                NULL,
    [Boolean0165]          BIT                NULL,
    [Boolean0166]          BIT                NULL,
    [Boolean0167]          BIT                NULL,
    [Boolean0168]          BIT                NULL,
    [Boolean0169]          BIT                NULL,
    [Boolean0170]          BIT                NULL,
    [Boolean0171]          BIT                NULL,
    [Boolean0172]          BIT                NULL,
    [Boolean0173]          BIT                NULL,
    [Boolean0174]          BIT                NULL,
    [Boolean0175]          BIT                NULL,
    [Boolean0176]          BIT                NULL,
    [Boolean0177]          BIT                NULL,
    [Boolean0178]          BIT                NULL,
    [Boolean0179]          BIT                NULL,
    [Boolean0180]          BIT                NULL,
    [Boolean0181]          BIT                NULL,
    [Boolean0182]          BIT                NULL,
    [Boolean0183]          BIT                NULL,
    [Boolean0184]          BIT                NULL,
    [Boolean0185]          BIT                NULL,
    [Boolean0186]          BIT                NULL,
    [Boolean0187]          BIT                NULL,
    [Boolean0188]          BIT                NULL,
    [Boolean0189]          BIT                NULL,
    [Boolean0190]          BIT                NULL,
    [Boolean0191]          BIT                NULL,
    [Boolean0192]          BIT                NULL,
    [Boolean0193]          BIT                NULL,
    [Boolean0194]          BIT                NULL,
    [Boolean0195]          BIT                NULL,
    [Boolean0196]          BIT                NULL,
    [Boolean0197]          BIT                NULL,
    [Boolean0198]          BIT                NULL,
    [Boolean0199]          BIT                NULL,
    [Boolean0200]          BIT                NULL,
    [Identity0151]         UNIQUEIDENTIFIER   NULL,
    [Identity0152]         UNIQUEIDENTIFIER   NULL,
    [Identity0153]         UNIQUEIDENTIFIER   NULL,
    [Identity0154]         UNIQUEIDENTIFIER   NULL,
    [Identity0155]         UNIQUEIDENTIFIER   NULL,
    [Identity0156]         UNIQUEIDENTIFIER   NULL,
    [Identity0157]         UNIQUEIDENTIFIER   NULL,
    [Identity0158]         UNIQUEIDENTIFIER   NULL,
    [Identity0159]         UNIQUEIDENTIFIER   NULL,
    [Identity0160]         UNIQUEIDENTIFIER   NULL,
    [Identity0161]         UNIQUEIDENTIFIER   NULL,
    [Identity0162]         UNIQUEIDENTIFIER   NULL,
    [Identity0163]         UNIQUEIDENTIFIER   NULL,
    [Identity0164]         UNIQUEIDENTIFIER   NULL,
    [Identity0165]         UNIQUEIDENTIFIER   NULL,
    [Identity0166]         UNIQUEIDENTIFIER   NULL,
    [Identity0167]         UNIQUEIDENTIFIER   NULL,
    [Identity0168]         UNIQUEIDENTIFIER   NULL,
    [Identity0169]         UNIQUEIDENTIFIER   NULL,
    [Identity0170]         UNIQUEIDENTIFIER   NULL,
    [Identity0171]         UNIQUEIDENTIFIER   NULL,
    [Identity0172]         UNIQUEIDENTIFIER   NULL,
    [Identity0173]         UNIQUEIDENTIFIER   NULL,
    [Identity0174]         UNIQUEIDENTIFIER   NULL,
    [Identity0175]         UNIQUEIDENTIFIER   NULL,
    [Identity0176]         UNIQUEIDENTIFIER   NULL,
    [Identity0177]         UNIQUEIDENTIFIER   NULL,
    [Identity0178]         UNIQUEIDENTIFIER   NULL,
    [Identity0179]         UNIQUEIDENTIFIER   NULL,
    [Identity0180]         UNIQUEIDENTIFIER   NULL,
    [Identity0181]         UNIQUEIDENTIFIER   NULL,
    [Identity0182]         UNIQUEIDENTIFIER   NULL,
    [Identity0183]         UNIQUEIDENTIFIER   NULL,
    [Identity0184]         UNIQUEIDENTIFIER   NULL,
    [Identity0185]         UNIQUEIDENTIFIER   NULL,
    [Identity0186]         UNIQUEIDENTIFIER   NULL,
    [Identity0187]         UNIQUEIDENTIFIER   NULL,
    [Identity0188]         UNIQUEIDENTIFIER   NULL,
    [Identity0189]         UNIQUEIDENTIFIER   NULL,
    [Identity0190]         UNIQUEIDENTIFIER   NULL,
    [Identity0191]         UNIQUEIDENTIFIER   NULL,
    [Identity0192]         UNIQUEIDENTIFIER   NULL,
    [Identity0193]         UNIQUEIDENTIFIER   NULL,
    [Identity0194]         UNIQUEIDENTIFIER   NULL,
    [Identity0195]         UNIQUEIDENTIFIER   NULL,
    [Identity0196]         UNIQUEIDENTIFIER   NULL,
    [Identity0197]         UNIQUEIDENTIFIER   NULL,
    [Identity0198]         UNIQUEIDENTIFIER   NULL,
    [Identity0199]         UNIQUEIDENTIFIER   NULL,
    [Identity0200]         UNIQUEIDENTIFIER   NULL
) ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_AnalyticsModel_tbl_WorkItemRevisionCustom04_WorkItemRevisionSK]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom04]([PartitionId] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_WorkItemRevisionCustom04]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom04]
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

