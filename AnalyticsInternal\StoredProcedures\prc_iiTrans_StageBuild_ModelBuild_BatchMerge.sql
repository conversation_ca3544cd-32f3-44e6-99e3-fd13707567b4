/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FF1AECB97C88F7C2A1167B3944A50A81BFD3DF8D
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageBuild_ModelBuild_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #ImpactedBuilds
    (
        BuildId INT NOT NULL,
    )

    INSERT  #ImpactedBuilds (BuildId)
    SELECT TOP (@batchSizeMax) WITH TIES BuildId
    FROM    AnalyticsStage.tbl_Build
    WHERE   PartitionId = @partitionId
            AND BuildId > ISNULL(@stateData, -1)
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    ORDER BY BuildId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(BuildId) FROM #ImpactedBuilds)

    CREATE TABLE #Build
    (
        BuildId                         INT                 NOT NULL,
        BuildPipelineId                 INT                 NULL,
        ProjectSK                       UNIQUEIDENTIFIER    NULL,
        BuildPipelineSK                 INT                 NULL,
        BranchSK                        INT                 NULL,
        BuildNumber                     VARCHAR(260)        COLLATE DATABASE_DEFAULT NULL,
        BuildNumberRevision             INT                 NULL,
        BuildReason                     SMALLINT            NULL,
        BuildOutcome                    TINYINT             NULL,
        QueuedDate                      DATETIMEOFFSET(0)   NULL,
        QueuedDateSK                    INT                 NULL,
        StartedDate                     DATETIMEOFFSET(0)   NULL,
        StartedDateSK                   INT                 NULL,
        CompletedDate                   DATETIMEOFFSET(0)   NULL,
        CompletedDateSK                 INT                 NULL,
        BuildDurationSeconds            DECIMAL(18,3)       NULL,
        QueueDurationSeconds            DECIMAL(18,3)       NULL,
        TotalDurationSeconds            DECIMAL(18,3)       NULL,
        SucceededCount                  INT                 NULL,
        PartiallySucceededCount         INT                 NULL,
        FailedCount                     INT                 NULL,
        CanceledCount                   INT                 NULL,
    )

    INSERT  #Build
    (
        BuildId,
        BuildPipelineId,
        ProjectSK,
        BuildPipelineSK,
        BranchSK,
        BuildNumber,
        BuildNumberRevision,
        BuildReason,
        BuildOutcome,
        QueuedDate,
        QueuedDateSK,
        StartedDate,
        StartedDateSK,
        CompletedDate,
        CompletedDateSK,
        BuildDurationSeconds,
        QueueDurationSeconds,
        TotalDurationSeconds,
        SucceededCount,
        PartiallySucceededCount,
        FailedCount,
        CanceledCount
    )
    SELECT  s.BuildId,
            s.DefinitionId AS BuildPipelineId,
            s.ProjectGuid AS ProjectSK,
            mbp.BuildPipelineSk,
            mbb.BranchSK,
            s.BuildNumber,
            s.BuildNumberRevision,
            s.Reason AS BuildReason,
            s.Result AS BuildOutcome,
            s.QueueTime AT TIME ZONE @timeZone AS QueuedDate,
            AnalyticsInternal.func_GenDateSK(s.QueueTime AT TIME ZONE @timeZone) AS QueuedDateSK,
            s.StartTime AT TIME ZONE @timeZone AS QueuedDate,
            AnalyticsInternal.func_GenDateSK(s.StartTime AT TIME ZONE @timeZone) AS StartedDateSK,
            s.FinishTime AT TIME ZONE @timeZone AS CompleteDate,
            AnalyticsInternal.func_GenDateSK(s.FinishTime AT TIME ZONE @timeZone) AS CompletedDateSK,
            DATEDIFF_BIG(millisecond, s.StartTime, s.FinishTime) / 1000.0 AS BuildDurationSeconds,
            DATEDIFF_BIG(millisecond, s.QueueTime, s.StartTime) / 1000.0 AS QueueDurationSeconds,
            DATEDIFF_BIG(millisecond, s.QueueTime, s.FinishTime) / 1000.0 AS BuildDurationSeconds,
            IIF(s.Result IS NULL, NULL, IIF(s.Result = 2, 1, 0)) AS SucceededCount,
            IIF(s.Result IS NULL, NULL, IIF(s.Result = 4, 1, 0)) AS PartiallySucceededCount,
            IIF(s.Result IS NULL, NULL, IIF(s.Result = 8, 1, 0)) AS FailedCount,
            IIF(s.Result IS NULL, NULL, IIF(s.Result = 32, 1, 0)) AS CanceledCount
    FROM    AnalyticsStage.tbl_Build s
    JOIN    #ImpactedBuilds i
    ON      i.BuildId = s.BuildId
    LEFT JOIN AnalyticsModel.tbl_BuildPipeline mbp -- StageBuild_ModelBuildPipeline_BatchInsert guards against late arriving build pipelines
    ON      mbp.PartitionId = @partitionId
            AND mbp.BuildPipelineId = s.DefinitionId
    LEFT JOIN AnalyticsModel.tbl_Branch mbb -- StageBuild_ModelBranch_BatchInsert guards against late arriving branches
    ON      mbb.PartitionId = @partitionId
            AND mbb.RepositoryId = s.RepositoryId
            AND mbb.BranchName = s.BranchName
    WHERE   s.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    MERGE   AnalyticsModel.tbl_Build t
    USING   #Build s
    ON      (t.PartitionId = @partitionId AND t.BuildId = s.BuildId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.BuildPipelineId,
        s.ProjectSK,
        s.BuildPipelineSK,
        s.BranchSK,
        s.BuildNumber,
        s.BuildNumberRevision,
        s.BuildReason,
        s.BuildOutcome,
        s.QueuedDate,
        s.QueuedDateSK,
        s.StartedDate,
        s.StartedDateSK,
        s.CompletedDate,
        s.CompletedDateSK,
        s.BuildDurationSeconds,
        s.QueueDurationSeconds,
        s.TotalDurationSeconds,
        s.SucceededCount,
        s.PartiallySucceededCount,
        s.FailedCount,
        s.CanceledCount
        INTERSECT
        SELECT
        t.BuildPipelineId,
        t.ProjectSK,
        t.BuildPipelineSK,
        t.BranchSK,
        t.BuildNumber,
        t.BuildNumberRevision,
        t.BuildReason,
        t.BuildOutcome,
        t.QueuedDate,
        t.QueuedDateSK,
        t.StartedDate,
        t.StartedDateSK,
        t.CompletedDate,
        t.CompletedDateSK,
        t.BuildDurationSeconds,
        t.QueueDurationSeconds,
        t.TotalDurationSeconds,
        t.SucceededCount,
        t.PartiallySucceededCount,
        t.FailedCount,
        t.CanceledCount
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate    = @batchDt,
        AnalyticsBatchId        = @batchId,
        BuildId                 = s.BuildId,
        BuildPipelineId         = s.BuildPipelineId,
        ProjectSK               = s.ProjectSK,
        BuildPipelineSK         = s.BuildPipelineSK,
        BranchSK                = s.BranchSK,
        BuildNumber             = s.BuildNumber,
        BuildNumberRevision     = s.BuildNumberRevision,
        BuildReason             = s.BuildReason,
        BuildOutcome            = s.BuildOutcome,
        QueuedDate              = s.QueuedDate,
        QueuedDateSK            = s.QueuedDateSK,
        StartedDate             = s.StartedDate,
        StartedDateSK           = s.StartedDateSK,
        CompletedDate           = s.CompletedDate,
        CompletedDateSK         = s.CompletedDateSK,
        BuildDurationSeconds    = s.BuildDurationSeconds,
        QueueDurationSeconds    = s.QueueDurationSeconds,
        TotalDurationSeconds    = s.TotalDurationSeconds,
        SucceededCount          = s.SucceededCount,
        PartiallySucceededCount = s.PartiallySucceededCount,
        FailedCount             = s.FailedCount,
        CanceledCount           = s.CanceledCount
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        BuildId,
        BuildPipelineId,
        ProjectSK,
        BuildPipelineSK,
        BranchSK,
        BuildNumber,
        BuildNumberRevision,
        BuildReason,
        BuildOutcome,
        QueuedDate,
        QueuedDateSK,
        StartedDate,
        StartedDateSK,
        CompletedDate,
        CompletedDateSK,
        BuildDurationSeconds,
        QueueDurationSeconds,
        TotalDurationSeconds,
        SucceededCount,
        PartiallySucceededCount,
        FailedCount,
        CanceledCount
    )
    VALUES (
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.BuildId,
        s.BuildPipelineId,
        s.ProjectSK,
        s.BuildPipelineSK,
        s.BranchSK,
        s.BuildNumber,
        s.BuildNumberRevision,
        s.BuildReason,
        s.BuildOutcome,
        s.QueuedDate,
        s.QueuedDateSK,
        s.StartedDate,
        s.StartedDateSK,
        s.CompletedDate,
        s.CompletedDateSK,
        s.BuildDurationSeconds,
        s.QueueDurationSeconds,
        s.TotalDurationSeconds,
        s.SucceededCount,
        s.PartiallySucceededCount,
        s.FailedCount,
        s.CanceledCount
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    DROP TABLE #Build
    DROP TABLE #ImpactedBuilds

    RETURN 0
END

GO

