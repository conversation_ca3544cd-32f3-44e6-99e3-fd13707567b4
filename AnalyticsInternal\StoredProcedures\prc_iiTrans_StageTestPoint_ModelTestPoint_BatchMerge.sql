/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A44C427617E54576F43CEFFF5221DA3082812C59
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestPoint_ModelTestPoint_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    -- Mentioning primary key to avoid sorting #ChangedTestPlans when joining with other tables
    -- This should be safe, as for a partitionId, point Ids are unique.
    CREATE TABLE #ChangedTestPoints
    (
        TestPointId        INT NOT NULL PRIMARY KEY
    )

    IF (@subBatchCount <= 1)
    BEGIN
        -- Initial batches in case of sub-batching, mostly @triggerBatchIdStart and @triggerBatchIdEnd
        -- don't differ much. Go with IX_tbl_TestPoint_AxBatchIdChanged
        INSERT     #ChangedTestPoints (TestPointId)
        SELECT TOP (@batchSizeMax) p.TestPointId
        FROM       AnalyticsStage.tbl_TestPoint p WITH (INDEX (IX_tbl_TestPoint_AxBatchIdChanged))
        WHERE      p.PartitionId = @partitionId
                   AND p.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                   AND p.TestPointId > ISNULL(@stateData, 0)
        ORDER BY   p.TestPointId

        SET @complete     = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPoints)
    END
    ELSE
    BEGIN
        -- Subsequent batches in case of sub-batching, mostly @triggerBatchIdStart and @triggerBatchIdEnd
        -- cover a lot of batches in between. Go with CI_tbl_TestPoint to avoid expensive sorts
        INSERT     #ChangedTestPoints (TestPointId)
        SELECT TOP (@batchSizeMax) p.TestPointId
        FROM       AnalyticsStage.tbl_TestPoint p WITH (INDEX (CI_tbl_TestPoint))
        WHERE      p.PartitionId = @partitionId
                   AND p.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                   AND p.TestPointId > ISNULL(@stateData, 0)
        ORDER BY   p.TestPointId

        SET @complete     = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPoints)
    END

    CREATE TABLE #TestPoint
    (
        ProjectSK                   UNIQUEIDENTIFIER    NULL,
        TestSuiteSK                 INT                 NULL,
        TestPlanId                  INT                 NULL,
        TestSuiteId                 INT                 NULL,
        TestPointId                 INT                 NOT NULL,
        TestConfigurationSK         INT                 NULL,
        TestConfigurationId         INT                 NULL,
        TestCaseId                  INT                 NULL,
        State                       TINYINT             NULL,
        Revision                    INT                 NULL,
        TesterUserSK                UNIQUEIDENTIFIER    NULL,
        AssignedToUserSK            UNIQUEIDENTIFIER    NULL,
        Priority                    INT                 NULL,
        AutomationStatus            NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        LastResultState             TINYINT             NULL,
        LastResultOutcome           TINYINT             NULL,
        ChangedDate                 DATETIMEOFFSET(0)   NOT NULL,
        ChangedDateSK               INT                 NULL,
        IsDeleted                   BIT                 NOT NULL,
        DataSourceId                INT                 NOT NULL,
        HasWorkItemProperties       BIT                 NOT NULL
    )

    INSERT  #TestPoint
    (
        ProjectSK,
        TestSuiteSK,
        TestPlanId,
        TestSuiteId,
        TestPointId,
        TestConfigurationSK,
        TestConfigurationId,
        TestCaseId,
        State,
        Revision,
        TesterUserSK,
        AssignedToUserSK,
        Priority,
        AutomationStatus,
        LastResultState,
        LastResultOutcome,
        ChangedDate,
        ChangedDateSK,
        IsDeleted,
        DataSourceId,
        HasWorkItemProperties
    )
    SELECT    mts.ProjectSK,
              mts.TestSuiteSK,
              p.TestPlanId,
              p.TestSuiteId,
              p.TestPointId,
              mtc.TestConfigurationSK,
              p.TestConfigurationId,
              p.TestCaseId,
              p.State,
              p.Revision,
              AnalyticsInternal.func_GetUserSKFromWITPerson(p.Tester),
              AnalyticsInternal.func_GetUserSKFromWITPerson(TestCase.AssignedTo),
              TestCase.Priority,
              TestCase.AutomationStatus,
              p.TestResultState,
              IIF (p.TestResultOutcome IS NULL, 1, IIF(p.TestResultOutcome < 2, 1, p.TestResultOutcome)), -- Set TestResultOutcome to 1 when it is NULL or 'Unspecified' so that we get a single value for active points
              p.ChangedDate AT TIME ZONE @timeZone,
              AnalyticsInternal.func_GenDateSK(p.ChangedDate AT TIME ZONE @timeZone),
              (p.IsDeleted | ~ISNULL(p.Enabled, 1) | ISNULL(mts.IsDeleted, 0) | IIF (mts.TestPlanState IS NULL, 0, IIF(mts.TestPlanState = 255, 1, 0))), -- Set IsDeleted for points to 1 for points lying under deleted test plans / test suites too
              p.DataSourceId,
              IIF(TestCase.TestCaseId > 0, 1, 0)
    FROM      #ChangedTestPoints cp
    INNER LOOP JOIN AnalyticsStage.tbl_TestPoint p WITH (INDEX (CI_tbl_TestPoint), FORCESEEK) -- Having LOOP hint and FORCESEEK to push index predicates to CI
    ON        p.partitionId = @partitionId
              AND p.TestPointId = cp.TestPointId
    LEFT JOIN AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_TestPlanId_TestSuiteId), FORCESEEK)
    ON        mts.PartitionId = @partitionId
              AND mts.TestPlanId = p.TestPlanId
              AND mts.TestSuiteId = p.TestSuiteId
    LEFT JOIN AnalyticsModel.tbl_TestConfiguration mtc WITH (INDEX (IX_tbl_TestConfiguration_TestConfigurationId), FORCESEEK)
    ON        mtc.PartitionId = @partitionId
              AND mtc.TestConfigurationId = p.TestConfigurationId
    OUTER APPLY (
        SELECT   TOP 1 System_AssignedTo AS AssignedTo, Microsoft_VSTS_Common_Priority AS Priority, Microsoft_VSTS_TCM_AutomationStatus AS AutomationStatus, System_Id AS TestCaseId
        FROM     AnalyticsStage.tbl_WorkItemRevision wir WITH (INDEX (CI_tbl_WorkItemRevision), FORCESEEK)
        WHERE    wir.PartitionId = @partitionId
                 AND wir.System_Id = p.TestCaseId
        ORDER BY System_Rev DESC
    ) TestCase
    WHERE     p.PartitionId = @partitionId
    OPTION    (OPTIMIZE FOR (@partitionId UNKNOWN))

    MERGE AnalyticsModel.tbl_TestPoint t
    USING #TestPoint s
    -- Here we check equality on (PartitionId, TestPlanId, TestPointId, DataSourceId) since this is the uniqueness condition on ops side.
    -- We have introduced ProjectGuid only on the analytics side and we derive it from test suite only during transformation.
    -- Points can not be moved across projects once created, hence we only set the projectSK once during creation of point.
    ON (t.PartitionId = @partitionId AND t.TestPlanId = s.TestPlanId AND t.TestPointId = s.TestPointId AND t.DataSourceId = s.DataSourceId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.TestSuiteSK,
        s.TestPlanId,
        s.TestSuiteId,
        s.TestPointId,
        s.TestConfigurationSK,
        s.TestConfigurationId,
        s.TestCaseId,
        s.State,
        s.Revision,
        s.TesterUserSK,
        s.AssignedToUserSK,
        s.Priority,
        s.AutomationStatus,
        s.LastResultState,
        s.LastResultOutcome,
        s.ChangedDate,
        s.ChangedDateSK,
        s.IsDeleted,
        s.HasWorkItemProperties
        INTERSECT
        SELECT
        t.TestSuiteSK,
        t.TestPlanId,
        t.TestSuiteId,
        t.TestPointId,
        t.TestConfigurationSK,
        t.TestConfigurationId,
        t.TestCaseId,
        t.State,
        t.Revision,
        t.TesterUserSK,
        t.AssignedToUserSK,
        t.Priority,
        t.AutomationStatus,
        t.LastResultState,
        t.LastResultOutcome,
        t.ChangedDate,
        t.ChangedDateSK,
        t.IsDeleted,
        t.HasWorkItemProperties
    ) THEN
    UPDATE SET
        AnalyticsUpdatedDate        = @batchDt,
        AnalyticsBatchId            = @batchId,
        TestSuiteSK                 = s.TestSuiteSK,
        TestPlanId                  = s.TestPlanId,
        TestSuiteId                 = s.TestSuiteId,
        TestPointId                 = s.TestPointId,
        TestConfigurationSK         = s.TestConfigurationSK,
        TestConfigurationId         = s.TestConfigurationId,
        TestCaseId                  = s.TestCaseId,
        State                       = s.State,
        Revision                    = s.Revision,
        TesterUserSK                = s.TesterUserSK,
        AssignedToUserSK            = s.AssignedToUserSK,
        Priority                    = s.Priority,
        AutomationStatus            = s.AutomationStatus,
        LastResultState             = s.LastResultState,
        LastResultOutcome           = s.LastResultOutcome,
        ChangedDate                 = s.ChangedDate,
        ChangedDateSK               = s.ChangedDateSK,
        InternalForSnapshotHashJoin = 1,
        IsDeleted                   = s.IsDeleted,
        HasWorkItemProperties       = s.HasWorkItemProperties
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        ProjectSK,
        TestSuiteSK,
        TestPlanId,
        TestSuiteId,
        TestPointId,
        TestConfigurationSK,
        TestConfigurationId,
        TestCaseId,
        State,
        Revision,
        TesterUserSK,
        AssignedToUserSK,
        Priority,
        AutomationStatus,
        LastResultState,
        LastResultOutcome,
        ChangedDate,
        ChangedDateSK,
        InternalForSnapshotHashJoin,
        IsDeleted,
        DataSourceId,
        HasWorkItemProperties
    )
    VALUES (
        @partitionId,
        @batchDt,
        @batchDt,
        @batchId,
        s.ProjectSK,
        s.TestSuiteSK,
        s.TestPlanId,
        s.TestSuiteId,
        s.TestPointId,
        s.TestConfigurationSK,
        s.TestConfigurationId,
        s.TestCaseId,
        s.State,
        s.Revision,
        s.TesterUserSK,
        s.AssignedToUserSK,
        s.Priority,
        s.AutomationStatus,
        s.LastResultState,
        s.LastResultOutcome,
        s.ChangedDate,
        s.ChangedDateSK,
        1,
        s.IsDeleted,
        s.DataSourceId,
        s.HasWorkItemProperties
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    DROP TABLE #TestPoint
    DROP TABLE #ChangedTestPoints

    RETURN 0
END

GO

