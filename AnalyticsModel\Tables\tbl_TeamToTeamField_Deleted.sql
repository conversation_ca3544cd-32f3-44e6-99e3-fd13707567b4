CREATE TABLE [AnalyticsModel].[tbl_TeamToTeamField_Deleted] (
    [PartitionId]             INT              NOT NULL,
    [AnalyticsDeletedDate]    DATETIME2 (7)    NOT NULL,
    [AnalyticsBatchIdDeleted] BIGINT           NOT NULL,
    [TeamSK]                  UNIQUEIDENTIFIER NOT NULL,
    [TeamFieldSK]             INT              NOT NULL
);


GO

CREATE NONCLUSTERED INDEX [IX_AnalyticsModel_tbl_TeamToTeamField_AxBatchIdDeleted]
    ON [AnalyticsModel].[tbl_TeamToTeamField_Deleted]([PartitionId] ASC, [AnalyticsBatchIdDeleted] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE CLUSTERED INDEX [CI_AnalyticsModel_tbl_TeamToTeamField_Deleted]
    ON [AnalyticsModel].[tbl_TeamToTeamField_Deleted]([PartitionId] ASC, [TeamSK] ASC, [TeamFieldSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

