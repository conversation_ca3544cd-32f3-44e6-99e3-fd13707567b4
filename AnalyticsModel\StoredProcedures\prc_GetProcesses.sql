/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D84AC02F1A76ACF9AA1802340A92C58E747BEC46
CREATE PROCEDURE AnalyticsModel.prc_GetProcesses
    @partitionId     INT,
    @projectIds      typ_GuidTable READONLY
AS
BEGIN

    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    BEGIN
        SELECT  ProjectSK,
                TeamSK,
                BacklogCategoryReferenceName,
                BacklogName,
                BacklogType,
                BacklogLevel,
                WorkItemType,
                HasBacklog,
                IsHiddenType,
                IsBugType
        FROM    AnalyticsModel.tbl_Process p
        JOIN    @projectIds projectIds
        ON      p.ProjectSK = projectIds.Id
        WHERE   p.PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

END

GO

