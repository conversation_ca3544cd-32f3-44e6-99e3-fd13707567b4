CREATE TABLE [AnalyticsModel].[tbl_TestConfiguration] (
    [PartitionId]          INT              NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7)    NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7)    NOT NULL,
    [AnalyticsBatchId]     BIGINT           NOT NULL,
    [TestConfigurationSK]  INT              IDENTITY (1, 1) NOT NULL,
    [ProjectSK]            UNIQUEIDENTIFIER NOT NULL,
    [TestConfigurationId]  INT              NOT NULL,
    [Name]                 NVARCHAR (256)   NULL,
    [State]                NVARCHAR (128)   NULL,
    [DataSourceId]         INT              DEFAULT ((0)) NOT NULL
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_TestConfiguration_TestConfigurationId]
    ON [AnalyticsModel].[tbl_TestConfiguration]([PartitionId] ASC, [TestConfigurationId] ASC)
    INCLUDE([AnalyticsBatchId]) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestConfiguration_AnalyticsBatchId]
    ON [AnalyticsModel].[tbl_TestConfiguration]([PartitionId] ASC, [AnalyticsBatchId] ASC)
    INCLUDE([TestConfigurationId]) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_TestConfiguration]
    ON [AnalyticsModel].[tbl_TestConfiguration]([PartitionId] ASC, [TestConfigurationSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

