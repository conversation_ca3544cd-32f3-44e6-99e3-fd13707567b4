/******************************************************************************************************
** Warning: the contents of this function are critical to its functioning.
** Modifying the contents of this function could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 39B87FCBD5CBED71E6134D1E4798EAB8365AFE83
--------------------------------------------------------------------
-- Returns Partition TimeZone
-- NEVER, EVER USE THIS FUNCTION IN SELECT
--------------------------------------------------------------------
CREATE FUNCTION AnalyticsInternal.func_GetPartitionTimeZone(@partitionId INT)
RETURNS NVARCHAR(128)
AS
BEGIN
    DECLARE @timeZone NVARCHAR(128) = NULL
    SELECT  TOP 1 @timeZone = TimeZone
    FROM    AnalyticsInternal.tbl_CollectionTimeZone
    WHERE   PartitionID = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    RETURN ISNULL(@timeZone, 'UTC')
END

GO

GRANT EXECUTE
    ON OBJECT::[AnalyticsInternal].[func_GetPartitionTimeZone] TO [VSODIAGROLE]
    AS [dbo];


GO

