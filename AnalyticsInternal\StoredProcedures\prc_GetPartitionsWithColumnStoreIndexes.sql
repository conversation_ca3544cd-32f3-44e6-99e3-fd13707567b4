/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: CE5933DB4397EA9FF38AE79C5F911B97679C607C
CREATE PROCEDURE AnalyticsInternal.prc_GetPartitionsWithColumnStoreIndexes
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    SELECT      partition_number,
                i.name AS ColumnStoreIndexName,
                s.name + '.' + t.name AS TableName,
                ISNULL(tm.IsActive, 0) AS IsActive
    FROM        sys.schemas (NOLOCK) as s
    JOIN        sys.tables (NOLOCK) as t
                ON  t.schema_id = s.schema_id
    JOIN        sys.indexes (NOLOCK) AS i
                ON  i.object_id = t.object_id
    JOIN        sys.partitions AS p
                ON  i.object_id = p.object_id
                AND i.index_id = p.index_id
    JOIN        sys.data_spaces ds
                ON ds.data_space_id = i.data_space_id
    JOIN        sys.partition_schemes ps
                ON ds.name = ps.name
    JOIN        sys.partition_range_values prv
                ON prv.function_id = ps.function_id
                AND prv.boundary_id=p.partition_number
    LEFT JOIN   AnalyticsInternal.tbl_TableMaintenance tm
                ON tm.TableName = t.name
                AND tm.EndPartitionId = value -- implicit conversion to avoid errors like: "Explicit conversion from data type datetime2 to int is not allowed."
                AND tm.IsActive = 1
    WHERE       p.rows > 0
                AND s.name = 'AnalyticsModel'
                AND (t.name IN ('tbl_WorkItem','tbl_WorkItemHistory','tbl_TestRun','tbl_TestResult','tbl_Test','tbl_TestResultDaily')
                OR  t.name LIKE 'tbl_WorkItemRevisionCustom[0-9][0-9]')
                AND i.type = 5 -- CCI

    RETURN 0
END

GO

