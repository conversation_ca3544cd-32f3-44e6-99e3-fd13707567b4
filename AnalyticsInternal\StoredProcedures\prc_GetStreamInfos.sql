/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 821A1BEEEFE5B6FD2AC121699C262DC8332F9D6B
CREATE PROCEDURE AnalyticsInternal.prc_GetStreamInfos
    @partitionId    INT,
    @tableName VARCHAR(64),
    @providerShardId INT,
    @streamId INT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@tableName IS NOT NULL)
    BEGIN
        IF (@providerShardId IS NOT NULL)
        BEGIN
            SELECT  TableName,
                    AnalyticsProviderShardId AS ProviderShardId,
                    AnalyticsStreamId AS StreamId,
                    WaterMark,
                    [Enabled],
                    [Priority],
                    [Current],
                    Maintenance,
                    Disposed,
                    CreateTime,
                    LoadedTime,
                    InitialContentVersion,
                    LatestContentVersion,
                    ISNULL(KeysOnly, 0) AS KeysOnly,
                    MaintenanceReason,
                    MaintenanceChangedTime
            FROM    AnalyticsInternal.tbl_TableProviderShardStream WITH (REPEATABLEREAD)
            WHERE   PartitionId = @partitionId
                    AND TableName = @tableName
                    AND AnalyticsProviderShardId = @providerShardId
                    AND AnalyticsStreamId = ISNULL(@streamId, AnalyticsStreamId)
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
        END
        ELSE
        BEGIN
            SELECT  TableName,
                    AnalyticsProviderShardId AS ProviderShardId,
                    AnalyticsStreamId AS StreamId,
                    WaterMark,
                    [Enabled],
                    [Priority],
                    [Current],
                    Maintenance,
                    Disposed,
                    CreateTime,
                    LoadedTime,
                    InitialContentVersion,
                    LatestContentVersion,
                    ISNULL(KeysOnly, 0) AS KeysOnly,
                    MaintenanceReason,
                    MaintenanceChangedTime
            FROM    AnalyticsInternal.tbl_TableProviderShardStream WITH (REPEATABLEREAD)
            WHERE   PartitionId = @partitionId
                    AND TableName = @tableName
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
        END
    END
    ELSE
    BEGIN
        SELECT  TableName,
                AnalyticsProviderShardId AS ProviderShardId,
                AnalyticsStreamId AS StreamId,
                WaterMark,
                [Enabled],
                [Priority],
                [Current],
                Maintenance,
                Disposed,
                CreateTime,
                LoadedTime,
                InitialContentVersion,
                LatestContentVersion,
                ISNULL(KeysOnly, 0) AS KeysOnly,
                MaintenanceReason,
                MaintenanceChangedTime
        FROM    AnalyticsInternal.tbl_TableProviderShardStream WITH (REPEATABLEREAD)
        WHERE   PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

END

GO

