/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 5FF9929B5448D1997482C15939DED7D3EABC815C
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestResult_ModelTestRun_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 1000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    CREATE TABLE #TestRun
    (
        TestRunId                       INT                 NOT NULL,
        DataSourceId                    INT                 NOT NULL,
        -- aggregations of test results
        ResultDurationSeconds           DECIMAL(18,3)       NULL,
        ResultCount                     INT                 NULL,
        ResultPassCount                 INT                 NULL,
        ResultFailCount                 INT                 NULL,
        -- counts for invididual outcomes
        ResultOutcomeNoneCount          INT                 NULL,
        ResultOutcomePassedCount        INT                 NULL,
        ResultOutcomeFailedCount        INT                 NULL,
        ResultOutcomeInconclusiveCount  INT                 NULL,
        ResultOutcomeTimeoutCount       INT                 NULL,
        ResultOutcomeAbortedCount       INT                 NULL,
        ResultOutcomeBlockedCount       INT                 NULL,
        ResultOutcomeNotExecutedCount   INT                 NULL,
        ResultOutcomeWarningCount       INT                 NULL,
        ResultOutcomeErrorCount         INT                 NULL,
        ResultOutcomeNotApplicableCount INT                 NULL,
        ResultOutcomeNotImpactedCount   INT                 NULL,
        PRIMARY KEY CLUSTERED (TestRunId, DataSourceId)
    )

    IF (@triggerBatchIdStart > 1 AND (@stateData IS NULL OR @state = 'skip')) -- always assume NCI for small batches, and split datasource tables
    BEGIN
        -- Run that for first sub-batch in incremental processing or if we failed due gaps in TestRunIds caused by TFS and TCM running in parallel
        INSERT  #TestRun (TestRunId, DataSourceId)
        SELECT  TOP (@batchSizeMax) WITH TIES
                TestRunId,
                DataSourceId
        FROM    AnalyticsStage.tbl_TestResult WITH (INDEX (IX_tbl_TestResult_AnalyticsBatchIdCreated), FORCESEEK)
        WHERE   PartitionId = @partitionId
                AND TestRunId > ISNULL(@stateData, -1)
                AND AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        GROUP BY TestRunId, DataSourceId
        ORDER BY TestRunId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endState = '' -- try sequential optimizaion next time
    END
    ELSE
    BEGIN
        -- this opimization assumes test results arrive with roughly sequential run ids
        CREATE TABLE #ImpactedRuns0
        (
            TestRunId INT NOT NULL
        )

        IF (@triggerBatchIdStart > 1)
        BEGIN
            DECLARE @prevRunId BIGINT = ISNULL(@stateData, -1)
            DECLARE @maxRunId BIGINT = @prevRunId + (@batchSizeMax * 10) -- runaway window
            SET @maxRunId = IIF(@maxRunId > @prevRunId + 1000, @maxRunId, @prevRunId + 1000) -- ensure minimum runaway window

            -- getting TestRunId alone from clustered index is much faster
            INSERT  #ImpactedRuns0
            SELECT  TOP (@batchSizeMax)
                    TestRunId
            FROM    AnalyticsStage.tbl_TestResult WITH (INDEX (CI_tbl_TestResult))
            WHERE   PartitionId = @partitionId
                    AND TestRunId > @prevRunId
                    AND TestRunId < @maxRunId -- prevent runaway scans for split ranges
                    AND AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            GROUP BY TestRunId
            ORDER BY TestRunId
            OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @endState = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 'skip' ELSE '' END -- if we exhausted the run ids in this range, switch back to other index
            SET @complete = 0
        END
        ELSE
        BEGIN -- retransform
            -- getting TestRunId alone from clustered index is much faster
            INSERT  #ImpactedRuns0
            SELECT  TOP (@batchSizeMax)
                    TestRunId
            FROM    AnalyticsStage.tbl_TestResult WITH (INDEX (CI_tbl_TestResult))
            WHERE   PartitionId = @partitionId
                    AND TestRunId > ISNULL(@stateData, -1)
                    AND AnalyticsBatchIdCreated <= @triggerBatchIdEnd
            GROUP BY TestRunId
            ORDER BY TestRunId
            OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endState = ''
        END

        -- now flesh out the DataSourceId
        INSERT  #TestRun (TestRunId, DataSourceId)
        SELECT  r.TestRunId,
                r.DataSourceId
        FROM    #ImpactedRuns0 ir
        INNER LOOP JOIN AnalyticsStage.tbl_TestResult r WITH (INDEX (CI_tbl_TestResult))
        ON      r.TestRunId = ir.TestRunId
        WHERE   r.PartitionId = @partitionId
                AND r.AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        GROUP BY r.TestRunId, r.DataSourceId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        DROP TABLE #ImpactedRuns0
    END

    SET @endStateData = ISNULL((SELECT MAX(TestRunId) FROM #TestRun), @stateData)

    UPDATE  t
    SET     ResultCount                     = s.ResultCount,
            ResultPassCount                 = s.ResultPassCount,
            ResultFailCount                 = s.ResultFailCount,
            ResultDurationSeconds           = s.ResultDurationSeconds,
            ResultOutcomeNoneCount          = s.ResultOutcomeNoneCount,
            ResultOutcomePassedCount        = s.ResultOutcomePassedCount,
            ResultOutcomeFailedCount        = s.ResultOutcomeFailedCount,
            ResultOutcomeInconclusiveCount  = s.ResultOutcomeInconclusiveCount,
            ResultOutcomeTimeoutCount       = s.ResultOutcomeTimeoutCount,
            ResultOutcomeAbortedCount       = s.ResultOutcomeAbortedCount,
            ResultOutcomeBlockedCount       = s.ResultOutcomeBlockedCount,
            ResultOutcomeNotExecutedCount   = s.ResultOutcomeNotExecutedCount,
            ResultOutcomeWarningCount       = s.ResultOutcomeWarningCount,
            ResultOutcomeErrorCount         = s.ResultOutcomeErrorCount,
            ResultOutcomeNotApplicableCount = s.ResultOutcomeNotApplicableCount,
            ResultOutcomeNotImpactedCount   = s.ResultOutcomeNotImpactedCount
    FROM    #TestRun t
    CROSS APPLY
    (
        SELECT  COUNT(*) AS ResultCount,
                ISNULL(SUM(IIF(Outcome IN (2), 1, 0)), 0) AS ResultPassCount,
                ISNULL(SUM(IIF(Outcome IN (3, 5, 6, 7, 10), 1, 0)), 0) AS ResultFailCount,
                ISNULL(SUM(DATEDIFF_BIG(millisecond, DateStarted, DateCompleted) / 1000.0), 0.0) AS ResultDurationSeconds,
                ISNULL(SUM(IIF(Outcome = 1, 1, 0)), 0) AS ResultOutcomeNoneCount,
                ISNULL(SUM(IIF(Outcome = 2, 1, 0)), 0) AS ResultOutcomePassedCount,
                ISNULL(SUM(IIF(Outcome = 3, 1, 0)), 0) AS ResultOutcomeFailedCount,
                ISNULL(SUM(IIF(Outcome = 4, 1, 0)), 0) AS ResultOutcomeInconclusiveCount,
                ISNULL(SUM(IIF(Outcome = 5, 1, 0)), 0) AS ResultOutcomeTimeoutCount,
                ISNULL(SUM(IIF(Outcome = 6, 1, 0)), 0) AS ResultOutcomeAbortedCount,
                ISNULL(SUM(IIF(Outcome = 7, 1, 0)), 0) AS ResultOutcomeBlockedCount,
                ISNULL(SUM(IIF(Outcome = 8, 1, 0)), 0) AS ResultOutcomeNotExecutedCount,
                ISNULL(SUM(IIF(Outcome = 9, 1, 0)), 0) AS ResultOutcomeWarningCount,
                ISNULL(SUM(IIF(Outcome = 10, 1, 0)), 0) AS ResultOutcomeErrorCount,
                ISNULL(SUM(IIF(Outcome = 11, 1, 0)), 0) AS ResultOutcomeNotApplicableCount,
                ISNULL(SUM(IIF(Outcome = 14, 1, 0)), 0) AS ResultOutcomeNotImpactedCount
        FROM    AnalyticsStage.tbl_TestResult r
        WHERE   PartitionId = @partitionId
                AND TestRunId = t.TestRunId
                AND DataSourceId = t.DataSourceId
    ) s
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    UPDATE  t
    SET     AnalyticsUpdatedDate            = @batchDt,
            AnalyticsBatchId                = @batchId,
            ResultDurationSeconds           = s.ResultDurationSeconds,
            ResultCount                     = s.ResultCount,
            ResultPassCount                 = s.ResultPassCount,
            ResultFailCount                 = s.ResultFailCount,
            ResultOutcomeNoneCount          = s.ResultOutcomeNoneCount,
            ResultOutcomePassedCount        = s.ResultOutcomePassedCount,
            ResultOutcomeFailedCount        = s.ResultOutcomeFailedCount,
            ResultOutcomeInconclusiveCount  = s.ResultOutcomeInconclusiveCount,
            ResultOutcomeTimeoutCount       = s.ResultOutcomeTimeoutCount,
            ResultOutcomeAbortedCount       = s.ResultOutcomeAbortedCount,
            ResultOutcomeBlockedCount       = s.ResultOutcomeBlockedCount,
            ResultOutcomeNotExecutedCount   = s.ResultOutcomeNotExecutedCount,
            ResultOutcomeWarningCount       = s.ResultOutcomeWarningCount,
            ResultOutcomeErrorCount         = s.ResultOutcomeErrorCount,
            ResultOutcomeNotApplicableCount = s.ResultOutcomeNotApplicableCount,
            ResultOutcomeNotImpactedCount   = s.ResultOutcomeNotImpactedCount
    FROM    #TestRun AS s
    INNER LOOP JOIN AnalyticsModel.tbl_TestRun AS t
    ON      t.TestRunId = s.TestRunId
            AND t.DataSourceId = s.DataSourceId
    WHERE   t.PartitionId = @partitionId
            AND NOT EXISTS (
                SELECT
                s.ResultDurationSeconds,
                s.ResultCount,
                s.ResultPassCount,
                s.ResultFailCount,
                s.ResultOutcomeNoneCount,
                s.ResultOutcomePassedCount,
                s.ResultOutcomeFailedCount,
                s.ResultOutcomeInconclusiveCount,
                s.ResultOutcomeTimeoutCount,
                s.ResultOutcomeAbortedCount,
                s.ResultOutcomeBlockedCount,
                s.ResultOutcomeNotExecutedCount,
                s.ResultOutcomeWarningCount,
                s.ResultOutcomeErrorCount,
                s.ResultOutcomeNotApplicableCount,
                s.ResultOutcomeNotImpactedCount
                INTERSECT
                SELECT
                t.ResultDurationSeconds,
                t.ResultCount,
                t.ResultPassCount,
                t.ResultFailCount,
                t.ResultOutcomeNoneCount,
                t.ResultOutcomePassedCount,
                t.ResultOutcomeFailedCount,
                t.ResultOutcomeInconclusiveCount,
                t.ResultOutcomeTimeoutCount,
                t.ResultOutcomeAbortedCount,
                t.ResultOutcomeBlockedCount,
                t.ResultOutcomeNotExecutedCount,
                t.ResultOutcomeWarningCount,
                t.ResultOutcomeErrorCount,
                t.ResultOutcomeNotApplicableCount,
                t.ResultOutcomeNotImpactedCount
                )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    DROP TABLE #TestRun

    RETURN 0
END

GO

