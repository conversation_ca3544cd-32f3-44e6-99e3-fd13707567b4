CREATE TABLE [AnalyticsInternal].[tbl_Batch] (
    [PartitionId]                      INT             NOT NULL,
    [BatchId]                          BIGINT          NOT NULL,
    [TableName]                        VARCHAR (64)    NOT NULL,
    [AnalyticsProviderShardId]         INT             NULL,
    [AnalyticsStreamId]                INT             NULL,
    [Operation]                        VARCHAR (10)    NOT NULL,
    [OperationSproc]                   VARCHAR (100)   NULL,
    [OperationState]                   VARCHAR (10)    NULL,
    [OperationStateData]               BIGINT          NULL,
    [OperationActive]                  BIT             NOT NULL,
    [Ready]                            BIT             NOT NULL,
    [InsertedCount]                    INT             NULL,
    [UpdatedCount]                     INT             NULL,
    [DeletedCount]                     INT             NULL,
    [Failed]                           BIT             NOT NULL,
    [CreateDateTime]                   DATETIME        NOT NULL,
    [ReadyDateTime]                    DATETIME        NULL,
    [OperationTriggerTableName]        VARCHAR (64)    NULL,
    [OperationSubBatchCount]           INT             NULL,
    [OperationDurationMS]              INT             NULL,
    [FailedMessage]                    NVARCHAR (1000) NULL,
    [OperationSprocVersion]            INT             NULL,
    [OperationTriggerBatchIdStart]     BIGINT          NULL,
    [OperationTriggerBatchIdEnd]       BIGINT          NULL,
    [Inserted]                         BIT             NULL,
    [Updated]                          BIT             NULL,
    [Deleted]                          BIT             NULL,
    [Invalidated]                      BIT             NULL,
    [AttemptCount]                     INT             NULL,
    [FailedCount]                      INT             NULL,
    [OperationPriority]                INT             NULL,
    [ReworkAttemptCount]               INT             NULL,
    [ReworkBatchId]                    BIGINT          NULL,
    [LastFailedOperationSubBatchCount] INT             NULL
);


GO

CREATE NONCLUSTERED INDEX [IX_AnalyticsInternal_tbl_Batch_NotReadyNotFailedProcess]
    ON [AnalyticsInternal].[tbl_Batch]([PartitionId] ASC, [TableName] ASC) WHERE ([Ready]=(0) AND [Failed]=(0) AND [OperationTriggerTableName] IS NOT NULL);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_AnalyticsInternal_tbl_Batch]
    ON [AnalyticsInternal].[tbl_Batch]([PartitionId] ASC, [BatchId] ASC);


GO

