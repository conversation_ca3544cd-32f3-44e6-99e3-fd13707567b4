/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 554668EE0063084B12CECCDCB513451B08F83EF5
--=================================
--Check all cold start staged data are available in model
--=================================
CREATE PROCEDURE AnalyticsInternal.prc_DQ_WorkItemRevision_ModelReady
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    DECLARE @now DATETIME = GETUTCDATE();

    --All shards must be loaded but within a shard, at least one stream has to be loaded
    IF NOT EXISTS
    (
        SELECT      TOP 1 *
        FROM        AnalyticsInternal.tbl_TableProviderShardStream
        WHERE       PartitionId = @partitionId
                    AND TableName = 'WorkItemRevision'
    )
    OR EXISTS
    (
        SELECT      AnalyticsProviderShardId, MAX(LoadedTime)
        FROM        AnalyticsInternal.tbl_TableProviderShardStream
        WHERE       PartitionId = @partitionId
                    AND TableName = 'WorkItemRevision'
        GROUP BY    AnalyticsProviderShardId
        HAVING      MAX(LoadedTime) IS NULL
    )
    BEGIN

        EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
                @partitionId,
                @now, --@runDate
                @now, --@startDate
                @now, --@endDate
                @name,
                'Model.WorkItem', --@scope
                'Model.WorkItem', --@targetTable
                0, --@expectedValue
                0, --@actualValue
                -99999999, --@kpiValue
                1 --@failed
    END
    ELSE
    BEGIN

        DECLARE @maxLoadedTime DATETIME
        SELECT  @maxLoadedTime = MAX(LoadedTime)
        FROM    AnalyticsInternal.tbl_TableProviderShardStream
        WHERE   PartitionId = @partitionId
                AND TableName = 'WorkItemRevision'
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        DECLARE @exclusionTime DATETIME
        SELECT  @exclusionTime = DATEADD(SECOND, -1 * @latencyExclusionSeconds, @now)

        DECLARE @compareStartDate DATETIME = '1900-01-01'
        DECLARE @compareEndDate DATETIME = IIF(@maxLoadedTime > @exclusionTime, @maxLoadedTime, @exclusionTime)
        DECLARE @expectedCount BIGINT, @actualCount BIGINT
        DECLARE @kpiValue FLOAT
        DECLARE @failed BIT
        DECLARE @result AnalyticsInternal.typ_DataQualityResult3

        EXEC AnalyticsInternal.prc_WorkItemRevision_StageModelCountDiff
            @partitionId,
            @compareStartDate,
            @compareEndDate,
            @expectedCount OUTPUT,
            @actualCount OUTPUT,
            @kpiValue OUTPUT,
            @failed OUTPUT

        EXEC AnalyticsInternal.prc_iRecordDataQualitySingle
            @partitionId,
            @now,
            @compareStartDate,
            @compareEndDate,
            @name,
            'Model.WorkItem',
            'Model.WorkItem',
            @expectedCount,
            @actualCount,
            @kpiValue,
            @failed

    END

    RETURN 0
END

GO

