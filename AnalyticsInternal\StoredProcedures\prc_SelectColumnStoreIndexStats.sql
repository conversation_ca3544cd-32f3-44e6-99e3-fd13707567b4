/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B7F3A228F051903733530FFE2771E5AF42C97DDC
CREATE PROCEDURE AnalyticsInternal.prc_SelectColumnStoreIndexStats
    @physicalPartitionId INT,
    @columnStoreIndexName NVARCHAR(128),
    @action NVARCHAR(128)
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    SELECT  DB_NAME() AS DBName,
            @action AS [Action],
            i.name,
            partition_number,
            row_group_id,
            total_rows, deleted_rows, size_in_bytes, state_desc, trim_reason_desc, transition_to_compressed_state_desc,
            CONVERT(INT, 100 * (ISNULL(deleted_rows, 0)) / total_rows) AS fragmentation
    FROM    sys.indexes(NOLOCK) AS i
    JOIN    sys.dm_db_column_store_row_group_physical_stats(NOLOCK) AS CSRowGroups
            ON i.object_id = CSRowGroups.object_id
            AND i.index_id = CSRowGroups.index_id
    WHERE   total_rows > 0
            AND state IN ( 1 , 3)
            AND (@physicalPartitionId IS NULL OR partition_number = @physicalPartitionId)
            AND name = @columnStoreIndexName
    ORDER BY i.name, state_desc, fragmentation DESC
END

GO

