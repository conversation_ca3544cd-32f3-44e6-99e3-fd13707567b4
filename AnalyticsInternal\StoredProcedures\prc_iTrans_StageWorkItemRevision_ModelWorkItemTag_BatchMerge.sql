/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 1BF04325064A5B82A003A33C7D66BE940AA540A2
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_ModelWorkItemTag_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 20000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)
    DECLARE @triggerWorkItemId TABLE
    (
        System_Id INT NOT NULL PRIMARY KEY,
        [Count] INT NOT NULL
    )

    IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
                    AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = 0
        SET @endState = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 'dense', '')
    END
    ELSE -- use NCI
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 0, 1)
        SET @endState = IIF(@complete = 0 AND @endStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
    END

    CREATE TABLE #Src
    (
        WorkItemRevisionSK INT,
        TagSK INT,
        UNIQUE CLUSTERED (WorkItemRevisionSK, TagSK)
    )

    IF (@triggerBatchIdStart <= 1) -- use different filter to encourage simpler plan
    BEGIN
        DECLARE @triggerWorkItemIdMin INT = (SELECT MIN(System_Id) FROM @triggerWorkItemId)
        DECLARE @triggerWorkItemIdMax INT = @endStateData

        ;WITH RevTags AS
        (
            SELECT  r.PartitionId,
                    r.System_ProjectGuid,
                    rd.WorkItemRevisionSK,
                    TagIds.x.value('(TagId/text())[1]','nvarchar(256)') AS TagId
            FROM    AnalyticsStage.tbl_WorkItemRevision r
            INNER LOOP JOIN AnalyticsInternal.vw_WorkItemRevisionSK rd
            ON      rd.PartitionId = r.PartitionId
                    AND rd.WorkItemId = r.System_Id
                    AND rd.Revision = r.System_Rev
            OUTER APPLY Tags.nodes('//Item') AS TagIds(x)
            WHERE   r.PartitionId = @partitionId
                    AND r.System_Id BETWEEN @triggerWorkItemIdMin AND @triggerWorkItemIdMax
                    AND r.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
                    AND r.Tags IS NOT NULL
        )
        INSERT  #Src (WorkItemRevisionSK, TagSK)
        SELECT  r.WorkItemRevisionSK,
                t.TagSK
        FROM    RevTags r
        LEFT JOIN AnalyticsModel.tbl_Tag t
        ON      r.PartitionId = t.PartitionId
                AND r.TagId = t.TagId
                AND r.System_ProjectGuid = t.ProjectSK
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        ;WITH RevTags AS
        (
            SELECT  r.PartitionId,
                    r.System_ProjectGuid,
                    rd.WorkItemRevisionSK,
                    TagIds.x.value('(TagId/text())[1]','nvarchar(256)') AS TagId
            FROM    @triggerWorkItemId tid
            INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision r
            ON      r.System_Id = tid.System_Id
            INNER LOOP JOIN AnalyticsInternal.vw_WorkItemRevisionSK rd
            ON      rd.PartitionId = r.PartitionId
                    AND rd.WorkItemId = r.System_Id
                    AND rd.Revision = r.System_Rev
            OUTER APPLY Tags.nodes('//Item') AS TagIds(x)
            WHERE   r.PartitionId = @partitionId
                    AND r.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND r.Tags IS NOT NULL
        )
        INSERT  #Src (WorkItemRevisionSK, TagSK)
        SELECT  r.WorkItemRevisionSK,
                t.TagSK
        FROM    RevTags r
        LEFT JOIN AnalyticsModel.tbl_Tag t
        ON      r.PartitionId = t.PartitionId
                AND r.TagId = t.TagId
                AND r.System_ProjectGuid = t.ProjectSK
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    CREATE TABLE #Existing
    (
        WorkItemRevisionSK INT,
        TagSK INT,
        UNIQUE CLUSTERED (WorkItemRevisionSK, TagSK)
    )

    INSERT  #Existing
    SELECT  t.WorkItemRevisionSK,
            t.TagSK
    FROM    AnalyticsModel.tbl_WorkItemTag t
    WHERE   t.PartitionId = @partitionId
            AND t.WorkItemRevisionSK IN (SELECT WorkItemRevisionSK FROM #Src)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF (@@ROWCOUNT = 0)
    BEGIN
        INSERT  AnalyticsModel.tbl_WorkItemTag
                (
                PartitionId,
                AnalyticsBatchId,
                AnalyticsCreatedDate,
                AnalyticsUpdatedDate,
                WorkItemRevisionSK,
                TagSK
                )
        SELECT  @partitionId,
                @batchId,
                @batchDt,
                @batchDt,
                s.WorkItemRevisionSK,
                s.TagSK
        FROM    #Src s
        WHERE   s.TagSK IS NOT NULL

        SET @insertedCount = @@ROWCOUNT
        SET @deletedCount = 0
    END
    ELSE
    BEGIN
        DELETE  e
        FROM    #Existing e
        JOIN    #Src s
        ON      e.WorkItemRevisionSK = s.WorkItemRevisionSK
                AND e.TagSK = s.TagSK

        BEGIN TRAN
            INSERT  AnalyticsModel.tbl_WorkItemTag
                    (
                    PartitionId,
                    AnalyticsBatchId,
                    AnalyticsCreatedDate,
                    AnalyticsUpdatedDate,
                    WorkItemRevisionSK,
                    TagSK
                    )
            SELECT  @partitionId,
                    @batchId,
                    @batchDt,
                    @batchDt,
                    s.WorkItemRevisionSK,
                    s.TagSK
            FROM    #Src s
            LEFT LOOP JOIN AnalyticsModel.tbl_WorkItemTag t WITH (INDEX (PK_tbl_WorkItemTag)) -- avoid bad plan, this index will match fewer rows in model
            ON      t.PartitionID = @partitionId
                    AND t.WorkItemRevisionSK = s.WorkItemRevisionSK
                    AND t.TagSK = s.TagSK
            WHERE   t.PartitionId IS NULL
                    AND s.TagSK IS NOT NULL
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

            SET @insertedCount = @@ROWCOUNT

            DELETE  t
            FROM    AnalyticsModel.tbl_WorkItemTag t
            JOIN    #Existing e
            ON      t.WorkItemRevisionSK = e.WorkItemRevisionSK
                    AND t.TagSK = e.TagSK
            WHERE   t.PartitionId = @partitionId
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @deletedCount = @@ROWCOUNT
        COMMIT TRAN
    END

    DROP TABLE #Src
    DROP TABLE #Existing

    RETURN 0
END

GO

