/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 2AB2E6F68C34DE8E0382AF5A70F06B1A69F3EC91
CREATE PROCEDURE AnalyticsInternal.prc_iRecordDataQualitySingle(
    @partitionId INT,
    @runDate DATETIME2,
    @startDate DATETIME2,
    @endDate DATETIME2,
    @name VARCHAR(256),
    @scope VARCHAR(256),
    @targetTable VARCHAR(64),
    @expectedValue BIGINT,
    @actualValue BIGINT,
    @kpiValue FLOAT,
    @failed BIT
    )
AS
BEGIN
    DECLARE @runEndDate DATETIME = GETUTCDATE()

    BEGIN TRANSACTION

    -- Unset existing latest result
    UPDATE  AnalyticsInternal.tbl_DataQualityResult
    SET     Latest = 0
    WHERE   PartitionId = @partitionId
            AND Name = @name
            AND TargetTable = @targetTable
            AND EXISTS (
                SELECT scope
                    INTERSECT
                SELECT @scope)
            AND Latest = 1

    -- Insert as latest (assumption: new result always newer than existing results)
    INSERT INTO AnalyticsInternal.tbl_DataQualityResult(PartitionId, RunDate, RunEndDate, StartDate, EndDate, Name, Scope, TargetTable, ExpectedValue, ActualValue, Failed, KpiValue, Latest)
    SELECT  @partitionId,
            @runDate,
            @runEndDate,
            @startDate,
            @endDate,
            @name,
            @scope,
            @targetTable,
            @expectedValue,
            @actualValue,
            @failed,
            @kpiValue,
            1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    COMMIT

    SELECT  @partitionId AS PartitionId,
            @name AS Name,
            ISNULL(@scope, @targetTable) AS Scope,
            @targetTable AS TargetTable,
            @runDate AS RunDate,
            @runEndDate AS RunEndDate,
            @startDate AS StartDate,
            @endDate AS EndDate,
            @expectedValue AS ExpectedValue,
            @actualValue AS ActualValue,
            @kpiValue AS KpiValue,
            @failed AS Failed

    RETURN 0

END

GO

