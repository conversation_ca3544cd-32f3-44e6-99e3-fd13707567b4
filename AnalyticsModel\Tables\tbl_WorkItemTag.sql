CREATE TABLE [AnalyticsModel].[tbl_WorkItemTag] (
    [PartitionId]          INT      NOT NULL,
    [AnalyticsCreatedDate] DATETIME NOT NULL,
    [AnalyticsUpdatedDate] DATETIME NOT NULL,
    [AnalyticsBatchId]     BIGINT   NOT NULL,
    [WorkItemRevisionSK]   INT      NOT NULL,
    [TagSK]                INT      NOT NULL
);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_WorkItemTag_TagSK]
    ON [AnalyticsModel].[tbl_WorkItemTag]([PartitionId] ASC, [TagSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_WorkItemTag]
    ON [AnalyticsModel].[tbl_WorkItemTag]([PartitionId] ASC, [WorkItemRevisionSK] ASC, [TagSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

