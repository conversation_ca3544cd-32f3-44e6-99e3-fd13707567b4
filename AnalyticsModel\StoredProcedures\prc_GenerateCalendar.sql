/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FFCFB7ED4024B9AFA298153B5C52AA249A8FF697
CREATE PROCEDURE AnalyticsModel.prc_GenerateCalendar
    @partitionId    INT,
    @cultureName    VARCHAR(256),--short culture name Ex: en-US
    @LCID           INT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON
    BEGIN TRANSACTION

    BEGIN TRY

        DECLARE @StartYear      INT = 2010

        DECLARE @newDateFirst INT = AnalyticsInternal.func_GetDateFirstFromLCID(@LCID)
        SET DATEFIRST @newDateFirst

        DECLARE @StartDate DATE = DATEFROMPARTS(@StartYear, 1, 1)
        DECLARE @Now DATE = GETUTCDATE()
        DECLARE @EndDate DATE = DATEFROMPARTS(YEAR(@Now) + 1,12, 31)  -- Generate calendar til the end of the next Calendar year.
        DECLARE @DaysCount INT = DATEDIFF(day, @StartDate, @EndDate) + 1

        -- Clean up previous version
        DELETE FROM AnalyticsModel.tbl_Date WHERE PartitionId = @partitionId

        --Get the collection time zone
        DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

        -- Create numbers table with stacked CTEs
        -- More details http:
        ;WITH e1(n) AS
        (
            SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL
            SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL
            SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1 UNION ALL SELECT 1
        ), -- 10
        e2(n) AS (SELECT 1 FROM e1 CROSS JOIN e1 AS b), -- 10*10
        e3(n) AS (SELECT 1 FROM e1 CROSS JOIN e2), -- 10*100
        e4(n) AS (SELECT 1 FROM e2 CROSS JOIN e3), -- 10*100*100
        -- Take only required numbers
        Numbers(n) AS (SELECT TOP(@DaysCount) n = ROW_NUMBER() OVER (ORDER BY n) FROM e4 ORDER BY n),
        -- Generates list of dates
        Dates([Date]) AS (SELECT DATEADD(day, n-1, @StartDate) AS [Date] FROM Numbers)
        INSERT INTO AnalyticsModel.tbl_Date
        (
            PartitionId,
            DateSK,
            Date,
            Year,
            MonthOfYear,
            DayOfYear,
            MonthName,
            MonthShortName,
            Month,
            DayOfWeek,
            DayName,
            DayShortName,
            WeekStartingDate,
            WeekEndingDate,
            YearMonth,
            IsLastDayOfPeriod,
            DayOfMonth
        )
        SELECT
            @PartitionId AS PartitionId,
            AnalyticsInternal.func_GenDateSK([Date]) AS DateSK,
            CAST([Date] AS DATETIME) AT TIME ZONE @timeZone AS [DATE],
            YEAR([Date]) AS Year,
            MONTH([Date]) AS MonthOfYear,
            DATEPART(dy, [Date]) AS DayOfYear,
            FORMAT([Date],'MMMM', @cultureName) AS MonthName,
            FORMAT([Date],'MMM', @cultureName) AS MonthShortName,
            FORMAT([Date],'MMM', @cultureName) +' '+CAST(YEAR([Date]) AS nchar(4)) AS Month,
            DATEPART(weekday,[Date]) AS DayOfWeek, -- We need to add culture specific logic
            FORMAT([Date],'dddd', @cultureName) AS DayName,
            FORMAT([Date],'ddd', @cultureName) AS DayShortName,
            CAST(DATEADD(dd, -(DATEPART(dw, [Date])-1), [Date]) AS DATETIME) AT TIME ZONE @timeZone AS WeekStartingDate,
            CAST(DATEADD(dd, 7-(DATEPART(dw, [Date])), [Date]) AS DATETIME) AT TIME ZONE @timeZone AS WeekEndingDate,
            YEAR([Date])* 100 + MONTH([Date]) AS YearMonth,
            AnalyticsInternal.func_GetIsLastRevisionOfPeriod([Date], DATEADD(DAY, 1, [Date])) AS IsLastDayOfPeriod,
            DAY([Date]) AS DayOfMonth
        FROM Dates d
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        COMMIT TRANSACTION
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        THROW
    END CATCH
END

GO

