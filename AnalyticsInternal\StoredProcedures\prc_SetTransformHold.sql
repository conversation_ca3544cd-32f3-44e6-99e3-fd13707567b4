/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 4B9E24E06EB21037DEB8CA432675A470EC496737
CREATE PROCEDURE AnalyticsInternal.prc_SetTransformHold
    @hold BIT,
    @reason NVARCHAR(255),
    @targetTableName VARCHAR(64) = NULL,
    @firstPartitionId INT = NULL,
    @lastPartitionId INT = NULL,
    @wait BIT = 1 -- Wait for active transforms to end by default
AS
BEGIN
    EXEC AnalyticsInternal.prc_iSetTransformHold @hold, @reason, @targetTableName, @firstPartitionId, @lastPartitionId, @wait

    RETURN 0
END

GO

