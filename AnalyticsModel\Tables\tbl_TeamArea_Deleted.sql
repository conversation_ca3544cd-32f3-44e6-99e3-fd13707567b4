CREATE TABLE [AnalyticsModel].[tbl_TeamArea_Deleted] (
    [PartitionId]             INT              NOT NULL,
    [AnalyticsDeletedDate]    DATETIME         NOT NULL,
    [AnalyticsBatchIdDeleted] BIGINT           NOT NULL,
    [TeamSK]                  UNIQUEIDENTIFIER NOT NULL,
    [AreaSK]                  UNIQUEIDENTIFIER NOT NULL
);


GO

CREATE NONCLUSTERED INDEX [IX_AnalyticsModel_tbl_TeamArea_AxBatchIdDeleted]
    ON [AnalyticsModel].[tbl_TeamArea_Deleted]([PartitionId] ASC, [AnalyticsBatchIdDeleted] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE CLUSTERED INDEX [CI_AnalyticsModel_tbl_TeamArea_Deleted]
    ON [AnalyticsModel].[tbl_TeamArea_Deleted]([PartitionId] ASC, [TeamSK] ASC, [AreaSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

