/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B4E3F9E5836A9808292CE50B6CB9E31672EC2963
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Any_ModelBoardLocation_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)

    -- I select the teams that have changed in this batch
    CREATE TABLE #ChangedTeams
    (
        TeamId UNIQUEIDENTIFIER,
        PRIMARY KEY CLUSTERED (TeamId)
    )

    IF (@triggerTableName = 'KanbanBoardColumn')
    BEGIN
        INSERT #ChangedTeams
        SELECT DISTINCT TeamId
        FROM AnalyticsStage.tbl_KanbanBoardColumn
        WHERE PartitionId = @partitionId
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END
    ELSE IF (@triggerTableName = 'KanbanBoardRow')
    BEGIN
        INSERT #ChangedTeams
        SELECT DISTINCT TeamId
        FROM AnalyticsStage.tbl_KanbanBoardRow
        WHERE PartitionId = @partitionId
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END
    ELSE IF (@triggerTableName = 'TeamSetting')
    BEGIN
        INSERT #ChangedTeams
        SELECT DISTINCT TeamGuid
        FROM AnalyticsStage.tbl_TeamSetting
        WHERE PartitionId = @partitionId
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END

    CREATE TABLE #TeamBoards
    (
        TeamId UNIQUEIDENTIFIER,
        BoardId UNIQUEIDENTIFIER NULL,
        INDEX CL_TeamBoards CLUSTERED (TeamId, BoardId)
    )

    -- Populate all boards for the affected team(s). Including teams with no boards
    INSERT INTO #TeamBoards (TeamId, BoardId)
    SELECT DISTINCT ct.TeamId, c.BoardId
    FROM #ChangedTeams ct
    LEFT JOIN AnalyticsStage.tbl_KanbanBoardColumn c
        ON c.PartitionId = @partitionId
        AND ct.TeamId = c.TeamId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    DECLARE @localizedDefaultLane   NVARCHAR(230)

    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_DEFAULT_LANE',
                                                            @defaultValue = 'Default Lane',
                                                            @localizedValue =  @localizedDefaultLane OUTPUT

    CREATE TABLE #BoardRow
    (
        BoardId UNIQUEIDENTIFIER,
        Id UNIQUEIDENTIFIER,
        Name NVARCHAR(256) COLLATE Latin1_General_100_CI_AS,
        [Order] INT,
        IsDeleted BIT,
        IsDefault BIT,
        ChangedDate DATETIMEOFFSET,
        RevisedDate DATETIMEOFFSET,
        IsVirtual BIT,
        PRIMARY KEY CLUSTERED (BoardId, Name, ChangedDate)
    )

    -- I select the matching rows for the board
    -- with the changed date based on all the staged row revisions
    ;WITH StageRows AS
    (
        SELECT r.*, ISNULL(r.Name, IIF(r.Id = '00000000-0000-0000-0000-000000000000', @localizedDefaultLane, NULL)) AS FixedName
        FROM AnalyticsStage.tbl_KanbanBoardRow r
        JOIN #TeamBoards tb
            ON tb.TeamId = r.TeamId
            AND tb.BoardId = r.BoardId
        WHERE PartitionId = @partitionId
    )
    INSERT #BoardRow
    SELECT
        r.BoardId,
        Id,
        FixedName,
        [Order],
        IsDeleted,
        IsDefault,
        --do not use tbl_KanbanBoardRow.ChangedDate directly here because this logic handles lane 1 deleted and lane 2 re-using the name.
        ISNULL(LAG(RevisedDate) OVER (PARTITION BY r.PartitionId, r.BoardId, r.FixedName ORDER BY RevisedDate), '1900-01-01') AS ChangedDate,
        ISNULL(RevisedDate, '9999-01-01') as RevisedDate,
        0 as IsVirtual
    FROM StageRows r
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    -- I ensure a default lane exists for board
    INSERT #BoardRow
    SELECT
        tb.BoardId,
        '00000000-0000-0000-0000-000000000000' as Id,
        @localizedDefaultLane as Name,
        NULL as [Order],
        NULL as IsDeleted,
        1 as IsDefault,
        '1900-01-01' as ChangedDate,
        '9999-01-01' as RevisedDate,
        0 as IsVirtual
    FROM #TeamBoards tb
    LEFT JOIN #BoardRow r
        ON r.BoardId = tb.BoardId
        AND r.Id = '00000000-0000-0000-0000-000000000000'
    WHERE tb.BoardId IS NOT NULL
        AND r.BoardId IS NULL

    -- For renamed or deleted lanes keep their names forever, because WEF could still reference them for last column
    INSERT #BoardRow
    SELECT
        BoardId,
        Id,
        Name,
        [Order],
        IsDeleted,
        IsDefault,
        RevisedDate AS ChangedDate,
        ISNULL(NextStart, '9999-01-01') as RevisedDate,
        1 as IsVirtual
    FROM
    (
        SELECT  *,
                LEAD(ChangedDate) OVER (PARTITION BY BoardId, Name ORDER BY RevisedDate) AS NextStart
        FROM    #BoardRow
    ) T
    WHERE (RevisedDate != '9999-01-01' AND NextStart IS NULL) --don't have now
        OR (RevisedDate != NextStart) -- cover a gap when lane was renamed to something else and than renamed back (or new lane with the same name was created)

    -- I select the backlog categories (board settings) for the team
    CREATE TABLE #BacklogCategory
    (
        TeamGuid UNIQUEIDENTIFIER,
        ProjectGuid UNIQUEIDENTIFIER,
        BoardCategoryReferenceName NVARCHAR(70) COLLATE DATABASE_DEFAULT,
        BoardName NVARCHAR(256) COLLATE DATABASE_DEFAULT,
        BoardLevel INT NULL,
        IsBoardVisible BIT,
        BacklogType NVARCHAR(50),
        UNIQUE CLUSTERED (TeamGuid, BoardCategoryReferenceName)
    )

    -- TODO: Should reduce the select down to teams matching those from the changed boards
    -- I use ISNULL to handle previous version of XML that only contained Name and was sparse
    -- The new format of the XML is non-sparse and contains staged fields.
    INSERT INTO #BacklogCategory (TeamGuid, ProjectGuid, BoardCategoryReferenceName, BoardName, BoardLevel, IsBoardVisible, BacklogType)
    SELECT
        ts.TeamGuid,
        ts.ProjectGuid,
        ISNULL(bc.Item.value('CategoryReferenceName[1]','nvarchar(70)'), bc.Item.value('SingularName[1]','nvarchar(70)')),      -- BoardCategoryReferenceName
        ISNULL(NULLIF(bc.Item.value('PluralName[1]','nvarchar(256)'), ''), bc.Item.value('SingularName[1]','nvarchar(70)')),    -- BoardName
        CASE WHEN bc.Item.exist('Level[1]') = 1 THEN bc.Item.value('Level[1]','int') ELSE NULL END,    -- BoardLevel
        ISNULL(bc.Item.value('IsVisible[1]', 'bit'), 0),    -- IsVisible
        bc.Item.value('BacklogType[1]', 'nvarchar(50)')     -- BacklogType
    FROM AnalyticsStage.tbl_TeamSetting ts
    CROSS APPLY ts.BacklogCategories.nodes('//Item') bc(Item)
    WHERE ts.PartitionId = @partitionId
    AND ts.TeamGuid IN (SELECT TeamId FROM #TeamBoards)
    AND bc.Item.value('CategoryReferenceName[1]','nvarchar(70)') != 'Microsoft.TaskCategory'
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), MAXDOP 1);

    CREATE TABLE #Src (
        ColumnId                    UNIQUEIDENTIFIER    NULL, -- business key
        LaneId                      UNIQUEIDENTIFIER    NULL, -- business key
        IsDone                      BIT                 NULL, -- business key
        BoardId                     UNIQUEIDENTIFIER    NULL,
        BoardExtensionId            UNIQUEIDENTIFIER    NULL,
        BoardName                   NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        ColumnName                  NVARCHAR(256)       COLLATE Latin1_General_100_CI_AS NULL,
        ColumnOrder                 INT                 NULL,
        ColumnItemLimit             INT                 NULL,
        LaneName                    NVARCHAR(256)       COLLATE Latin1_General_100_CI_AS NULL,
        LaneOrder                   INT                 NULL,
        ProjectSK                   UNIQUEIDENTIFIER    NULL,
        TeamSK                      UNIQUEIDENTIFIER    NULL,
        IsColumnSplit               BIT                 NULL,
        ChangedDate                 DATETIMEOFFSET      NULL,
        RevisedDate                 DATETIMEOFFSET      NULL,
        IsCurrent                   BIT                 NULL,
        IsDefaultLane               BIT                 NOT NULL,
        IsBoardVisible              BIT                 NOT NULL,
        BoardCategoryReferenceName  NVARCHAR(70)        COLLATE DATABASE_DEFAULT NULL,
        BoardLevel                  INT                 NULL,
        ColumnType                  INT                 NULL,
        IsVirtual                   BIT                 NULL,
        BacklogType                 NVARCHAR(50)        COLLATE DATABASE_DEFAULT NULL,
        INDEX CL_Src CLUSTERED (BoardCategoryReferenceName, ColumnId, LaneName, ChangedDate, IsDone)
    )

    -- I select the columns
    -- with the changed date based all the staged column revisions
    ;WITH BoardColumn AS
    (
        SELECT
            c.Id,
            c.ProjectId,
            c.TeamId,
            c.BoardId,
            c.BoardExtensionId,
            c.BoardCategoryReferenceName,
            c.Name,
            c.[Order],
            c.ItemLimit,
            IIF(ColumnType = 1, ISNULL(c.IsSplit, 0), 0) AS IsSplit, -- IsSplit is requried for next steps, also you couldn't split first and last columns
            ISNULL(LAG(c.RevisedDate) OVER (PARTITION BY c.PartitionId, c.BoardId, c.Id ORDER BY c.RevisedDate),
                   ISNULL(LAG(c.RevisedDate) OVER (PARTITION BY c.PartitionId, c.BoardId, c.Name ORDER BY c.RevisedDate), '1900-01-01')
                   ) AS ChangedDate,
            ISNULL(c.RevisedDate, '9999-01-01') AS RevisedDate,
            c.IsDeleted,
            c.ColumnType
        FROM AnalyticsStage.tbl_KanbanBoardColumn c
        JOIN #TeamBoards tb
            ON c.TeamId = tb.TeamId
            AND c.BoardId = tb.BoardId
        WHERE c.PartitionId = @partitionId
    )
    -- I joined the columns with the matching rows and the backlog categories as the source
    -- The changed date is the start date and the revised date is the end date of the column/lane revision
    INSERT #Src (
        ColumnId,
        ProjectSK,
        LaneId,
        IsDone,
        BoardId,
        BoardExtensionId,
        BoardCategoryReferenceName,
        ColumnName,
        ColumnOrder,
        ColumnItemLimit,
        LaneName,
        LaneOrder,
        ChangedDate,
        RevisedDate,
        IsCurrent,
        IsDefaultLane,
        BoardName,
        BoardLevel,
        IsBoardVisible,
        TeamSK,
        IsColumnSplit,
        ColumnType,
        IsVirtual,
        BacklogType
        )
    SELECT
        c.Id AS ColumnId,
        COALESCE(c.ProjectId, bc.ProjectGuid) AS ProjectSK,
        r.Id AS LaneId,
        sp.Done AS IsDone,
        c.BoardId,
        c.BoardExtensionId,
        COALESCE(c.BoardCategoryReferenceName, bc.BoardCategoryReferenceName) AS BoardCategoryReferenceName,
        c.Name AS ColumnName,
        c.[Order] AS ColumnOrder,
        c.ItemLimit AS ColumnItemLimit,
        r.Name AS LaneName,
        r.[Order] AS LaneOrder,
        CASE WHEN c.ChangedDate > r.ChangedDate THEN c.ChangedDate ELSE r.ChangedDate END AS ChangedDate,
        CASE WHEN c.RevisedDate < r.RevisedDate THEN c.RevisedDate ELSE r.RevisedDate END AS RevisedDate,
        CASE WHEN (r.IsDeleted = 1 OR r.IsVirtual = 1) THEN 0 ELSE (CASE WHEN ISNULL(c.RevisedDate, '9999-01-01') = '9999-01-01' AND ISNULL(r.RevisedDate, '9999-01-01') = '9999-01-01' THEN 1 ELSE 0 END) END AS IsCurrent,
        ISNULL(r.IsDefault, IIF(r.Id = '00000000-0000-0000-0000-000000000000', 1, 0)) AS IsDefaultLane,
        bc.BoardName AS BoardName,
        bc.BoardLevel,
        ISNULL(bc.IsBoardVisible, 0) AS IsBoardVisible,
        COALESCE(c.TeamId, bc.TeamGuid) AS TeamSK,
        c.IsSplit AS IsColumnSplit,
        c.ColumnType,
        r.IsVirtual,
        bc.BacklogType
    FROM BoardColumn c
    JOIN #BoardRow r
        ON r.BoardId = c.BoardId
        AND r.ChangedDate <= c.RevisedDate -- row starts before column ends
        AND r.RevisedDate >= c.ChangedDate -- column start before row ends
        AND (r.IsVirtual = 0 OR c.ColumnType = 2)
    JOIN (SELECT CAST(NULL AS BIT) AS Done UNION SELECT 0 UNION SELECT 1) AS sp
        ON ISNULL(c.IsSplit, 0) = 1
        OR Done IS NULL
    FULL OUTER JOIN #BacklogCategory bc -- outer join to ensure all backlogs have a placeholder, with null board info
        ON bc.TeamGuid = c.TeamId
        AND bc.BoardCategoryReferenceName = c.BoardCategoryReferenceName

    DELETE TOP (@batchSizeMax) t
    FROM AnalyticsModel.tbl_BoardLocation t
    JOIN #ChangedTeams ct
        ON t.TeamSK = ct.TeamId
    LEFT JOIN #Src s
        ON s.BoardCategoryReferenceName = t.BoardCategoryReferenceName
        AND s.TeamSK = t.TeamSK
        AND (s.ColumnId = t.ColumnId OR (s.ColumnId IS NULL AND t.ColumnId IS NULL))
        AND (s.LaneName = t.LaneName OR (s.LaneName IS NULL AND t.LaneName IS NULL))
        AND (s.ChangedDate = t.ChangedDate OR (s.ChangedDate IS NULL AND t.ChangedDate IS NULL))
        AND (s.IsDone = t.IsDone OR (s.IsDone IS NULL AND t.IsDone IS NULL))
    WHERE t.PartitionId = @partitionId
        AND s.BoardCategoryReferenceName IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    UPDATE  TOP (@batchSizeMax) t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            BoardId = s.BoardId,
            BoardExtensionId = s.BoardExtensionId,
            BoardCategoryReferenceName = s.BoardCategoryReferenceName,
            BoardName = s.BoardName,
            BoardLevel = s.BoardLevel,
            IsBoardVisible = s.IsBoardVisible,
            ColumnName = s.ColumnName,
            ColumnOrder = s.ColumnOrder,
            ColumnItemLimit = s.ColumnItemLimit,
            LaneId = s.LaneId,
            LaneName = s.LaneName,
            LaneOrder = s.LaneOrder,
            TeamSK = s.TeamSK,
            ProjectSK = s.ProjectSK,
            IsColumnSplit = s.IsColumnSplit,
            RevisedDate = s.RevisedDate,
            IsCurrent = s.IsCurrent,
            IsDefaultLane = s.IsDefaultLane,
            IsVirtual = s.IsVirtual,
            BacklogType = s.BacklogType
    FROM    AnalyticsModel.tbl_BoardLocation t WITH (INDEX (UQ_tbl_BoardLocation_BoardColumnId))
    JOIN    #Src AS s
    ON      s.BoardCategoryReferenceName = t.BoardCategoryReferenceName
            AND s.TeamSK = t.TeamSK
            AND (s.ColumnId = t.ColumnId OR (s.ColumnId IS NULL AND t.ColumnId IS NULL))
            AND (s.LaneName = t.LaneName OR (s.LaneName IS NULL AND t.LaneName IS NULL))
            AND (s.ChangedDate = t.ChangedDate OR (s.ChangedDate IS NULL AND t.ChangedDate IS NULL))
            AND (s.IsDone = t.IsDone OR (s.IsDone IS NULL AND t.IsDone IS NULL))
    WHERE   t.PartitionId = @partitionId
            AND NOT EXISTS (
            SELECT  s.BoardId,
                    s.BoardExtensionId,
                    s.BoardCategoryReferenceName,
                    s.BoardName,
                    s.BoardLevel,
                    s.IsBoardVisible,
                    s.ColumnName,
                    s.ColumnOrder,
                    s.ColumnItemLimit,
                    s.LaneName,
                    s.LaneOrder,
                    s.TeamSK,
                    s.ProjectSK,
                    s.IsColumnSplit,
                    s.ChangedDate,
                    s.RevisedDate,
                    s.IsCurrent,
                    s.IsDefaultLane,
                    s.IsVirtual,
                    s.BacklogType
            INTERSECT
            SELECT  t.BoardId,
                    t.BoardExtensionId,
                    t.BoardCategoryReferenceName,
                    t.BoardName,
                    t.BoardLevel,
                    t.IsBoardVisible,
                    t.ColumnName,
                    t.ColumnOrder,
                    t.ColumnItemLimit,
                    t.LaneName,
                    t.LaneOrder,
                    t.TeamSK,
                    t.ProjectSK,
                    t.IsColumnSplit,
                    t.ChangedDate,
                    t.RevisedDate,
                    t.IsCurrent,
                    t.IsDefaultLane,
                    t.IsVirtual,
                    t.BacklogType
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    DELETE  s
    FROM    AnalyticsModel.tbl_BoardLocation t WITH (INDEX (UQ_tbl_BoardLocation_BoardColumnId))
    JOIN    #Src AS s
    ON      s.BoardCategoryReferenceName = t.BoardCategoryReferenceName
            AND s.TeamSK = t.TeamSK
            AND (s.ColumnId = t.ColumnId OR (s.ColumnId IS NULL AND t.ColumnId IS NULL))
            AND (s.LaneName = t.LaneName OR (s.LaneName IS NULL AND t.LaneName IS NULL))
            AND (s.ChangedDate = t.ChangedDate OR (s.ChangedDate IS NULL AND t.ChangedDate IS NULL))
            AND (s.IsDone = t.IsDone OR (s.IsDone IS NULL AND t.IsDone IS NULL))
    WHERE   t.PartitionId = @partitionId

    INSERT  TOP (@batchSizeMax) AnalyticsModel.tbl_BoardLocation
            (PartitionId,
            AnalyticsBatchId,
            AnalyticsCreatedDate,
            AnalyticsUpdatedDate,
            ColumnId,
            LaneId,
            IsDone,
            BoardId,
            BoardExtensionId,
            BoardCategoryReferenceName,
            BoardName,
            BoardLevel,
            IsBoardVisible,
            ColumnName,
            ColumnOrder,
            ColumnItemLimit,
            LaneName,
            LaneOrder,
            TeamSK,
            ProjectSK,
            IsColumnSplit,
            ChangedDate,
            RevisedDate,
            IsCurrent,
            IsDefaultLane,
            IsVirtual,
            BacklogType
            )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.ColumnId,
            s.LaneId,
            s.IsDone,
            s.BoardId,
            s.BoardExtensionId,
            s.BoardCategoryReferenceName,
            s.BoardName,
            s.BoardLevel,
            s.IsBoardVisible,
            s.ColumnName,
            s.ColumnOrder,
            s.ColumnItemLimit,
            s.LaneName,
            s.LaneOrder,
            s.TeamSK,
            s.ProjectSK,
            s.IsColumnSplit,
            s.ChangedDate,
            s.RevisedDate,
            s.IsCurrent,
            s.IsDefaultLane,
            s.IsVirtual,
            s.BacklogType
    FROM    #Src AS s
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT

    SET @complete = IIF(@insertedCount < @batchSizeMax AND @updatedCount < @batchSizeMax AND @deletedCount < @batchSizeMax, 1, 0)

    DROP TABLE #ChangedTeams
    DROP TABLE #TeamBoards
    DROP TABLE #BoardRow
    DROP TABLE #BacklogCategory
    DROP TABLE #Src

    RETURN 0
END

GO

