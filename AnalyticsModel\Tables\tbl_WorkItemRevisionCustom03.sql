CREATE TABLE [AnalyticsModel].[tbl_WorkItemRevisionCustom03] (
    [PartitionId]          INT                NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]     BIGINT             NOT NULL,
    [WorkItemId]           INT                NOT NULL,
    [Revision]             INT                NOT NULL,
    [WorkItemRevisionSK]   INT                NOT NULL,
    [String0401]           NVARCHAR (256)     NULL,
    [String0402]           NVARCHAR (256)     NULL,
    [String0403]           NVARCHAR (256)     NULL,
    [String0404]           NVARCHAR (256)     NULL,
    [String0405]           NVARCHAR (256)     NULL,
    [String0406]           NVARCHAR (256)     NULL,
    [String0407]           NVARCHAR (256)     NULL,
    [String0408]           NVARCHAR (256)     NULL,
    [String0409]           NVARCHAR (256)     NULL,
    [String0410]           NVARCHAR (256)     NULL,
    [String0411]           NVARCHAR (256)     NULL,
    [String0412]           NVARCHAR (256)     NULL,
    [String0413]           NVARCHAR (256)     NULL,
    [String0414]           NVARCHAR (256)     NULL,
    [String0415]           NVARCHAR (256)     NULL,
    [String0416]           NVARCHAR (256)     NULL,
    [String0417]           NVARCHAR (256)     NULL,
    [String0418]           NVARCHAR (256)     NULL,
    [String0419]           NVARCHAR (256)     NULL,
    [String0420]           NVARCHAR (256)     NULL,
    [String0421]           NVARCHAR (256)     NULL,
    [String0422]           NVARCHAR (256)     NULL,
    [String0423]           NVARCHAR (256)     NULL,
    [String0424]           NVARCHAR (256)     NULL,
    [String0425]           NVARCHAR (256)     NULL,
    [String0426]           NVARCHAR (256)     NULL,
    [String0427]           NVARCHAR (256)     NULL,
    [String0428]           NVARCHAR (256)     NULL,
    [String0429]           NVARCHAR (256)     NULL,
    [String0430]           NVARCHAR (256)     NULL,
    [String0431]           NVARCHAR (256)     NULL,
    [String0432]           NVARCHAR (256)     NULL,
    [String0433]           NVARCHAR (256)     NULL,
    [String0434]           NVARCHAR (256)     NULL,
    [String0435]           NVARCHAR (256)     NULL,
    [String0436]           NVARCHAR (256)     NULL,
    [String0437]           NVARCHAR (256)     NULL,
    [String0438]           NVARCHAR (256)     NULL,
    [String0439]           NVARCHAR (256)     NULL,
    [String0440]           NVARCHAR (256)     NULL,
    [String0441]           NVARCHAR (256)     NULL,
    [String0442]           NVARCHAR (256)     NULL,
    [String0443]           NVARCHAR (256)     NULL,
    [String0444]           NVARCHAR (256)     NULL,
    [String0445]           NVARCHAR (256)     NULL,
    [String0446]           NVARCHAR (256)     NULL,
    [String0447]           NVARCHAR (256)     NULL,
    [String0448]           NVARCHAR (256)     NULL,
    [String0449]           NVARCHAR (256)     NULL,
    [String0450]           NVARCHAR (256)     NULL,
    [String0451]           NVARCHAR (256)     NULL,
    [String0452]           NVARCHAR (256)     NULL,
    [String0453]           NVARCHAR (256)     NULL,
    [String0454]           NVARCHAR (256)     NULL,
    [String0455]           NVARCHAR (256)     NULL,
    [String0456]           NVARCHAR (256)     NULL,
    [String0457]           NVARCHAR (256)     NULL,
    [String0458]           NVARCHAR (256)     NULL,
    [String0459]           NVARCHAR (256)     NULL,
    [String0460]           NVARCHAR (256)     NULL,
    [String0461]           NVARCHAR (256)     NULL,
    [String0462]           NVARCHAR (256)     NULL,
    [String0463]           NVARCHAR (256)     NULL,
    [String0464]           NVARCHAR (256)     NULL,
    [String0465]           NVARCHAR (256)     NULL,
    [String0466]           NVARCHAR (256)     NULL,
    [String0467]           NVARCHAR (256)     NULL,
    [String0468]           NVARCHAR (256)     NULL,
    [String0469]           NVARCHAR (256)     NULL,
    [String0470]           NVARCHAR (256)     NULL,
    [String0471]           NVARCHAR (256)     NULL,
    [String0472]           NVARCHAR (256)     NULL,
    [String0473]           NVARCHAR (256)     NULL,
    [String0474]           NVARCHAR (256)     NULL,
    [String0475]           NVARCHAR (256)     NULL,
    [String0476]           NVARCHAR (256)     NULL,
    [String0477]           NVARCHAR (256)     NULL,
    [String0478]           NVARCHAR (256)     NULL,
    [String0479]           NVARCHAR (256)     NULL,
    [String0480]           NVARCHAR (256)     NULL,
    [String0481]           NVARCHAR (256)     NULL,
    [String0482]           NVARCHAR (256)     NULL,
    [String0483]           NVARCHAR (256)     NULL,
    [String0484]           NVARCHAR (256)     NULL,
    [String0485]           NVARCHAR (256)     NULL,
    [String0486]           NVARCHAR (256)     NULL,
    [String0487]           NVARCHAR (256)     NULL,
    [String0488]           NVARCHAR (256)     NULL,
    [String0489]           NVARCHAR (256)     NULL,
    [String0490]           NVARCHAR (256)     NULL,
    [String0491]           NVARCHAR (256)     NULL,
    [String0492]           NVARCHAR (256)     NULL,
    [String0493]           NVARCHAR (256)     NULL,
    [String0494]           NVARCHAR (256)     NULL,
    [String0495]           NVARCHAR (256)     NULL,
    [String0496]           NVARCHAR (256)     NULL,
    [String0497]           NVARCHAR (256)     NULL,
    [String0498]           NVARCHAR (256)     NULL,
    [String0499]           NVARCHAR (256)     NULL,
    [String0500]           NVARCHAR (256)     NULL,
    [String0501]           NVARCHAR (256)     NULL,
    [String0502]           NVARCHAR (256)     NULL,
    [String0503]           NVARCHAR (256)     NULL,
    [String0504]           NVARCHAR (256)     NULL,
    [String0505]           NVARCHAR (256)     NULL,
    [String0506]           NVARCHAR (256)     NULL,
    [String0507]           NVARCHAR (256)     NULL,
    [String0508]           NVARCHAR (256)     NULL,
    [String0509]           NVARCHAR (256)     NULL,
    [String0510]           NVARCHAR (256)     NULL,
    [String0511]           NVARCHAR (256)     NULL,
    [String0512]           NVARCHAR (256)     NULL,
    [String0513]           NVARCHAR (256)     NULL,
    [String0514]           NVARCHAR (256)     NULL,
    [String0515]           NVARCHAR (256)     NULL,
    [String0516]           NVARCHAR (256)     NULL,
    [String0517]           NVARCHAR (256)     NULL,
    [String0518]           NVARCHAR (256)     NULL,
    [String0519]           NVARCHAR (256)     NULL,
    [String0520]           NVARCHAR (256)     NULL,
    [String0521]           NVARCHAR (256)     NULL,
    [String0522]           NVARCHAR (256)     NULL,
    [String0523]           NVARCHAR (256)     NULL,
    [String0524]           NVARCHAR (256)     NULL,
    [String0525]           NVARCHAR (256)     NULL,
    [String0526]           NVARCHAR (256)     NULL,
    [String0527]           NVARCHAR (256)     NULL,
    [String0528]           NVARCHAR (256)     NULL,
    [String0529]           NVARCHAR (256)     NULL,
    [String0530]           NVARCHAR (256)     NULL,
    [String0531]           NVARCHAR (256)     NULL,
    [String0532]           NVARCHAR (256)     NULL,
    [String0533]           NVARCHAR (256)     NULL,
    [String0534]           NVARCHAR (256)     NULL,
    [String0535]           NVARCHAR (256)     NULL,
    [String0536]           NVARCHAR (256)     NULL,
    [String0537]           NVARCHAR (256)     NULL,
    [String0538]           NVARCHAR (256)     NULL,
    [String0539]           NVARCHAR (256)     NULL,
    [String0540]           NVARCHAR (256)     NULL,
    [String0541]           NVARCHAR (256)     NULL,
    [String0542]           NVARCHAR (256)     NULL,
    [String0543]           NVARCHAR (256)     NULL,
    [String0544]           NVARCHAR (256)     NULL,
    [String0545]           NVARCHAR (256)     NULL,
    [String0546]           NVARCHAR (256)     NULL,
    [String0547]           NVARCHAR (256)     NULL,
    [String0548]           NVARCHAR (256)     NULL,
    [String0549]           NVARCHAR (256)     NULL,
    [String0550]           NVARCHAR (256)     NULL,
    [String0551]           NVARCHAR (256)     NULL,
    [String0552]           NVARCHAR (256)     NULL,
    [String0553]           NVARCHAR (256)     NULL,
    [String0554]           NVARCHAR (256)     NULL,
    [String0555]           NVARCHAR (256)     NULL,
    [String0556]           NVARCHAR (256)     NULL,
    [String0557]           NVARCHAR (256)     NULL,
    [String0558]           NVARCHAR (256)     NULL,
    [String0559]           NVARCHAR (256)     NULL,
    [String0560]           NVARCHAR (256)     NULL,
    [String0561]           NVARCHAR (256)     NULL,
    [String0562]           NVARCHAR (256)     NULL,
    [String0563]           NVARCHAR (256)     NULL,
    [String0564]           NVARCHAR (256)     NULL,
    [String0565]           NVARCHAR (256)     NULL,
    [String0566]           NVARCHAR (256)     NULL,
    [String0567]           NVARCHAR (256)     NULL,
    [String0568]           NVARCHAR (256)     NULL,
    [String0569]           NVARCHAR (256)     NULL,
    [String0570]           NVARCHAR (256)     NULL,
    [String0571]           NVARCHAR (256)     NULL,
    [String0572]           NVARCHAR (256)     NULL,
    [String0573]           NVARCHAR (256)     NULL,
    [String0574]           NVARCHAR (256)     NULL,
    [String0575]           NVARCHAR (256)     NULL,
    [String0576]           NVARCHAR (256)     NULL,
    [String0577]           NVARCHAR (256)     NULL,
    [String0578]           NVARCHAR (256)     NULL,
    [String0579]           NVARCHAR (256)     NULL,
    [String0580]           NVARCHAR (256)     NULL,
    [String0581]           NVARCHAR (256)     NULL,
    [String0582]           NVARCHAR (256)     NULL,
    [String0583]           NVARCHAR (256)     NULL,
    [String0584]           NVARCHAR (256)     NULL,
    [String0585]           NVARCHAR (256)     NULL,
    [String0586]           NVARCHAR (256)     NULL,
    [String0587]           NVARCHAR (256)     NULL,
    [String0588]           NVARCHAR (256)     NULL,
    [String0589]           NVARCHAR (256)     NULL,
    [String0590]           NVARCHAR (256)     NULL,
    [String0591]           NVARCHAR (256)     NULL,
    [String0592]           NVARCHAR (256)     NULL,
    [String0593]           NVARCHAR (256)     NULL,
    [String0594]           NVARCHAR (256)     NULL,
    [String0595]           NVARCHAR (256)     NULL,
    [String0596]           NVARCHAR (256)     NULL,
    [String0597]           NVARCHAR (256)     NULL,
    [String0598]           NVARCHAR (256)     NULL,
    [String0599]           NVARCHAR (256)     NULL,
    [String0600]           NVARCHAR (256)     NULL,
    [Integer0101]          BIGINT             NULL,
    [Integer0102]          BIGINT             NULL,
    [Integer0103]          BIGINT             NULL,
    [Integer0104]          BIGINT             NULL,
    [Integer0105]          BIGINT             NULL,
    [Integer0106]          BIGINT             NULL,
    [Integer0107]          BIGINT             NULL,
    [Integer0108]          BIGINT             NULL,
    [Integer0109]          BIGINT             NULL,
    [Integer0110]          BIGINT             NULL,
    [Integer0111]          BIGINT             NULL,
    [Integer0112]          BIGINT             NULL,
    [Integer0113]          BIGINT             NULL,
    [Integer0114]          BIGINT             NULL,
    [Integer0115]          BIGINT             NULL,
    [Integer0116]          BIGINT             NULL,
    [Integer0117]          BIGINT             NULL,
    [Integer0118]          BIGINT             NULL,
    [Integer0119]          BIGINT             NULL,
    [Integer0120]          BIGINT             NULL,
    [Integer0121]          BIGINT             NULL,
    [Integer0122]          BIGINT             NULL,
    [Integer0123]          BIGINT             NULL,
    [Integer0124]          BIGINT             NULL,
    [Integer0125]          BIGINT             NULL,
    [Integer0126]          BIGINT             NULL,
    [Integer0127]          BIGINT             NULL,
    [Integer0128]          BIGINT             NULL,
    [Integer0129]          BIGINT             NULL,
    [Integer0130]          BIGINT             NULL,
    [Integer0131]          BIGINT             NULL,
    [Integer0132]          BIGINT             NULL,
    [Integer0133]          BIGINT             NULL,
    [Integer0134]          BIGINT             NULL,
    [Integer0135]          BIGINT             NULL,
    [Integer0136]          BIGINT             NULL,
    [Integer0137]          BIGINT             NULL,
    [Integer0138]          BIGINT             NULL,
    [Integer0139]          BIGINT             NULL,
    [Integer0140]          BIGINT             NULL,
    [Integer0141]          BIGINT             NULL,
    [Integer0142]          BIGINT             NULL,
    [Integer0143]          BIGINT             NULL,
    [Integer0144]          BIGINT             NULL,
    [Integer0145]          BIGINT             NULL,
    [Integer0146]          BIGINT             NULL,
    [Integer0147]          BIGINT             NULL,
    [Integer0148]          BIGINT             NULL,
    [Integer0149]          BIGINT             NULL,
    [Integer0150]          BIGINT             NULL,
    [Double0101]           FLOAT (53)         NULL,
    [Double0102]           FLOAT (53)         NULL,
    [Double0103]           FLOAT (53)         NULL,
    [Double0104]           FLOAT (53)         NULL,
    [Double0105]           FLOAT (53)         NULL,
    [Double0106]           FLOAT (53)         NULL,
    [Double0107]           FLOAT (53)         NULL,
    [Double0108]           FLOAT (53)         NULL,
    [Double0109]           FLOAT (53)         NULL,
    [Double0110]           FLOAT (53)         NULL,
    [Double0111]           FLOAT (53)         NULL,
    [Double0112]           FLOAT (53)         NULL,
    [Double0113]           FLOAT (53)         NULL,
    [Double0114]           FLOAT (53)         NULL,
    [Double0115]           FLOAT (53)         NULL,
    [Double0116]           FLOAT (53)         NULL,
    [Double0117]           FLOAT (53)         NULL,
    [Double0118]           FLOAT (53)         NULL,
    [Double0119]           FLOAT (53)         NULL,
    [Double0120]           FLOAT (53)         NULL,
    [Double0121]           FLOAT (53)         NULL,
    [Double0122]           FLOAT (53)         NULL,
    [Double0123]           FLOAT (53)         NULL,
    [Double0124]           FLOAT (53)         NULL,
    [Double0125]           FLOAT (53)         NULL,
    [Double0126]           FLOAT (53)         NULL,
    [Double0127]           FLOAT (53)         NULL,
    [Double0128]           FLOAT (53)         NULL,
    [Double0129]           FLOAT (53)         NULL,
    [Double0130]           FLOAT (53)         NULL,
    [Double0131]           FLOAT (53)         NULL,
    [Double0132]           FLOAT (53)         NULL,
    [Double0133]           FLOAT (53)         NULL,
    [Double0134]           FLOAT (53)         NULL,
    [Double0135]           FLOAT (53)         NULL,
    [Double0136]           FLOAT (53)         NULL,
    [Double0137]           FLOAT (53)         NULL,
    [Double0138]           FLOAT (53)         NULL,
    [Double0139]           FLOAT (53)         NULL,
    [Double0140]           FLOAT (53)         NULL,
    [Double0141]           FLOAT (53)         NULL,
    [Double0142]           FLOAT (53)         NULL,
    [Double0143]           FLOAT (53)         NULL,
    [Double0144]           FLOAT (53)         NULL,
    [Double0145]           FLOAT (53)         NULL,
    [Double0146]           FLOAT (53)         NULL,
    [Double0147]           FLOAT (53)         NULL,
    [Double0148]           FLOAT (53)         NULL,
    [Double0149]           FLOAT (53)         NULL,
    [Double0150]           FLOAT (53)         NULL,
    [DateTime0101]         DATETIMEOFFSET (7) NULL,
    [DateTime0102]         DATETIMEOFFSET (7) NULL,
    [DateTime0103]         DATETIMEOFFSET (7) NULL,
    [DateTime0104]         DATETIMEOFFSET (7) NULL,
    [DateTime0105]         DATETIMEOFFSET (7) NULL,
    [DateTime0106]         DATETIMEOFFSET (7) NULL,
    [DateTime0107]         DATETIMEOFFSET (7) NULL,
    [DateTime0108]         DATETIMEOFFSET (7) NULL,
    [DateTime0109]         DATETIMEOFFSET (7) NULL,
    [DateTime0110]         DATETIMEOFFSET (7) NULL,
    [DateTime0111]         DATETIMEOFFSET (7) NULL,
    [DateTime0112]         DATETIMEOFFSET (7) NULL,
    [DateTime0113]         DATETIMEOFFSET (7) NULL,
    [DateTime0114]         DATETIMEOFFSET (7) NULL,
    [DateTime0115]         DATETIMEOFFSET (7) NULL,
    [DateTime0116]         DATETIMEOFFSET (7) NULL,
    [DateTime0117]         DATETIMEOFFSET (7) NULL,
    [DateTime0118]         DATETIMEOFFSET (7) NULL,
    [DateTime0119]         DATETIMEOFFSET (7) NULL,
    [DateTime0120]         DATETIMEOFFSET (7) NULL,
    [DateTime0121]         DATETIMEOFFSET (7) NULL,
    [DateTime0122]         DATETIMEOFFSET (7) NULL,
    [DateTime0123]         DATETIMEOFFSET (7) NULL,
    [DateTime0124]         DATETIMEOFFSET (7) NULL,
    [DateTime0125]         DATETIMEOFFSET (7) NULL,
    [DateTime0126]         DATETIMEOFFSET (7) NULL,
    [DateTime0127]         DATETIMEOFFSET (7) NULL,
    [DateTime0128]         DATETIMEOFFSET (7) NULL,
    [DateTime0129]         DATETIMEOFFSET (7) NULL,
    [DateTime0130]         DATETIMEOFFSET (7) NULL,
    [DateTime0131]         DATETIMEOFFSET (7) NULL,
    [DateTime0132]         DATETIMEOFFSET (7) NULL,
    [DateTime0133]         DATETIMEOFFSET (7) NULL,
    [DateTime0134]         DATETIMEOFFSET (7) NULL,
    [DateTime0135]         DATETIMEOFFSET (7) NULL,
    [DateTime0136]         DATETIMEOFFSET (7) NULL,
    [DateTime0137]         DATETIMEOFFSET (7) NULL,
    [DateTime0138]         DATETIMEOFFSET (7) NULL,
    [DateTime0139]         DATETIMEOFFSET (7) NULL,
    [DateTime0140]         DATETIMEOFFSET (7) NULL,
    [DateTime0141]         DATETIMEOFFSET (7) NULL,
    [DateTime0142]         DATETIMEOFFSET (7) NULL,
    [DateTime0143]         DATETIMEOFFSET (7) NULL,
    [DateTime0144]         DATETIMEOFFSET (7) NULL,
    [DateTime0145]         DATETIMEOFFSET (7) NULL,
    [DateTime0146]         DATETIMEOFFSET (7) NULL,
    [DateTime0147]         DATETIMEOFFSET (7) NULL,
    [DateTime0148]         DATETIMEOFFSET (7) NULL,
    [DateTime0149]         DATETIMEOFFSET (7) NULL,
    [DateTime0150]         DATETIMEOFFSET (7) NULL,
    [Boolean0101]          BIT                NULL,
    [Boolean0102]          BIT                NULL,
    [Boolean0103]          BIT                NULL,
    [Boolean0104]          BIT                NULL,
    [Boolean0105]          BIT                NULL,
    [Boolean0106]          BIT                NULL,
    [Boolean0107]          BIT                NULL,
    [Boolean0108]          BIT                NULL,
    [Boolean0109]          BIT                NULL,
    [Boolean0110]          BIT                NULL,
    [Boolean0111]          BIT                NULL,
    [Boolean0112]          BIT                NULL,
    [Boolean0113]          BIT                NULL,
    [Boolean0114]          BIT                NULL,
    [Boolean0115]          BIT                NULL,
    [Boolean0116]          BIT                NULL,
    [Boolean0117]          BIT                NULL,
    [Boolean0118]          BIT                NULL,
    [Boolean0119]          BIT                NULL,
    [Boolean0120]          BIT                NULL,
    [Boolean0121]          BIT                NULL,
    [Boolean0122]          BIT                NULL,
    [Boolean0123]          BIT                NULL,
    [Boolean0124]          BIT                NULL,
    [Boolean0125]          BIT                NULL,
    [Boolean0126]          BIT                NULL,
    [Boolean0127]          BIT                NULL,
    [Boolean0128]          BIT                NULL,
    [Boolean0129]          BIT                NULL,
    [Boolean0130]          BIT                NULL,
    [Boolean0131]          BIT                NULL,
    [Boolean0132]          BIT                NULL,
    [Boolean0133]          BIT                NULL,
    [Boolean0134]          BIT                NULL,
    [Boolean0135]          BIT                NULL,
    [Boolean0136]          BIT                NULL,
    [Boolean0137]          BIT                NULL,
    [Boolean0138]          BIT                NULL,
    [Boolean0139]          BIT                NULL,
    [Boolean0140]          BIT                NULL,
    [Boolean0141]          BIT                NULL,
    [Boolean0142]          BIT                NULL,
    [Boolean0143]          BIT                NULL,
    [Boolean0144]          BIT                NULL,
    [Boolean0145]          BIT                NULL,
    [Boolean0146]          BIT                NULL,
    [Boolean0147]          BIT                NULL,
    [Boolean0148]          BIT                NULL,
    [Boolean0149]          BIT                NULL,
    [Boolean0150]          BIT                NULL,
    [Identity0101]         UNIQUEIDENTIFIER   NULL,
    [Identity0102]         UNIQUEIDENTIFIER   NULL,
    [Identity0103]         UNIQUEIDENTIFIER   NULL,
    [Identity0104]         UNIQUEIDENTIFIER   NULL,
    [Identity0105]         UNIQUEIDENTIFIER   NULL,
    [Identity0106]         UNIQUEIDENTIFIER   NULL,
    [Identity0107]         UNIQUEIDENTIFIER   NULL,
    [Identity0108]         UNIQUEIDENTIFIER   NULL,
    [Identity0109]         UNIQUEIDENTIFIER   NULL,
    [Identity0110]         UNIQUEIDENTIFIER   NULL,
    [Identity0111]         UNIQUEIDENTIFIER   NULL,
    [Identity0112]         UNIQUEIDENTIFIER   NULL,
    [Identity0113]         UNIQUEIDENTIFIER   NULL,
    [Identity0114]         UNIQUEIDENTIFIER   NULL,
    [Identity0115]         UNIQUEIDENTIFIER   NULL,
    [Identity0116]         UNIQUEIDENTIFIER   NULL,
    [Identity0117]         UNIQUEIDENTIFIER   NULL,
    [Identity0118]         UNIQUEIDENTIFIER   NULL,
    [Identity0119]         UNIQUEIDENTIFIER   NULL,
    [Identity0120]         UNIQUEIDENTIFIER   NULL,
    [Identity0121]         UNIQUEIDENTIFIER   NULL,
    [Identity0122]         UNIQUEIDENTIFIER   NULL,
    [Identity0123]         UNIQUEIDENTIFIER   NULL,
    [Identity0124]         UNIQUEIDENTIFIER   NULL,
    [Identity0125]         UNIQUEIDENTIFIER   NULL,
    [Identity0126]         UNIQUEIDENTIFIER   NULL,
    [Identity0127]         UNIQUEIDENTIFIER   NULL,
    [Identity0128]         UNIQUEIDENTIFIER   NULL,
    [Identity0129]         UNIQUEIDENTIFIER   NULL,
    [Identity0130]         UNIQUEIDENTIFIER   NULL,
    [Identity0131]         UNIQUEIDENTIFIER   NULL,
    [Identity0132]         UNIQUEIDENTIFIER   NULL,
    [Identity0133]         UNIQUEIDENTIFIER   NULL,
    [Identity0134]         UNIQUEIDENTIFIER   NULL,
    [Identity0135]         UNIQUEIDENTIFIER   NULL,
    [Identity0136]         UNIQUEIDENTIFIER   NULL,
    [Identity0137]         UNIQUEIDENTIFIER   NULL,
    [Identity0138]         UNIQUEIDENTIFIER   NULL,
    [Identity0139]         UNIQUEIDENTIFIER   NULL,
    [Identity0140]         UNIQUEIDENTIFIER   NULL,
    [Identity0141]         UNIQUEIDENTIFIER   NULL,
    [Identity0142]         UNIQUEIDENTIFIER   NULL,
    [Identity0143]         UNIQUEIDENTIFIER   NULL,
    [Identity0144]         UNIQUEIDENTIFIER   NULL,
    [Identity0145]         UNIQUEIDENTIFIER   NULL,
    [Identity0146]         UNIQUEIDENTIFIER   NULL,
    [Identity0147]         UNIQUEIDENTIFIER   NULL,
    [Identity0148]         UNIQUEIDENTIFIER   NULL,
    [Identity0149]         UNIQUEIDENTIFIER   NULL,
    [Identity0150]         UNIQUEIDENTIFIER   NULL
) ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_AnalyticsModel_tbl_WorkItemRevisionCustom03_WorkItemRevisionSK]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom03]([PartitionId] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_WorkItemRevisionCustom03]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom03]
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

