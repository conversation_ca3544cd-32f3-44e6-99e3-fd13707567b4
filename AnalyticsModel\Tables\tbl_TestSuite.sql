CREATE TABLE [AnalyticsModel].[tbl_TestSuite] (
    [PartitionId]           INT              NOT NULL,
    [AnalyticsCreatedDate]  DATETIME2 (7)    NOT NULL,
    [AnalyticsUpdatedDate]  DATETIME2 (7)    NOT NULL,
    [AnalyticsBatchId]      BIGINT           NOT NULL,
    [TestSuiteSK]           INT              IDENTITY (1, 1) NOT NULL,
    [ProjectSK]             UNIQUEIDENTIFIER NOT NULL,
    [TestPlanId]            INT              NOT NULL,
    [TestSuiteId]           INT              NOT NULL,
    [TestPlanTitle]         NVARCHAR (256)   NULL,
    [TestPlanState]         TINYINT          NULL,
    [ParentSuiteId]         INT              NULL,
    [Title]                 NVARCHAR (256)   NULL,
    [OrderId]               INT              NULL,
    [IsDeleted]             BIT              NOT NULL,
    [IdLevel1]              INT              NULL,
    [TitleLevel1]           NVARCHAR (256)   NULL,
    [IdLevel2]              INT              NULL,
    [TitleLevel2]           NVARCHAR (256)   NULL,
    [IdLevel3]              INT              NULL,
    [TitleLevel3]           NVARCHAR (256)   NULL,
    [IdLevel4]              INT              NULL,
    [TitleLevel4]           NVARCHAR (256)   NULL,
    [IdLevel5]              INT              NULL,
    [TitleLevel5]           NVARCHAR (256)   NULL,
    [IdLevel6]              INT              NULL,
    [TitleLevel6]           NVARCHAR (256)   NULL,
    [IdLevel7]              INT              NULL,
    [TitleLevel7]           NVARCHAR (256)   NULL,
    [IdLevel8]              INT              NULL,
    [TitleLevel8]           NVARCHAR (256)   NULL,
    [IdLevel9]              INT              NULL,
    [TitleLevel9]           NVARCHAR (256)   NULL,
    [IdLevel10]             INT              NULL,
    [TitleLevel10]          NVARCHAR (256)   NULL,
    [IdLevel11]             INT              NULL,
    [TitleLevel11]          NVARCHAR (256)   NULL,
    [IdLevel12]             INT              NULL,
    [TitleLevel12]          NVARCHAR (256)   NULL,
    [IdLevel13]             INT              NULL,
    [TitleLevel13]          NVARCHAR (256)   NULL,
    [IdLevel14]             INT              NULL,
    [TitleLevel14]          NVARCHAR (256)   NULL,
    [Depth]                 TINYINT          NULL,
    [Type]                  TINYINT          NULL,
    [RequirementWorkItemId] INT              NULL,
    [DataSourceId]          INT              DEFAULT ((0)) NOT NULL
);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel13]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel13] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel10]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel10] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel7]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel7] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel4]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel4] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_AnalyticsBatchId]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [AnalyticsBatchId] ASC)
    INCLUDE([ProjectSK], [TestSuiteId], [TestPlanId]) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel1]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel1] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel8]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel8] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel2]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel2] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel5]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel5] ASC);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_TestSuite]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [TestSuiteSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_ProjectSK_TestPlanId]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [ProjectSK] ASC, [TestPlanId] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel14]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel14] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel11]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel11] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel6]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel6] ASC);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_TestSuite_TestPlanId_TestSuiteId]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [TestPlanId] ASC, [TestSuiteId] ASC)
    INCLUDE([ProjectSK], [TestSuiteSK], [IsDeleted], [TestPlanState], [AnalyticsBatchId]) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_ProjectSK_TestSuiteId]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [ProjectSK] ASC, [TestSuiteId] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel3]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel3] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel12]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel12] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestSuite_IdLevel9]
    ON [AnalyticsModel].[tbl_TestSuite]([PartitionId] ASC, [IdLevel9] ASC);


GO

