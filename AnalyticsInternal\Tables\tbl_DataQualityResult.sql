CREATE TABLE [AnalyticsInternal].[tbl_DataQualityResult] (
    [PartitionId]   INT           NOT NULL,
    [RunDate]       DATETIME2 (7) NOT NULL,
    [StartDate]     DATETIME2 (7) NOT NULL,
    [EndDate]       DATETIME2 (7) NOT NULL,
    [Name]          VARCHAR (256) NOT NULL,
    [TargetTable]   VARCHAR (64)  NOT NULL,
    [ExpectedValue] BIGINT        NULL,
    [ActualValue]   BIGINT        NULL,
    [Failed]        BIT           NULL,
    [KpiValue]      FLOAT (53)    NULL,
    [RunEndDate]    DATETIME      NULL,
    [Scope]         VARCHAR (256) NULL,
    [Latest]        BIT           NOT NULL
);


GO

CREATE CLUSTERED INDEX [CI_AnalyticsInternal_tbl_DataQualityResult]
    ON [AnalyticsInternal].[tbl_DataQualityResult]([PartitionId] ASC, [TargetTable] ASC, [Name] ASC, [RunDate] DESC);


GO

CREATE NONCLUSTERED INDEX [IX_AnalyticsInternal_tbl_DataQualityResult_Latest]
    ON [AnalyticsInternal].[tbl_DataQualityResult]([PartitionId] ASC, [TargetTable] ASC, [Name] ASC, [Scope] ASC)
    INCLUDE([RunDate], [StartDate], [EndDate], [ExpectedValue], [ActualValue], [Failed], [KpiValue], [RunEndDate]) WHERE ([Latest]=(1));


GO

