/******************************************************************************************************
** Warning: the contents of this function are critical to its functioning.
** Modifying the contents of this function could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A757B1060C4DEF8AA2D25A5B1E3BB721A92939E2
CREATE FUNCTION AnalyticsInternal.func_AdjustReattemptedBatchSize(
    @batchSizeMax INT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT
    )
RETURNS INT
AS
BEGIN
    -- if there have been and failures in the last 20 batches
    IF (@failedCount > 0 AND (@subBatchCount - @lastFailedSubBatchCount) <= 20)
    BEGIN
        SET @batchSizeMax = @batchSizeMax / 10
    END

    IF (@attemptCount > 1)
    BEGIN
        SET @attemptCount = IIF(@attemptCount < 10, @attemptCount, 10) -- upper limit, prevents overflow in denominator

        -- reduce 10x for each attempt - to address timeouts
        SET @batchSizeMax = @batchSizeMax / POWER(10.0, @attemptCount - 1)

    END

    SET @batchSizeMax = IIF(@batchSizeMax >= 1, @batchSizeMax, 1) -- lower limit 1

    RETURN @batchSizeMax
END

GO

GRANT EXECUTE
    ON OBJECT::[AnalyticsInternal].[func_AdjustReattemptedBatchSize] TO [VSODIAGROLE]
    AS [dbo];


GO

