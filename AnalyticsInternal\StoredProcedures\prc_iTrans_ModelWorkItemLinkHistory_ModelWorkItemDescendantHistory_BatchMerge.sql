/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 659A778796B5D4E6518FB82EBF6657F27CE1A938
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_ModelWorkItemLinkHistory_ModelWorkItemDescendantHistory_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    CREATE TABLE #TriggerWorkItemId
    (
         ChildWorkItemId     INT   NOT NULL PRIMARY KEY
    )

    IF (@stateData IS NULL AND @triggerBatchIdStart > 1) -- use NCI for small batches - assume first batch is likely small
    BEGIN
        INSERT  #TriggerWorkItemId
        SELECT  TOP (@batchSizeMax) TargetWorkItemId
        FROM    AnalyticsModel.tbl_WorkItemLinkHistory WITH (INDEX (IX_tbl_WorkItemLinkHistory_AxBatchId))
        WHERE   PartitionId = @partitionId
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
        GROUP BY TargetWorkItemId
        ORDER BY TargetWorkItemId ASC
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)

        INSERT  #TriggerWorkItemId
        SELECT  TOP (@batchSizeMax) TargetWorkItemId
        FROM    AnalyticsModel.tbl_WorkItemLinkHistory WITH (INDEX (IX_tbl_WorkItemLinkHistory_TargetWorkItemIdDeletedDate))
        WHERE   PartitionId = @partitionId
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND TargetWorkItemId >= @workItemIdStart
                AND LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
        GROUP BY TargetWorkItemId
        ORDER BY TargetWorkItemId ASC
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    CREATE TABLE #SrcDown
    (
        WorkItemId                      INT                 NOT NULL,
        DescendantWorkItemId            INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
        CreatedDate                     DATETIMEOFFSET      NOT NULL,
        DeletedDate                     DATETIMEOFFSET      NOT NULL,
        IsCurrent                       BIT                 NOT NULL,
    )

    -- recurse down to find descendants of the ChildWorkItemId
    ;WITH WorkItemParents AS
    (
        SELECT  tc.ChildWorkItemId AS WorkItemId,
                tc.ChildWorkItemId AS ChildWorkItemId,
                0 AS Depth,
                CAST('1900-01-01' AS DATETIMEOFFSET) AS CreatedDate,
                CAST('9999-01-01' AS DATETIMEOFFSET) AS DeletedDate,
                CAST(1 AS BIT) AS IsCurrent
        FROM    #TriggerWorkItemId tc
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND tc.ChildWorkItemId = l.TargetWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'

        UNION ALL

        SELECT  p.WorkItemId,
                l.TargetWorkItemId AS ChildWorkItemId,
                p.Depth + 1 AS Depth,
                IIF(p.CreatedDate > l.CreatedDate, p.CreatedDate, l.CreatedDate) CreatedDate,
                IIF(p.DeletedDate < l.DeletedDate, p.DeletedDate, l.DeletedDate) DeletedDate,
                CAST(IIF(p.IsCurrent = 1 AND l.IsCurrent = 1, 1, 0) AS BIT) AS IsCurrent
        FROM    WorkItemParents p
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND p.ChildWorkItemId = l.SourceWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
                AND l.CreatedDate < p.DeletedDate
                AND l.DeletedDate > p.CreatedDate
        WHERE   p.Depth < 50
    )
    INSERT  #SrcDown
    SELECT  WorkItemId,
            ChildWorkItemId AS DescendantWorkItemId,
            MIN(Depth), -- loop detection
            CreatedDate,
            DeletedDate,
            IsCurrent
    FROM    WorkItemParents
    GROUP BY WorkItemId,
            ChildWorkItemId,
            CreatedDate,
            DeletedDate,
            IsCurrent
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #SrcUp
    (
        AncestorWorkItemId              INT                 NOT NULL,
        WorkItemId                      INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
        CreatedDate                     DATETIMEOFFSET      NOT NULL,
        DeletedDate                     DATETIMEOFFSET      NOT NULL,
        IsCurrent                       BIT                 NOT NULL,
    )

    -- recurse up to find ancestors of the ChildWorkItemId
    ;WITH WorkItemChildren AS
    (
        SELECT  tc.ChildWorkItemId AS WorkItemId,
                l.SourceWorkItemId AS ParentWorkItemId,
                1 AS Depth,
                l.CreatedDate,
                l.DeletedDate,
                l.IsCurrent
        FROM    #TriggerWorkItemId tc
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND tc.ChildWorkItemId = l.TargetWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'

        UNION ALL

        SELECT  c.WorkItemId,
                l.SourceWorkItemId AS ParentWorkItemId,
                c.Depth + 1 AS Depth,
                IIF(c.CreatedDate > l.CreatedDate, c.CreatedDate, l.CreatedDate) CreatedDate,
                IIF(c.DeletedDate < l.DeletedDate, c.DeletedDate, l.DeletedDate) DeletedDate,
                CAST(IIF(c.IsCurrent = 1 AND l.IsCurrent = 1, 1, 0) AS BIT) AS IsCurrent
        FROM    WorkItemChildren c
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND c.ParentWorkItemId = l.TargetWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward' -- 'System.LinkTypes.Hierarchy-Forward'
                AND l.CreatedDate < c.DeletedDate
                AND l.DeletedDate > c.CreatedDate
        WHERE   c.Depth < 50
    )
    INSERT  #SrcUp
    SELECT  ParentWorkItemId AS AncestorWorkItemId,
            WorkItemId,
            MIN(Depth), -- loop detection
            CreatedDate,
            DeletedDate,
            IsCurrent
    FROM    WorkItemChildren
    GROUP BY ParentWorkItemId,
            WorkItemId,
            CreatedDate,
            DeletedDate,
            IsCurrent
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #Src
    (
        WorkItemId                      INT                 NOT NULL,
        DescendantWorkItemId            INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
        CreatedDate                     DATETIMEOFFSET      NOT NULL,
        DeletedDate                     DATETIMEOFFSET      NOT NULL,
        IsCurrent                       BIT                 NOT NULL,
        IsLastRevisionOfPeriod          INT                 NOT NULL,
        PRIMARY KEY (WorkItemId, DescendantWorkItemId, CreatedDate)
    )

    -- flatten the descendants with the ancestors of the impacted children
    INSERT  #Src
    SELECT  DISTINCT AncestorWorkItemId,
            DescendantWorkItemId,
            d.Depth + u.Depth,
            IIF(u.CreatedDate > d.CreatedDate, u.CreatedDate, d.CreatedDate) AT TIME ZONE @timeZone AS CreatedDate,
            IIF(u.DeletedDate < d.DeletedDate, u.DeletedDate, d.DeletedDate) AT TIME ZONE @timeZone AS DeletedDate,
            IIF(u.IsCurrent = 1 AND d.IsCurrent = 1, 1, 0) AS IsCurrent,
            2047 AS IsLastRevisionOfPeriod -- set in next step
    FROM    #SrcDown d
    JOIN    #SrcUp u
    ON      d.WorkItemId = u.WorkItemId
            AND u.CreatedDate < d.DeletedDate
            AND u.DeletedDate > d.CreatedDate

    -- set IsLastRevisionOfDay
    ;WITH AllLinks AS
    (
        SELECT  *,
                CAST(CreatedDate AS DATE) CreatedDay,
                CAST(DeletedDate AS DATE) DeletedDay
        FROM    #Src
    )
    UPDATE  t
    SET     IsLastRevisionOfPeriod = IIF(DeletedDay IS NULL, 2047,
                  IIF(DATEDIFF(DAY, CreatedDay, DeletedDay) > 0, 1, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -0, CreatedDay), DATEADD(DAY, -0, DeletedDay)) > 0, 128, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -1, CreatedDay), DATEADD(DAY, -1, DeletedDay)) > 0, 2, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -2, CreatedDay), DATEADD(DAY, -2, DeletedDay)) > 0, 4, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -3, CreatedDay), DATEADD(DAY, -3, DeletedDay)) > 0, 8, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -4, CreatedDay), DATEADD(DAY, -4, DeletedDay)) > 0, 16, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -5, CreatedDay), DATEADD(DAY, -5, DeletedDay)) > 0, 32, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -6, CreatedDay), DATEADD(DAY, -6, DeletedDay)) > 0, 64, 0)
                | IIF(DATEDIFF(MONTH,   CreatedDay, DeletedDay) > 0, 256, 0)
                | IIF(DATEDIFF(QUARTER, CreatedDay, DeletedDay) > 0, 512, 0)
                | IIF(DATEDIFF(YEAR,    CreatedDay, DeletedDay) > 0, 1024, 0)
                )
    FROM    AllLinks t

    DELETE  s
    FROM    #Src s
    JOIN    AnalyticsModel.tbl_WorkItemDescendantHistory t
    ON      t.PartitionId = @partitionId
            AND t.WorkItemId = s.WorkItemId
            AND t.DescendantWorkItemId = s.DescendantWorkItemId
            AND t.CreatedDate = s.CreatedDate
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    BEGIN TRAN

    -- cleanup rollup roots for descendents of impacted child workitems (if deletes found)
    DELETE  t
    FROM    #SrcDown d -- the subtree of the impacted child
    INNER LOOP JOIN  AnalyticsModel.tbl_WorkItemDescendantHistory t WITH (INDEX (UQ_tbl_WorkItemDescendantHistory_Reverse))
    ON      d.DescendantWorkItemId = t.DescendantWorkItemId
            AND d.Depth < t.Depth
    LEFT HASH JOIN #Src s
    ON      t.WorkItemId = s.WorkItemId
            AND t.DescendantWorkItemId = s.DescendantWorkItemId
            AND t.CreatedDate = s.CreatedDate
            AND t.DeletedDate = s.DeletedDate
    WHERE   t.PartitionId = @partitionId
            AND (s.DescendantWorkItemId IS NULL OR s.Depth != t.Depth OR s.IsCurrent != t.IsCurrent OR s.IsLastRevisionOfPeriod != t.IsLastRevisionOfPeriod)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    INSERT  AnalyticsModel.tbl_WorkItemDescendantHistory -- note this needs to be a MERGE, to update DeletedDate, IsCurrent, and Depth
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        WorkItemId,
        DescendantWorkItemId,
        Depth,
        CreatedDate,
        DeletedDate,
        CreatedDateSK,
        DeletedDateSK,
        IsCurrent,
        IsLastRevisionOfPeriod
    )
    SELECT  @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            WorkItemId,
            DescendantWorkItemId,
            Depth,
            CreatedDate,
            DeletedDate,
            AnalyticsInternal.func_GenDateSK(CreatedDate),
            AnalyticsInternal.func_GenDateSK(DeletedDate),
            IsCurrent,
            IsLastRevisionOfPeriod
    FROM    #Src
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT

    COMMIT TRAN

    SET @endStateData = (SELECT MAX(ChildWorkItemId) FROM #TriggerWorkItemId)
    SET @complete = IIF((SELECT COUNT(*) FROM #TriggerWorkItemId) >= @batchSizeMax, 0, 1)

    DROP TABLE #TriggerWorkItemId
    DROP TABLE #SrcDown
    DROP TABLE #SrcUp
    DROP TABLE #Src

    RETURN 0
END

GO

