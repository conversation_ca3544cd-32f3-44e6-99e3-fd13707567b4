/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 289B20151EE574834693C0B43BE1500149414C3F
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_InternalWorkItemRevisionKanban_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)
    DECLARE @triggerWorkItemIdRev TABLE
    (
         System_Id INT NOT NULL,
         System_Rev INT NOT NULL,
         PRIMARY KEY (System_Id, System_Rev)
    )

    IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
    BEGIN
        INSERT  @triggerWorkItemIdRev
        SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev
        FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
        WHERE   wi.PartitionId = @partitionId
                AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND wi.System_Id >= @workItemIdStart
                AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
        ORDER BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @endState = IIF(@@ROWCOUNT >= @batchSizeMax, 'dense', '')
        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemIdRev), @stateData)
        SET @complete = 0
    END
    ELSE -- use NCI
    BEGIN
        INSERT  @triggerWorkItemIdRev
        SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev
        FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
        WHERE   wi.PartitionId = @partitionId
                AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND wi.System_Id >= @workItemIdStart
        ORDER BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

        SET @complete = IIF(@@ROWCOUNT >= @batchSizeMax, 0, 1)
        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemIdRev), @stateData)
        SET @endState = IIF(@complete = 0 AND @endStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
    END

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    )

    CREATE TABLE #Src
    (
        System_Id INT,
        System_Rev INT,
        ExtensionId UNIQUEIDENTIFIER,
        ExtensionMarker BIT,
        ColumnName NVARCHAR(256) COLLATE Latin1_General_100_CI_AS,
        LaneName NVARCHAR(256) COLLATE Latin1_General_100_CI_AS,
        Done BIT
    )

    -- NOTE - this approach does not support deletion of rows from extended table
    ;WITH BoardLocator AS
    (
        SELECT  x.System_Id,
                x.System_Rev,
                f.ExtensionId,
                CAST(MAX(IIF(f.ExtensionFieldName = 'System_ExtensionMarker' COLLATE Latin1_General_CI_AS, x.ValueInt, NULL)) AS BIT) AS ExtensionMarker,
                MAX(IIF(f.ExtensionFieldName = 'Kanban_Column' COLLATE Latin1_General_CI_AS, x.ValueString, NULL)) AS ColumnName,
                MAX(IIF(f.ExtensionFieldName = 'Kanban_Lane' COLLATE Latin1_General_CI_AS, x.ValueString, NULL)) AS LaneName,
                CAST(MAX(IIF(f.ExtensionFieldName = 'Kanban_Column_Done' COLLATE Latin1_General_CI_AS, x.ValueInt, NULL)) AS BIT) AS Done
        FROM    @triggerWorkItemIdRev r
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x
        ON      x.PartitionId = @partitionId
                AND x.System_Id = r.System_Id
                AND x.System_Rev = r.System_Rev
        INNER LOOP JOIN AnalyticsInternal.tbl_Fields f
        ON      f.PartitionId = x.PartitionId
                AND f.FieldSK = x.FieldSK
                AND f.ExtensionFieldName COLLATE Latin1_General_CI_AS IN ('System_ExtensionMarker', 'Kanban_Column', 'Kanban_Lane', 'Kanban_Column_Done')
        GROUP BY x.System_Id,
                x.System_Rev,
                f.ExtensionId
    )
    INSERT  #Src
    SELECT  System_Id,
            System_Rev,
            ExtensionId,
            ExtensionMarker,
            IIF(ExtensionMarker = 1, ColumnName, NULL) AS ColumnName,
            IIF(ExtensionMarker = 1, LaneName, NULL) AS LaneName,
            IIF(ExtensionMarker = 1, Done, NULL) AS Done
    FROM    BoardLocator
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

    ;WITH Tgt AS
    (
        SELECT *
        FROM AnalyticsInternal.tbl_WorkItemRevisionKanban
        WHERE PartitionId = @partitionId
    )
    MERGE   Tgt AS t
    USING   #Src AS s
    ON      t.System_Id = s.System_Id
            AND t.System_Rev = s.System_Rev
            AND t.ExtensionId = s.ExtensionId
    WHEN MATCHED AND NOT EXISTS (
        SELECT  s.ColumnName,
                s.LaneName,
                s.Done
        INTERSECT
        SELECT  t.ColumnName,
                t.LaneName,
                t.Done
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt,
        AnalyticsBatchId = @batchId,
        ColumnName = s.ColumnName,
        LaneName = s.LaneName,
        Done = s.Done
    WHEN NOT MATCHED BY TARGET AND s.ExtensionMarker = 1
    THEN INSERT (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        System_Id,
        System_Rev,
        ExtensionId,
        ColumnName,
        LaneName,
        Done
    )
    VALUES (
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.System_Id,
        s.System_Rev,
        s.ExtensionId,
        s.ColumnName,
        s.LaneName,
        s.Done
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), LOOP JOIN);

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    DROP TABLE #Src

    RETURN 0
END

GO

