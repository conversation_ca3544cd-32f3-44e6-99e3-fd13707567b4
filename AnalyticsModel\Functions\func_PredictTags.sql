/******************************************************************************************************
** Warning: the contents of this function are critical to its functioning.
** Modifying the contents of this function could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 050E32166A18037C27CFE10C84AEDFACC8AE2548
CREATE Function AnalyticsModel.func_PredictTags
(
    @partitionId INT,
    @workItemRevisionSK INT
)
RETURNS TABLE
AS
RETURN
    SELECT  t.PartitionId,
            t.TagSK,
            t.TagId,
            REVERSE(t.TagName) AS TagName,
            t.IsDeleted,
            t.ProjectSK
    FROM    AnalyticsModel.vw_Tag t
    JOIN    AnalyticsModel.tbl_WorkItemTag w
    ON      t.PartitionId = w.PartitionId
            AND t.TagSK = w.TagSK
    WHERE   t.PartitionId = @partitionId
            AND w.WorkItemRevisionSK = @workItemRevisionSK

GO

