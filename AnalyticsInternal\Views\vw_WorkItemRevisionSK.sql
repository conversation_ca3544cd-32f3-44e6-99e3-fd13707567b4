/******************************************************************************************************
** Warning: the contents of this view are critical to its functioning.
** Modifying the contents of view could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 05B9808B01233AE362E9AB4F84B5882713DCE14B
CREATE VIEW AnalyticsInternal.vw_WorkItemRevisionSK
AS
    SELECT  r.PartitionId,
            r.WorkItemId,
            r.Revision,
            r.WorkItemRevisionSK
    FROM AnalyticsModel.tbl_WorkItemHistory r WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemIdRev))
    UNION ALL
    SELECT  r.PartitionId,
            r.WorkItemId,
            r.Revision,
            r.WorkItemRevisionSK
    FROM AnalyticsModel.tbl_WorkItem r WITH (INDEX (IX_tbl_WorkItem_WorkItemIdRev))
    UNION ALL
    SELECT  r.PartitionId,
            r.WorkItemId,
            r.Revision,
            r.WorkItemRevisionSK
    FROM AnalyticsInternal.tbl_WorkItemRevisionReserved r WITH (INDEX (CL_AnalyticsInternal_tbl_WorkItemRevisionReserved))

GO

