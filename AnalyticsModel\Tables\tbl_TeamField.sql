CREATE TABLE [AnalyticsModel].[tbl_TeamField] (
    [PartitionId]            INT              NOT NULL,
    [AnalyticsCreatedDate]   DATETIME2 (7)    NOT NULL,
    [AnalyticsUpdatedDate]   DATETIME2 (7)    NOT NULL,
    [AnalyticsBatchId]       BIGINT           NOT NULL,
    [TeamFieldSK]            INT              IDENTITY (1, 1) NOT NULL,
    [ProjectId]              UNIQUEIDENTIFIER NOT NULL,
    [TeamFieldReferenceName] NVARCHAR (256)   NOT NULL,
    [TeamFieldValue]         NVARCHAR (4000)  NULL,
    [AreaId]                 UNIQUEIDENTIFIER NULL,
    [ProjectSK]              AS               ([ProjectId])
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_tbl_TeamField_TeamFieldValue]
    ON [AnalyticsModel].[tbl_TeamField]([PartitionId] ASC, [ProjectId] ASC, [TeamFieldReferenceName] ASC, [TeamFieldValue] ASC) WHERE ([TeamFieldReferenceName]<>'System.AreaPath');


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_TeamField]
    ON [AnalyticsModel].[tbl_TeamField]([PartitionId] ASC, [TeamFieldSK] ASC);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_tbl_TeamField_AreaId]
    ON [AnalyticsModel].[tbl_TeamField]([PartitionId] ASC, [ProjectId] ASC, [TeamFieldReferenceName] ASC, [AreaId] ASC) WHERE ([TeamFieldReferenceName]='System.AreaPath');


GO

