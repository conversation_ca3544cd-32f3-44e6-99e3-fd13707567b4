/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 6B7A5F623719E43FE777B4C1900525623FE88384
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTaskDefinitionReference_ModelBuildPipelineTask_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #Task
    (
        ProjectSK                UNIQUEIDENTIFIER NOT NULL,
        TaskDefinitionId         UNIQUEIDENTIFIER NOT NULL,
        TaskDefinitionName       NVARCHAR(400)    COLLATE DATABASE_DEFAULT NOT NULL,
        TaskDefinitionVersion    NVARCHAR(64)     COLLATE DATABASE_DEFAULT NOT NULL
    )

    INSERT  #Task (ProjectSK, TaskDefinitionId, TaskDefinitionName, TaskDefinitionVersion)
    SELECT  ProjectGuid,
            TaskDefinitionGuid,
            TaskDefinitionName,
            TaskDefinitionVersion
    FROM    AnalyticsStage.tbl_TaskDefinitionReference
    WHERE   PartitionId = @partitionId
            AND PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT AnalyticsModel.tbl_BuildPipelineTask
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        ProjectSK,
        TaskDefinitionId,
        TaskDefinitionName,
        TaskDefinitionVersion
    )
    SELECT TOP(@batchSizeMax)
           @partitionId,
           @batchDt,
           @batchDt,
           @batchId,
           s.ProjectSK,
           s.TaskDefinitionId,
           s.TaskDefinitionName,
           s.TaskDefinitionVersion
    FROM   #Task AS s
    LEFT JOIN AnalyticsModel.tbl_BuildPipelineTask AS t
    ON     t.PartitionId               = @partitionId
           AND t.ProjectSK             = s.ProjectSK
           AND t.TaskDefinitionId      = s.TaskDefinitionId
           AND t.TaskDefinitionVersion = s.TaskDefinitionVersion
    WHERE  t.PartitionId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT
    SET @complete = CASE WHEN @insertedCount < @batchSizeMax THEN 1 ELSE 0 END

    DROP TABLE #Task

    RETURN 0
END

GO

