CREATE TABLE [AnalyticsModel].[tbl_WorkItem_Deleted] (
    [PartitionId]             INT      NOT NULL,
    [AnalyticsDeletedDate]    DATETIME NOT NULL,
    [AnalyticsBatchIdDeleted] BIGINT   NOT NULL,
    [WorkItemRevisionSK]      INT      NOT NULL
);


GO

CREATE NONCLUSTERED INDEX [IX_AnalyticsModel_tbl_WorkItem_AxBatchIdDeleted]
    ON [AnalyticsModel].[tbl_WorkItem_Deleted]([PartitionId] ASC, [AnalyticsBatchIdDeleted] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE CLUSTERED INDEX [CI_AnalyticsModel_tbl_WorkItem_Deleted]
    ON [AnalyticsModel].[tbl_WorkItem_Deleted]([PartitionId] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

