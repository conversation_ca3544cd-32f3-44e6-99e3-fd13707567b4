/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 772237289BC21CB5F89FE03CA4E90E5D062AF3E7
CREATE PROCEDURE AnalyticsModel.prc_GetAreas
    @partitionId     INT,
    @areaIds         typ_GuidTable READONLY
AS
BEGIN

    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    BEGIN
        SELECT  ProjectSK,
                AreaSK,
                AreaId,
                AreaName,
                Number,
                AreaPath,
                Depth
        FROM    AnalyticsModel.tbl_Area a
        JOIN    @areaIds areaIds
        ON      a.AreaId = areaIds.Id
        WHERE   a.PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

END

GO

