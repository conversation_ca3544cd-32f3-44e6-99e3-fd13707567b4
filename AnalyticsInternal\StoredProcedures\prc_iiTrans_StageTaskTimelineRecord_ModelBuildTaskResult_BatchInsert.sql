/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FDD5D4CC56D0AF35B5CDACD49268AE9BC25DFE2F
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTaskTimelineRecord_ModelBuildTaskResult_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)
    DECLARE @popuplateJobSk BIT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'Target.PopulatePipelineJobSk'),0)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    CREATE TABLE #ImpactedTaskTimelineRecord
    (
        ProjectSK                UNIQUEIDENTIFIER NOT NULL,
        PipelineType             NVARCHAR(260)    COLLATE DATABASE_DEFAULT NOT NULL,
        PlanId                   INT              NOT NULL,
        TimelineId               INT              NOT NULL,
        TimelineRecordId         UNIQUEIDENTIFIER NOT NULL
    )
    IF (@popuplateJobSk = 0)
    BEGIN
        IF (@triggerBatchIdStart > 1 AND @stateData IS NULL) -- use batchID changed index for first batches
        BEGIN
            INSERT  #ImpactedTaskTimelineRecord (ProjectSK, PipelineType, PlanId, TimelineId, TimelineRecordId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    ProjectGuid,
                    PipelineType,
                    PlanId,
                    TimelineId,
                    TimelineRecordGuid
            FROM    AnalyticsStage.tbl_TaskTimelineRecord WITH (INDEX(IX_tbl_TaskTimelineRecord_AxBatchIdChanged))
            WHERE   PartitionId = @partitionId
                    AND PipelineType = 'Build'
                    AND [Type] = 'Task'
                    AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND PlanId > ISNULL(@stateData, -1)
            ORDER BY PlanId
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
        END
        ELSE --Otherwise use the clustered index that supports the sub-batching on PlanId
        BEGIN
            INSERT  #ImpactedTaskTimelineRecord (ProjectSK, PipelineType, PlanId, TimelineId, TimelineRecordId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    ProjectGuid,
                    PipelineType,
                    PlanId,
                    TimelineId,
                    TimelineRecordGuid
            FROM    AnalyticsStage.tbl_TaskTimelineRecord WITH (INDEX(CI_tbl_TaskTimelineRecord))
            WHERE   PartitionId = @partitionId
                    AND PipelineType = 'Build'
                    AND [Type] = 'Task'
                    AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND PlanId > ISNULL(@stateData, -1)
            ORDER BY PlanId
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
        END

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(PlanId) FROM #ImpactedTaskTimelineRecord)

        CREATE TABLE #TaskResult
        (
            ProjectSK                       UNIQUEIDENTIFIER    NULL,
            BuildId                         INT                 NULL,
            BuildPipelineSK                 INT                 NULL,
            BuildPipelineTaskSK             INT                 NULL,
            BranchSK                        INT                 NULL,
            BuildQueuedDateSK               INT                 NULL,
            BuildStartedDateSK              INT                 NULL,
            BuildCompletedDateSK            INT                 NULL,
            PlanId                          INT                 NULL,
            TimelineId                      INT                 NULL,
            TimelineRecordId                UNIQUEIDENTIFIER    NULL,
            ActivityStartedDateSK           INT                 NULL,
            ActivityStartedDate             DATETIMEOFFSET(0)   NULL,
            ActivityCompletedDateSK         INT                 NULL,
            ActivityCompletedDate           DATETIMEOFFSET(0)   NULL,
            TaskDisplayName                 NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
            TaskLogPath                     NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
            TaskOutcome                     TINYINT             NULL,
            SucceededCount                  INT                 NOT NULL,
            SucceededWithIssuesCount        INT                 NOT NULL,
            FailedCount                     INT                 NOT NULL,
            CanceledCount                   INT                 NOT NULL,
            SkippedCount                    INT                 NOT NULL,
            AbandonedCount                  INT                 NOT NULL,
            ActivityDurationSeconds         DECIMAL(18,3)       NULL,
            BuildOutcome                    TINYINT             NULL,
            TaskDefinitionReferenceId       INT                 NULL
        )

        INSERT #TaskResult
        (
            ProjectSK,
            BuildId,
            BuildPipelineSK,
            BuildPipelineTaskSK,
            BranchSK,
            BuildQueuedDateSK,
            BuildStartedDateSK,
            BuildCompletedDateSK,
            PlanId,
            TimelineId,
            TimelineRecordId,
            ActivityStartedDateSK,
            ActivityStartedDate,
            ActivityCompletedDateSK,
            ActivityCompletedDate,
            TaskDisplayName,
            TaskLogPath,
            TaskOutcome,
            SucceededCount,
            SucceededWithIssuesCount,
            FailedCount,
            CanceledCount,
            SkippedCount,
            AbandonedCount,
            ActivityDurationSeconds,
            BuildOutcome,
            TaskDefinitionReferenceId
        )
        SELECT  sttr.ProjectGuid,
                sb.BuildId,
                mbp.BuildPipelineSK,
                mbpt.BuildPipelineTaskSK,
                mbb.BranchSK,
                AnalyticsInternal.func_GenDateSK(sb.QueueTime AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(sb.StartTime AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(sb.FinishTime AT TIME ZONE @timeZone),
                sttr.PlanId,
                sttr.TimelineId,
                sttr.TimelineRecordGuid,
                AnalyticsInternal.func_GenDateSK(sttr.StartTime AT TIME ZONE @timeZone),
                sttr.StartTime AT TIME ZONE @timeZone,
                AnalyticsInternal.func_GenDateSK(sttr.FinishTime AT TIME ZONE @timeZone),
                sttr.FinishTime AT TIME ZONE @timeZone,
                sttr.Name,
                sttr.LogPath,
                sttr.Result,
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 0, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 1, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 2, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 3, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 4, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 5, 1, 0)),
                DATEDIFF_BIG(millisecond, sttr.StartTime, sttr.FinishTime) / 1000.0,
                sb.Result,
                sttr.TaskDefinitionReferenceId
        FROM    #ImpactedTaskTimelineRecord trec
        JOIN    AnalyticsStage.tbl_TaskTimelineRecord sttr WITH (FORCESEEK(CI_tbl_TaskTimelineRecord(PartitionId, PlanId, ProjectGuid, PipelineType, TimelineId, TimelineRecordGuid)))
        ON      trec.ProjectSK = sttr.ProjectGuid
                AND trec.PipelineType = sttr.PipelineType
                AND trec.PlanId = sttr.PlanId
                AND trec.TimelineId = sttr.TimelineId
                AND trec.TimelineRecordId = sttr.TimelineRecordGuid
        LEFT JOIN AnalyticsStage.tbl_TaskDefinitionReference stdr
        ON      stdr.PartitionId = @partitionId
                AND stdr.ProjectGuid = sttr.ProjectGuid
                AND stdr.PipelineType = sttr.PipelineType
                AND stdr.TaskDefinitionReferenceId = sttr.TaskDefinitionReferenceId
        LEFT JOIN AnalyticsModel.tbl_BuildPipelineTask mbpt
        ON      mbpt.PartitionId = @partitionId
                AND mbpt.ProjectSK = stdr.ProjectGuid
                AND mbpt.TaskDefinitionId = stdr.TaskDefinitionGuid
                AND mbpt.TaskDefinitionVersion = stdr.TaskDefinitionVersion
        JOIN    AnalyticsStage.tbl_TaskPlan stp
        ON      stp.PartitionId = @partitionId
                AND stp.ProjectGuid = trec.ProjectSK
                AND stp.PipelineType = trec.PipelineType
                AND stp.PlanId = trec.PlanId
        JOIN    AnalyticsStage.tbl_Build sb
        ON      sb.PartitionId = @partitionId
                AND sb.ProjectGuid = stp.ProjectGuid
                AND sb.PlanId = stp.PlanGuid
        JOIN    AnalyticsModel.tbl_BuildPipeline mbp
        ON      mbp.PartitionId = @partitionId
                AND mbp.BuildPipelineId = sb.DefinitionId
        JOIN    AnalyticsModel.tbl_Branch mbb
        ON      mbb.PartitionId = @partitionId
                AND mbb.RepositoryId = sb.RepositoryId
                AND mbb.BranchName = sb.BranchName
        WHERE   sttr.PartitionId = @partitionId
        OPTION  (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN))

        -- Delete Timeline recods for which there has to task defn associated but task defn reference has not arrived. These will be inserted with trigger on StageTaskDefinitionReference.
        DELETE  tr
        FROM    #TaskResult tr
        WHERE   TaskDefinitionReferenceId IS NOT NULL
                AND BuildPipelineTaskSK IS NULL

        INSERT AnalyticsModel.tbl_BuildTaskResult
        (
                  PartitionId
                , AnalyticsCreatedDate
                , AnalyticsUpdatedDate
                , AnalyticsBatchId
                , ProjectSK
                , BuildId
                , BuildPipelineSK
                , BuildPipelineTaskSK
                , BranchSK
                , BuildQueuedDateSK
                , BuildStartedDateSK
                , BuildCompletedDateSK
                , PlanId
                , TimelineId
                , TimelineRecordId
                , ActivityStartedDateSK
                , ActivityStartedDate
                , ActivityCompletedDateSK
                , ActivityCompletedDate
                , TaskDisplayName
                , TaskLogPath
                , TaskOutcome
                , SucceededCount
                , SucceededWithIssuesCount
                , FailedCount
                , CanceledCount
                , SkippedCount
                , AbandonedCount
                , ActivityDurationSeconds
                , BuildOutcome
        )
        SELECT    @partitionId
                , @batchDt
                , @batchDt
                , @batchId
                , s.ProjectSK
                , s.BuildId
                , s.BuildPipelineSK
                , s.BuildPipelineTaskSK
                , s.BranchSK
                , s.BuildQueuedDateSK
                , s.BuildStartedDateSK
                , s.BuildCompletedDateSK
                , s.PlanId
                , s.TimelineId
                , s.TimelineRecordId
                , s.ActivityStartedDateSK
                , s.ActivityStartedDate
                , s.ActivityCompletedDateSK
                , s.ActivityCompletedDate
                , s.TaskDisplayName
                , s.TaskLogPath
                , s.TaskOutcome
                , s.SucceededCount
                , s.SucceededWithIssuesCount
                , s.FailedCount
                , s.CanceledCount
                , s.SkippedCount
                , s.AbandonedCount
                , s.ActivityDurationSeconds
                , s.BuildOutcome
        FROM    #TaskResult s
        LEFT JOIN AnalyticsModel.tbl_BuildTaskResult mbtr
        ON      mbtr.PartitionId = @partitionId
                AND mbtr.ProjectSK = s.ProjectSK
                AND mbtr.PlanId = s.PlanId
                AND mbtr.TimelineId = s.TimelineId
                AND mbtr.TimelineRecordId = s.TimelineRecordId
        WHERE   mbtr.PartitionId IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @insertedCount = @@ROWCOUNT

        DROP TABLE #ImpactedTaskTimelineRecord
        DROP TABLE #TaskResult
    END
    ELSE
    BEGIN
        IF (@triggerBatchIdStart > 1 AND @stateData IS NULL) -- use batchID changed index for first batches
        BEGIN
            INSERT  #ImpactedTaskTimelineRecord (ProjectSK, PipelineType, PlanId, TimelineId, TimelineRecordId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    ProjectGuid,
                    PipelineType,
                    PlanId,
                    TimelineId,
                    TimelineRecordGuid
            FROM    AnalyticsStage.tbl_TaskTimelineRecord WITH (INDEX(IX_tbl_TaskTimelineRecord_AxBatchIdChanged))
            WHERE   PartitionId = @partitionId
                    AND PipelineType = 'Build'
                    AND [Type] IN ('Task', 'Job')
                    AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND PlanId > ISNULL(@stateData, -1)
            ORDER BY PlanId
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
        END
        ELSE --Otherwise use the clustered index that supports the sub-batching on PlanId
        BEGIN
            INSERT  #ImpactedTaskTimelineRecord (ProjectSK, PipelineType, PlanId, TimelineId, TimelineRecordId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    ProjectGuid,
                    PipelineType,
                    PlanId,
                    TimelineId,
                    TimelineRecordGuid
            FROM    AnalyticsStage.tbl_TaskTimelineRecord WITH (INDEX(CI_tbl_TaskTimelineRecord))
            WHERE   PartitionId = @partitionId
                    AND PipelineType = 'Build'
                    AND [Type] IN ('Task', 'Job')
                    AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND PlanId > ISNULL(@stateData, -1)
            ORDER BY PlanId
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
        END

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(PlanId) FROM #ImpactedTaskTimelineRecord)

        CREATE TABLE #TimelineRecordResult
        (
            ProjectSK                       UNIQUEIDENTIFIER    NULL,
            BuildId                         INT                 NULL,
            BuildPipelineSK                 INT                 NULL,
            BuildPipelineTaskSK             INT                 NULL,
            BranchSK                        INT                 NULL,
            BuildQueuedDateSK               INT                 NULL,
            BuildStartedDateSK              INT                 NULL,
            BuildCompletedDateSK            INT                 NULL,
            PlanId                          INT                 NULL,
            TimelineId                      INT                 NULL,
            TimelineRecordId                UNIQUEIDENTIFIER    NULL,
            ActivityStartedDateSK           INT                 NULL,
            ActivityStartedDate             DATETIMEOFFSET(0)   NULL,
            ActivityCompletedDateSK         INT                 NULL,
            ActivityCompletedDate           DATETIMEOFFSET(0)   NULL,
            TaskDisplayName                 NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
            TaskLogPath                     NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
            TaskOutcome                     TINYINT             NULL,
            SucceededCount                  INT                 NOT NULL,
            SucceededWithIssuesCount        INT                 NOT NULL,
            FailedCount                     INT                 NOT NULL,
            CanceledCount                   INT                 NOT NULL,
            SkippedCount                    INT                 NOT NULL,
            AbandonedCount                  INT                 NOT NULL,
            ActivityDurationSeconds         DECIMAL(18,3)       NULL,
            BuildOutcome                    TINYINT             NULL,
            TaskDefinitionReferenceId       INT                 NULL,
            PipelineJobSk                   INT                 NULL,
        )

        INSERT #TimelineRecordResult
        (
            ProjectSK,
            BuildId,
            BuildPipelineSK,
            BuildPipelineTaskSK,
            BranchSK,
            BuildQueuedDateSK,
            BuildStartedDateSK,
            BuildCompletedDateSK,
            PlanId,
            TimelineId,
            TimelineRecordId,
            ActivityStartedDateSK,
            ActivityStartedDate,
            ActivityCompletedDateSK,
            ActivityCompletedDate,
            TaskDisplayName,
            TaskLogPath,
            TaskOutcome,
            SucceededCount,
            SucceededWithIssuesCount,
            FailedCount,
            CanceledCount,
            SkippedCount,
            AbandonedCount,
            ActivityDurationSeconds,
            BuildOutcome,
            TaskDefinitionReferenceId,
            PipelineJobSk
        )
        SELECT  sttr.ProjectGuid,
                sb.BuildId,
                mbp.BuildPipelineSK,
                mbpt.BuildPipelineTaskSK,
                mbb.BranchSK,
                AnalyticsInternal.func_GenDateSK(sb.QueueTime AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(sb.StartTime AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(sb.FinishTime AT TIME ZONE @timeZone),
                sttr.PlanId,
                sttr.TimelineId,
                sttr.TimelineRecordGuid,
                AnalyticsInternal.func_GenDateSK(sttr.StartTime AT TIME ZONE @timeZone),
                sttr.StartTime AT TIME ZONE @timeZone,
                AnalyticsInternal.func_GenDateSK(sttr.FinishTime AT TIME ZONE @timeZone),
                sttr.FinishTime AT TIME ZONE @timeZone,
                IIF(sttr.[Type] = 'Task', sttr.Name , 'Job Failure For Agent'), -- other allowed type, other than task is failed Job only,
                sttr.LogPath,
                sttr.Result,
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 0, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 1, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 2, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 3, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 4, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 5, 1, 0)),
                DATEDIFF_BIG(millisecond, sttr.StartTime, sttr.FinishTime) / 1000.0,
                sb.Result,
                sttr.TaskDefinitionReferenceId,
                mpj.PipelineJobSk
        FROM    #ImpactedTaskTimelineRecord trec
        JOIN    AnalyticsStage.tbl_TaskTimelineRecord sttr WITH (FORCESEEK(CI_tbl_TaskTimelineRecord(PartitionId, PlanId, ProjectGuid, PipelineType, TimelineId, TimelineRecordGuid)))
        ON      trec.ProjectSK = sttr.ProjectGuid
                AND trec.PipelineType = sttr.PipelineType
                AND trec.PlanId = sttr.PlanId
                AND trec.TimelineId = sttr.TimelineId
                AND trec.TimelineRecordId = sttr.TimelineRecordGuid
        LEFT JOIN AnalyticsStage.tbl_TaskDefinitionReference stdr WITH (FORCESEEK(CI_tbl_TaskDefinitionReference(PartitionId, ProjectGuid, PipelineType, TaskDefinitionReferenceId)))
        ON      stdr.PartitionId = @partitionId
                AND stdr.ProjectGuid = sttr.ProjectGuid
                AND stdr.PipelineType = sttr.PipelineType
                AND stdr.TaskDefinitionReferenceId = sttr.TaskDefinitionReferenceId
        LEFT JOIN AnalyticsModel.tbl_BuildPipelineTask mbpt
        ON      mbpt.PartitionId = @partitionId
                AND mbpt.ProjectSK = stdr.ProjectGuid
                AND mbpt.TaskDefinitionId = stdr.TaskDefinitionGuid
                AND mbpt.TaskDefinitionVersion = stdr.TaskDefinitionVersion
        JOIN    AnalyticsStage.tbl_TaskPlan stp  WITH (FORCESEEK(CI_tbl_TaskPlan(PartitionId, ProjectGuid, PipelineType, PlanId)))
        ON      stp.PartitionId = @partitionId
                AND stp.ProjectGuid = trec.ProjectSK
                AND stp.PipelineType = trec.PipelineType
                AND stp.PlanId = trec.PlanId
        JOIN    AnalyticsStage.tbl_Build sb  WITH (FORCESEEK(IX_tbl_Build_PlanId(PartitionId, PlanId)))
        ON      sb.PartitionId = @partitionId
                AND sb.ProjectGuid = stp.ProjectGuid
                AND sb.PlanId = stp.PlanGuid
        JOIN    AnalyticsModel.tbl_BuildPipeline mbp
        ON      mbp.PartitionId = @partitionId
                AND mbp.BuildPipelineId = sb.DefinitionId
        JOIN    AnalyticsModel.tbl_Branch mbb
        ON      mbb.PartitionId = @partitionId
                AND mbb.RepositoryId = sb.RepositoryId
                AND mbb.BranchName = sb.BranchName
        LEFT JOIN AnalyticsModel.tbl_PipelineJob mpj
        ON      mpj.PartitionId = sttr.PartitionId
                AND mpj.BuildPipelineId = sb.DefinitionId
                AND (mpj.StageIdentifier = sttr.StageIdentifier OR (mpj.StageIdentifier IS NULL AND sttr.StageIdentifier IS NULL))
                AND (mpj.PhaseIdentifier = sttr.PhaseIdentifier OR (mpj.PhaseIdentifier IS NULL AND sttr.PhaseIdentifier IS NULL))
                AND (mpj.JobIdentifier = IIF(Sttr.[Type] = 'Job', sttr.Identifier, sttr.JobIdentifier)  OR (mpj.JobIdentifier IS NULL AND sttr.JobIdentifier IS NULL))
        WHERE   sttr.PartitionId = @partitionId
                AND
                    ( sttr.[Type] = 'Task'
                        OR (
                            Sttr.[Type] = 'Job'
                            AND mpj.PartitionId IS NOT NULL
                            AND sttr.Result = 2
                            AND (
                                  sttr.IsJobFailureDueToTask = 0
                                  OR sttr.IsJobFailureDueToTask IS NULL
                                )
                          )
                      )
                 AND (stdr.TaskDefinitionReferenceId IS NULL OR stdr.AnalyticsBatchIdChanged <= @triggerBatchIdEnd)
                 AND sb.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
                 AND stp.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
        OPTION  (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN))

        -- Delete Timeline recods for which there has to task defn associated but task defn reference has not arrived. These will be inserted with trigger on StageTaskDefinitionReference.
        DELETE  tr
        FROM    #TimelineRecordResult tr
        WHERE   TaskDefinitionReferenceId IS NOT NULL
                AND BuildPipelineTaskSK IS NULL

        INSERT AnalyticsModel.tbl_BuildTaskResult
        (
                  PartitionId
                , AnalyticsCreatedDate
                , AnalyticsUpdatedDate
                , AnalyticsBatchId
                , ProjectSK
                , BuildId
                , BuildPipelineSK
                , BuildPipelineTaskSK
                , BranchSK
                , BuildQueuedDateSK
                , BuildStartedDateSK
                , BuildCompletedDateSK
                , PlanId
                , TimelineId
                , TimelineRecordId
                , ActivityStartedDateSK
                , ActivityStartedDate
                , ActivityCompletedDateSK
                , ActivityCompletedDate
                , TaskDisplayName
                , TaskLogPath
                , TaskOutcome
                , SucceededCount
                , SucceededWithIssuesCount
                , FailedCount
                , CanceledCount
                , SkippedCount
                , AbandonedCount
                , ActivityDurationSeconds
                , BuildOutcome
                , PipelineJobSk
        )
        SELECT    @partitionId
                , @batchDt
                , @batchDt
                , @batchId
                , s.ProjectSK
                , s.BuildId
                , s.BuildPipelineSK
                , s.BuildPipelineTaskSK
                , s.BranchSK
                , s.BuildQueuedDateSK
                , s.BuildStartedDateSK
                , s.BuildCompletedDateSK
                , s.PlanId
                , s.TimelineId
                , s.TimelineRecordId
                , s.ActivityStartedDateSK
                , s.ActivityStartedDate
                , s.ActivityCompletedDateSK
                , s.ActivityCompletedDate
                , s.TaskDisplayName
                , s.TaskLogPath
                , s.TaskOutcome
                , s.SucceededCount
                , s.SucceededWithIssuesCount
                , s.FailedCount
                , s.CanceledCount
                , s.SkippedCount
                , s.AbandonedCount
                , s.ActivityDurationSeconds
                , s.BuildOutcome
                , s.PipelineJobSk
        FROM    #TimelineRecordResult s
        LEFT JOIN AnalyticsModel.tbl_BuildTaskResult mbtr
        ON      mbtr.PartitionId = @partitionId
                AND mbtr.ProjectSK = s.ProjectSK
                AND mbtr.PlanId = s.PlanId
                AND mbtr.TimelineId = s.TimelineId
                AND mbtr.TimelineRecordId = s.TimelineRecordId
        WHERE   mbtr.PartitionId IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @insertedCount = @@ROWCOUNT

        DROP TABLE #ImpactedTaskTimelineRecord
        DROP TABLE #TimelineRecordResult
    END
    RETURN 0
END

GO

