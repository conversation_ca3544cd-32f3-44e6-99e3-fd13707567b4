/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 05F4A1E94AF9617781239C03FAACEFEF07B163E9
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestSuite_ModelTestSuite_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    -- Mentioning primary key to avoid sorting #ChangedTestSuites when joining with other tables
    -- We take (TestSuiteId, ProjectGuid) to be unique since in rare cases, test suites could have moved across projects
    CREATE TABLE #ChangedTestSuites
    (
        TestSuiteId INT NOT NULL,
        ProjectGuid UNIQUEIDENTIFIER NOT NULL,
        TestPlanId  INT NOT NULL,
        Depth       INT

        INDEX IX_ChangedTestSuites_TestSuiteId_ProjectGuid UNIQUE CLUSTERED (TestSuiteId, ProjectGuid)
    )

    -- Mentioning primary key to avoid sorting #ChangedTestPlans when joining with other tables
    -- We take (TestPlanId, ProjectGuid) to be unique since in rare cases, test suites could have moved across projects
    CREATE TABLE #ChangedTestPlans
    (
        TestPlanId  INT NOT NULL,
        ProjectGuid UNIQUEIDENTIFIER NOT NULL

        INDEX IX_ChangedTestPlans_TestPlanId_ProjectGuid UNIQUE CLUSTERED (TestPlanId, ProjectGuid)
    )

    CREATE TABLE #LatestWorkItemRevsForTestSuites
    (
        WorkItemId  INT NOT NULL,
        ProjectGuid UNIQUEIDENTIFIER NOT NULL,
        Title       NVARCHAR(256) COLLATE DATABASE_DEFAULT

        INDEX IX_LatestWorkItemRevsForTestSuites_WorkItemId_ProjectGuid UNIQUE CLUSTERED (WorkItemId, ProjectGuid)
    )

    CREATE TABLE #LatestWorkItemRevsForTestPlans
    (
        WorkItemId  INT NOT NULL,
        ProjectGuid UNIQUEIDENTIFIER NOT NULL,
        Title       NVARCHAR(256) COLLATE DATABASE_DEFAULT

        INDEX IX_LatestWorkItemRevsForTestPlans_WorkItemId_ProjectGuid UNIQUE CLUSTERED (WorkItemId, ProjectGuid)
    )

    CREATE TABLE #ChangedTestSuiteSubtree
    (
        TestSuiteId INT NOT NULL PRIMARY KEY,
        Title       NVARCHAR(256) COLLATE DATABASE_DEFAULT,
        Depth       INT,
        IsDeleted   BIT
    )

    INSERT   #ChangedTestSuites (TestSuiteId, ProjectGuid, TestPlanId, Depth)
    SELECT   TOP (@batchSizeMax) WITH TIES s.TestSuiteId, s.ProjectGuid, s.TestPlanId, LEN(s.SuitePath)/4
    FROM     AnalyticsStage.tbl_TestSuite s WITH (INDEX (IX_tbl_TestSuite_AxBatchIdChanged), FORCESEEK)
    WHERE    s.PartitionId = @partitionId
             AND s.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
             AND s.TestSuiteId > ISNULL(@stateData, 0)
    ORDER BY s.TestSuiteId
    OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete     = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT TOP 1 TestSuiteId FROM #ChangedTestSuites ORDER BY TestSuiteId DESC)

    INSERT #ChangedTestPlans (TestPlanId, ProjectGuid)
    SELECT DISTINCT TestplanId, ProjectGuid
    FROM   #ChangedTestSuites cs

    INSERT #LatestWorkItemRevsForTestSuites (WorkItemId, ProjectGuid, Title)
    SELECT cts.TestSuiteId, cts.ProjectGuid, TestSuite.System_Title
    FROM   #ChangedTestSuites cts
    CROSS APPLY (
        SELECT   TOP 1 System_Title
        FROM     AnalyticsStage.tbl_WorkItemRevision wir WITH (INDEX (CI_tbl_WorkItemRevision), FORCESEEK)
        WHERE    wir.PartitionId = @partitionId
                 AND wir.System_Id = cts.TestSuiteId
                 AND wir.System_ProjectGuid = cts.ProjectGuid
        ORDER BY System_Rev DESC
    ) TestSuite

    INSERT #LatestWorkItemRevsForTestPlans (WorkItemId, ProjectGuid, Title)
    SELECT ctp.TestPlanId, ctp.ProjectGuid, TestPlan.System_Title
    FROM   #ChangedTestPlans ctp
    CROSS APPLY (
        SELECT   TOP 1 System_Title
        FROM     AnalyticsStage.tbl_WorkItemRevision wir WITH (INDEX (CI_tbl_WorkItemRevision), FORCESEEK)
        WHERE    wir.PartitionId = @partitionId
                 AND wir.System_Id = ctp.TestPlanId
                 AND wir.System_ProjectGuid = ctp.ProjectGuid
        ORDER BY System_Rev DESC
    ) TestPlan

    CREATE TABLE #TestSuite
    (
        ProjectSK              UNIQUEIDENTIFIER    NOT NULL,
        TestPlanId             INT                 NOT NULL,
        TestSuiteId            INT                 NOT NULL,
        TestPlanTitle          NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        TestPlanState          TINYINT             NULL,
        ParentSuiteId          INT                 NULL,
        Title                  NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        OrderId                INT                 NULL,
        IsDeleted              BIT                 NOT NULL,
        IdLevel1               INT                 NULL,
        IdLevel2               INT                 NULL,
        IdLevel3               INT                 NULL,
        IdLevel4               INT                 NULL,
        IdLevel5               INT                 NULL,
        IdLevel6               INT                 NULL,
        IdLevel7               INT                 NULL,
        IdLevel8               INT                 NULL,
        IdLevel9               INT                 NULL,
        IdLevel10              INT                 NULL,
        IdLevel11              INT                 NULL,
        IdLevel12              INT                 NULL,
        IdLevel13              INT                 NULL,
        IdLevel14              INT                 NULL,
        Depth                  TINYINT             NULL,
        Type                   TINYINT             NULL,
        RequirementWorkItemId  INT                 NULL,
        DataSourceId           INT                 NOT NULL
    )

    INSERT  #TestSuite
    (
        ProjectSK,
        TestPlanId,
        TestSuiteId,
        TestPlanTitle,
        TestPlanState,
        ParentSuiteId,
        Title,
        OrderId,
        IsDeleted,
        IdLevel1,
        IdLevel2,
        IdLevel3,
        IdLevel4,
        IdLevel5,
        IdLevel6,
        IdLevel7,
        IdLevel8,
        IdLevel9,
        IdLevel10,
        IdLevel11,
        IdLevel12,
        IdLevel13,
        IdLevel14,
        Depth,
        Type,
        RequirementWorkItemId,
        DataSourceId
    )
    SELECT    s.ProjectGuid,
              s.TestPlanId,
              s.TestSuiteId,
              tpr.Title,
              p.State,
              s.ParentSuiteId,
              tsr.Title,
              s.SequenceNumber,
              (s.IsDeleted | IIF (p.State IS NULL, 0, IIF (p.State = 255, 1, 0))), -- Set IsDeleted if Suite is deleted of plan is deleted
              IIF(CAST(SUBSTRING(s.SuitePath, 0, 5) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 0, 5) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 5, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 5, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 9, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 9, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 13, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 13, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 17, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 17, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 21, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 21, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 25, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 25, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 29, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 29, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 33, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 33, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 37, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 37, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 41, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 41, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 45, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 45, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 49, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 49, 4) AS INT), NULL),
              IIF(CAST(SUBSTRING(s.SuitePath, 53, 4) AS INT) <> 0, CAST(SUBSTRING(s.SuitePath, 53, 4) AS INT), NULL),
              DATALENGTH(SuitePath)/4,
              s.SuiteType,
              IIF(s.RequirementId = 0, NULL, s.RequirementId),
              s.DataSourceId
    FROM      AnalyticsStage.tbl_TestSuite s WITH (INDEX (CI_tbl_TestSuite), FORCESEEK)
    JOIN      #ChangedTestSuites cs
    ON        s.TestSuiteId = cs.TestSuiteId
              AND s.ProjectGuid = cs.ProjectGuid
    LEFT JOIN AnalyticsStage.tbl_TestPlan p WITH (INDEX (CI_tbl_TestPlan), FORCESEEK)
    ON        p.PartitionId = @partitionId
              AND p.TestPlanId = s.TestPlanId
              AND p.ProjectGuid = s.ProjectGuid
    LEFT JOIN #LatestWorkItemRevsForTestSuites tsr
    ON        tsr.WorkItemId = s.TestSuiteId
              AND tsr.ProjectGuid = s.ProjectGuid
    LEFT JOIN #LatestWorkItemRevsForTestPlans tpr
    ON        tpr.WorkItemId = s.TestPlanId
              AND tpr.ProjectGuid = s.ProjectGuid
    WHERE     s.PartitionId = @partitionId
    OPTION    (OPTIMIZE FOR (@partitionId UNKNOWN))

    MERGE AnalyticsModel.tbl_TestSuite t
    USING #TestSuite s
    ON    (t.PartitionId = @partitionId AND t.ProjectSK = s.ProjectSK AND t.TestSuiteId = s.TestSuiteId AND t.DataSourceId = s.DataSourceId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.TestPlanId,
        s.TestPlanTitle,
        s.TestPlanState,
        s.ParentSuiteId,
        s.Title,
        s.OrderId,
        s.IsDeleted,
        s.IdLevel1,
        s.IdLevel2,
        s.IdLevel3,
        s.IdLevel4,
        s.IdLevel5,
        s.IdLevel6,
        s.IdLevel7,
        s.IdLevel8,
        s.IdLevel9,
        s.IdLevel10,
        s.IdLevel11,
        s.IdLevel12,
        s.IdLevel13,
        s.IdLevel14,
        s.Depth,
        s.Type,
        s.RequirementWorkItemId
        INTERSECT
        SELECT
        t.TestPlanId,
        t.TestPlanTitle,
        t.TestPlanState,
        t.ParentSuiteId,
        t.Title,
        t.OrderId,
        t.IsDeleted,
        t.IdLevel1,
        t.IdLevel2,
        t.IdLevel3,
        t.IdLevel4,
        t.IdLevel5,
        t.IdLevel6,
        t.IdLevel7,
        t.IdLevel8,
        t.IdLevel9,
        t.IdLevel10,
        t.IdLevel11,
        t.IdLevel12,
        t.IdLevel13,
        t.IdLevel14,
        t.Depth,
        t.Type,
        t.RequirementWorkItemId
    ) THEN
    UPDATE SET
        AnalyticsUpdatedDate   = @batchDt,
        AnalyticsBatchId       = @batchId,
        TestPlanId             = s.TestPlanId,
        TestPlanTitle          = s.TestPlanTitle,
        TestPlanState          = s.TestPlanState,
        ParentSuiteId          = s.ParentSuiteId,
        Title                  = s.Title,
        OrderId                = s.OrderId,
        IsDeleted              = s.IsDeleted,
        IdLevel1               = s.IdLevel1,
        TitleLevel1            = IIF(s.IdLevel1 IS NULL, NULL, t.TitleLevel1),
        IdLevel2               = s.IdLevel2,
        TitleLevel2            = IIF(s.IdLevel2 IS NULL, NULL, t.TitleLevel2),
        IdLevel3               = s.IdLevel3,
        TitleLevel3            = IIF(s.IdLevel3 IS NULL, NULL, t.TitleLevel3),
        IdLevel4               = s.IdLevel4,
        TitleLevel4            = IIF(s.IdLevel4 IS NULL, NULL, t.TitleLevel4),
        IdLevel5               = s.IdLevel5,
        TitleLevel5            = IIF(s.IdLevel5 IS NULL, NULL, t.TitleLevel5),
        IdLevel6               = s.IdLevel6,
        TitleLevel6            = IIF(s.IdLevel6 IS NULL, NULL, t.TitleLevel6),
        IdLevel7               = s.IdLevel7,
        TitleLevel7            = IIF(s.IdLevel7 IS NULL, NULL, t.TitleLevel7),
        IdLevel8               = s.IdLevel8,
        TitleLevel8            = IIF(s.IdLevel8 IS NULL, NULL, t.TitleLevel8),
        IdLevel9               = s.IdLevel9,
        TitleLevel9            = IIF(s.IdLevel9 IS NULL, NULL, t.TitleLevel9),
        IdLevel10              = s.IdLevel10,
        TitleLevel10           = IIF(s.IdLevel10 IS NULL, NULL, t.TitleLevel10),
        IdLevel11              = s.IdLevel11,
        TitleLevel11           = IIF(s.IdLevel11 IS NULL, NULL, t.TitleLevel11),
        IdLevel12              = s.IdLevel12,
        TitleLevel12           = IIF(s.IdLevel12 IS NULL, NULL, t.TitleLevel12),
        IdLevel13              = s.IdLevel13,
        TitleLevel13           = IIF(s.IdLevel13 IS NULL, NULL, t.TitleLevel13),
        IdLevel14              = s.IdLevel14,
        TitleLevel14           = IIF(s.IdLevel14 IS NULL, NULL, t.TitleLevel14),
        Depth                  = s.Depth,
        Type                   = s.Type,
        RequirementWorkItemId  = s.RequirementWorkItemId
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        ProjectSK,
        TestPlanId,
        TestSuiteId,
        TestPlanTitle,
        TestPlanState,
        ParentSuiteId,
        Title,
        OrderId,
        IsDeleted,
        IdLevel1,
        IdLevel2,
        IdLevel3,
        IdLevel4,
        IdLevel5,
        IdLevel6,
        IdLevel7,
        IdLevel8,
        IdLevel9,
        IdLevel10,
        IdLevel11,
        IdLevel12,
        IdLevel13,
        IdLevel14,
        Depth,
        Type,
        RequirementWorkItemId
    )
    VALUES (
        @partitionId,
        @batchDt,
        @batchDt,
        @batchId,
        s.ProjectSK,
        s.TestPlanId,
        s.TestSuiteId,
        s.TestPlanTitle,
        s.TestPlanState,
        s.ParentSuiteId,
        s.Title,
        s.OrderId,
        s.IsDeleted,
        s.IdLevel1,
        s.IdLevel2,
        s.IdLevel3,
        s.IdLevel4,
        s.IdLevel5,
        s.IdLevel6,
        s.IdLevel7,
        s.IdLevel8,
        s.IdLevel9,
        s.IdLevel10,
        s.IdLevel11,
        s.IdLevel12,
        s.IdLevel13,
        s.IdLevel14,
        s.Depth,
        s.Type,
        s.RequirementWorkItemId
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    -- List all test suites with their titles and depths that form a subtree which contains the test suites in current batch and all their ancestors
    ;WITH TestSuitesAndAncestors AS (
        SELECT TestSuiteId, ParentSuiteId, Title, Depth, IsDeleted FROM #TestSuite

        UNION ALL

        SELECT     mts.TestSuiteId, mts.ParentSuiteId, mts.Title, mts.Depth, mts.IsDeleted FROM AnalyticsModel.tbl_TestSuite mts
        INNER JOIN TestSuitesAndAncestors
        ON         TestSuitesAndAncestors.ParentSuiteId = mts.TestSuiteId
        WHERE      mts.PartitionId = @partitionId
    )

    INSERT INTO #ChangedTestSuiteSubtree (TestSuiteId, Title, Depth, IsDeleted)
    SELECT DISTINCT TestSuiteId, Title, Depth, IsDeleted FROM TestSuitesAndAncestors

    DECLARE @DistinctChangedDepths dbo.typ_Int32Table

    INSERT INTO @DistinctChangedDepths (Val)
    SELECT DISTINCT Depth FROM #ChangedTestSuiteSubtree

    -- Update the suite information from level 1 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 1)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel1          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel1), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel1 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel1
               )
    END

    -- Update the suite information from level 2 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 2)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel2          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel2), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel2 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel2
               )
    END

    -- Update the suite information from level 3 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 3)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel3          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel3), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel3 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel3
               )
    END

    -- Update the suite information from level 4 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 4)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel4          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel4), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel4 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel4
               )
    END

    -- Update the suite information from level 5 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 5)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel5          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel5), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel5 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel5
               )
    END

    -- Update the suite information from level 6 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 6)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel6          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel6), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel6 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel6
               )
    END

    -- Update the suite information from level 7 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 7)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel7          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel7), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel7 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel7
               )
    END

    -- Update the suite information from level 8 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 8)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel8          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel8), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel8 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel8
               )
    END

    -- Update the suite information from level 9 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 9)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel9          = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel9), FORCESEEK)
        ON     t.PartitionId  = @partitionId
               AND t.IdLevel9 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel9
               )
    END

    -- Update the suite information from level 10 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 10)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel10         = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel10), FORCESEEK)
        ON     t.PartitionId   = @partitionId
               AND t.IdLevel10 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel10
               )
    END

    -- Update the suite information from level 11 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 11)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel11         = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel11), FORCESEEK)
        ON     t.PartitionId   = @partitionId
               AND t.IdLevel11 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel11
               )
    END

    -- Update the suite information from level 12 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 12)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel12         = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel12), FORCESEEK)
        ON     t.PartitionId   = @partitionId
               AND t.IdLevel12 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel12
               )
    END

    -- Update the suite information from level 13 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 13)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel13         = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel13), FORCESEEK)
        ON     t.PartitionId   = @partitionId
               AND t.IdLevel13 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel13
               )
    END

    -- Update the suite information from level 14 suites in model table
    IF EXISTS (SELECT 1 FROM @DistinctChangedDepths WHERE Val = 14)
    BEGIN
        UPDATE t
        SET    AnalyticsUpdatedDate = @batchDt,
               AnalyticsBatchId     = @batchId,
               TitleLevel14         = ctss.Title,
               IsDeleted            = ctss.IsDeleted
        OUTPUT 'UPDATE' INTO @changes
        FROM   #ChangedTestSuiteSubtree ctss
        JOIN   AnalyticsModel.tbl_TestSuite t WITH (INDEX (IX_tbl_TestSuite_IdLevel14), FORCESEEK)
        ON     t.PartitionId   = @partitionId
               AND t.IdLevel14 = ctss.TestSuiteId
               AND NOT EXISTS
               (
                   SELECT ctss.IsDeleted,
                          ctss.Title
                   INTERSECT
                   SELECT t.IsDeleted,
                          t.TitleLevel14
               )
    END

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount  = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    DROP TABLE #TestSuite
    DROP TABLE #ChangedTestSuites
    DROP TABLE #ChangedTestPlans
    DROP TABLE #LatestWorkItemRevsForTestSuites
    DROP TABLE #LatestWorkItemRevsForTestPlans
    DROP TABLE #ChangedTestSuiteSubtree

    RETURN 0
END

GO

