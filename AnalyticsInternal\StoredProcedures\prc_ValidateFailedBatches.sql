/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 349006BE2E1B24C7162170A11988A36122B5999C
--------------------------------------------------------------------
-- Update uncorrected failed batches with rework batch information,
-- marked them as corrected as appropriate
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_ValidateFailedBatches
    @partitionId    INT,
    @batchId        BIGINT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    CREATE TABLE #FailedBatch
    (
        BatchId BIGINT NOT NULL PRIMARY KEY,
        TableName VARCHAR(64) COLLATE DATABASE_DEFAULT NOT NULL,
        Operation VARCHAR(10) COLLATE DATABASE_DEFAULT NOT NULL,
        OperationSproc VARCHAR(100) COLLATE DATABASE_DEFAULT NULL,
        Ready BIT NOT NULL,
        Failed BIT NOT NULL,
        OperationTriggerTableName VARCHAR(64) COLLATE DATABASE_DEFAULT NULL,
        OperationSprocVersion INT NULL,
        OperationTriggerBatchIdStart BIGINT NULL,
        OperationTriggerBatchIdEnd BIGINT NULL,
        ReworkBatchId BIGINT NULL
    )

    IF (@batchId IS NULL)
    BEGIN
        INSERT  #Failedbatch
        SELECT  BatchId,
                TableName,
                Operation,
                OperationSproc,
                Ready,
                Failed,
                OperationTriggerTableName,
                OperationSprocVersion,
                OperationTriggerBatchIdStart,
                OperationTriggerBatchIdEnd,
                ReworkBatchId
        FROM    AnalyticsInternal.tbl_Batch WITH (READPAST, ROWLOCK)
        WHERE   PartitionId = @partitionId
                AND OperationTriggerTableName IS NOT NULL
                AND Failed = 1
                AND ReworkBatchId IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        INSERT  #Failedbatch
        SELECT  BatchId,
                TableName,
                Operation,
                OperationSproc,
                Ready,
                Failed,
                OperationTriggerTableName,
                OperationSprocVersion,
                OperationTriggerBatchIdStart,
                OperationTriggerBatchIdEnd,
                ReworkBatchId
        FROM    AnalyticsInternal.tbl_Batch WITH (READPAST, ROWLOCK)
        WHERE   PartitionId = @partitionId
                AND OperationTriggerTableName IS NOT NULL
                AND Failed = 1
                AND ReworkBatchId IS NULL
                AND BatchId = @batchId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    IF (@@ROWCOUNT > 0)
    BEGIN
        -- reprocess of the same sproc covering all trigger batch ids
        UPDATE  fb
        SET     ReworkBatchId = rb.BatchId
        FROM    #Failedbatch fb
        INNER HASH JOIN AnalyticsInternal.tbl_Batch rb WITH (READPAST)
        ON      rb.PartitionId = @partitionId
                AND rb.OperationTriggerTableName = fb.OperationTriggerTableName
                AND rb.OperationSproc = fb.OperationSproc
                AND rb.OperationTriggerBatchIdStart <= fb.OperationTriggerBatchIdStart
                AND rb.OperationTriggerBatchIdEnd >= fb.OperationTriggerBatchIdEnd
                AND rb.BatchId > fb.BatchId
                AND rb.Ready = 1
        WHERE   fb.ReworkBatchId IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- a full process of the target table
        UPDATE  fb
        SET     ReworkBatchId = rb.BatchId
        FROM    #Failedbatch fb
        INNER HASH JOIN AnalyticsInternal.tbl_Batch rb WITH (READPAST)
        ON      rb.PartitionId = @partitionId
                AND rb.TableName = fb.TableName
                AND rb.Operation IN ('merge', 'replace')
                AND rb.OperationTriggerBatchIdStart <= 1
                AND rb.OperationTriggerBatchIdEnd >= fb.OperationTriggerBatchIdEnd
                AND rb.BatchId >= fb.BatchId
                AND rb.Ready = 1
        WHERE   fb.ReworkBatchId IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- transform def is obsolete
        UPDATE  fb
        SET     ReworkBatchId = 0
        FROM    #Failedbatch fb
        LEFT JOIN AnalyticsInternal.tbl_TransformDefinition d
        ON      d.SprocName = fb.OperationSproc
                AND d.TriggerTableName = fb.OperationTriggerTableName
        WHERE   fb.ReworkBatchId IS NULL
                AND d.SprocName IS NULL

        -- push the results back into the batches table
        UPDATE  b
        SET     ReworkBatchId = fb.ReworkBatchId
        FROM    AnalyticsInternal.tbl_Batch b WITH (ROWLOCK)
        JOIN    #Failedbatch fb
        ON      b.PartitionId = @partitionId
                AND b.BatchId = fb.BatchId
        WHERE   fb.ReworkBatchId IS NOT NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    DROP TABLE #FailedBatch
END

GO

