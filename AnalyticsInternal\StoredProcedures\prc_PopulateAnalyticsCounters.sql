/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B9C77494100647AB9D596A7D12EF2AF403705582
CREATE PROCEDURE AnalyticsInternal.prc_PopulateAnalyticsCounters
    @partitionId        INT
AS
BEGIN
    SET NOCOUNT ON

    DECLARE @status INT
    DECLARE @tfError   NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @counterValue BIGINT

    -- batchid counter
    SELECT  @counterValue = MAX(BatchId)
    FROM    AnalyticsInternal.tbl_Batch
    WHERE   BatchId > 0 -- there are possibly negative batch ids
            AND PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @counterValue = ISNULL(@counterValue, 0) + 1

    EXEC @status = prc_iCounterPopulate @partitionId, 'AnalyticsBatchIdCounter', @counterValue

    IF (@status <> 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670004); RAISERROR(@tfError, 16, -1, @procedureName, 'AnalyticsBatchIdCounter', @status)
        RETURN 167004
    END

    -- WorkItemRevisionSK counter
    SELECT  @counterValue = MAX(WorkItemRevisionSK)
    FROM    AnalyticsInternal.vw_WorkItemRevisionSK
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @counterValue = ISNULL(@counterValue, 0) + 1

    EXEC @status = prc_iCounterPopulate @partitionId, 'AnalyticsWorkItemRevisionSKCounter', @counterValue

    IF (@status <> 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670004); RAISERROR(@tfError, 16, -1, @procedureName, 'AnalyticsWorkItemRevisionSKCounter', @status)
        RETURN 167004
    END

    RETURN 0
END

GO

