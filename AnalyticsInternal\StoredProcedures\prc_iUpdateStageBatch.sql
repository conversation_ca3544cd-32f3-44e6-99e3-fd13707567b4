/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 9D17771D5CCA1B266ADF114BA4113CE02D1C589C
CREATE PROCEDURE AnalyticsInternal.prc_iUpdateStageBatch
    @partitionId    INT,
    @batchId BIGINT,
    @operationActive BIT,
    @ready BIT,
    @failed BIT,
    @incInsertedCount INT,
    @incUpdatedCount INT,
    @incDeletedCount INT,
    @tableLoading BIT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT

    EXEC @status = AnalyticsInternal.prc_iUpdateBatch
        @partitionId = @partitionId,
        @batchId = @batchId,
        @operationState = null,
        @operationStateData = null,
        @operationActive = @operationActive,
        @ready = @ready,
        @failed = @failed,
        @incFailedCount = @failed,
        @failedMessage = NULL,
        @attemptCount = 1,
        @incInsertedCount = @incInsertedCount,
        @incUpdatedCount = @incUpdatedCount,
        @incDeletedCount = @incDeletedCount,
        @incOperationSubBatchCount = null,
        @incOperationDurationMS = null,
        @tableLoading = @tableLoading

    RETURN @status
END

GO

