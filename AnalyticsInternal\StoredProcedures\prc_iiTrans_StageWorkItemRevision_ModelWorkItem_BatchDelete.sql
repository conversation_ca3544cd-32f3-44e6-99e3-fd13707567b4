/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B8F70DF3E22206853302E05BE362D295AAAFE415
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemRevision_ModelWorkItem_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @deleted TABLE
    (
        WorkItemRevisionSK BIGINT,
        WorkItemId INT,
        Revision INT
    )

    CREATE TABLE #DeletedWorkItemRevision (WorkItemId INT, Revision INT)

    INSERT  #DeletedWorkItemRevision
    SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev
    FROM    AnalyticsStage.tbl_WorkItemRevision_Deleted d
    WHERE   PartitionId = @partitionId
            AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND d.System_Id > ISNULL(@stateData, -1)
    ORDER BY System_Id ASC
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)
    SET @endStateData = (SELECT MAX(WorkItemId) FROM #DeletedWorkItemRevision)

    -- make sure it's still gone from stage
    DELETE  t
    FROM    #DeletedWorkItemRevision t
    JOIN    AnalyticsStage.tbl_WorkItemRevision s
    ON      s.System_Id = t.WorkItemId
            AND s.System_Rev = t.Revision
    WHERE   s.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    OUTPUT  DELETED.WorkItemRevisionSK, DELETED.WorkItemId, DELETED.Revision INTO @deleted
    FROM    AnalyticsModel.tbl_WorkItem t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem)) -- force use of CCI
    JOIN    #DeletedWorkItemRevision d
    ON      d.WorkItemId = t.WorkItemId
            AND d.Revision = t.Revision
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DELETE  t
    OUTPUT  DELETED.WorkItemRevisionSK, DELETED.WorkItemId, DELETED.Revision INTO @deleted
    FROM    AnalyticsModel.tbl_WorkItemHistory t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory)) -- force use of CCI
    JOIN    #DeletedWorkItemRevision d
    ON      d.WorkItemId = t.WorkItemId
            AND d.Revision = t.Revision
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount += @@ROWCOUNT

    INSERT AnalyticsModel.tbl_WorkItem_Deleted
    (
        PartitionId,
        AnalyticsBatchIdDeleted,
        AnalyticsDeletedDate,
        WorkItemRevisionSK
    )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            WorkItemRevisionSK
    FROM    @deleted

    -- make sure IsCurrent and IsDeleted are still correctly set for any work items with deleted revisions
    CREATE TABLE #WorkItemRevsAll
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        PrevIsCurrent BIT NOT NULL,
        PrevIsDeleted BIT NOT NULL,
        IsCurrent BIT NOT NULL,
        IsDeleted BIT NOT NULL,
        ExistingCurrent BIT NOT NULL,
        ExistingHistory BIT NOT NULL
    )
    CREATE CLUSTERED INDEX CI_WorkItemRevsAll ON #WorkItemRevsAll (WorkItemId, Revision)

    INSERT  #WorkItemRevsAll (WorkItemId, Revision, PrevIsCurrent, PrevIsDeleted, IsCurrent, IsDeleted, ExistingCurrent, ExistingHistory)
    SELECT  t.WorkItemId,
            t.Revision,
            1 AS IsCurrent,
            t.IsDeleted,
            1 AS IsCurrent,
            t.IsDeleted,
            1 AS ExistingCurrent,
            0 AS ExistingHistory
    FROM    AnalyticsModel.tbl_WorkItem t
    JOIN    (SELECT DISTINCT WorkItemId FROM @deleted) tid
    ON      tid.WorkItemId = t.WorkItemId
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  #WorkItemRevsAll (WorkItemId, Revision, PrevIsCurrent, PrevIsDeleted, IsCurrent, IsDeleted, ExistingCurrent, ExistingHistory)
    SELECT  t.WorkItemId,
            t.Revision,
            0 AS IsCurrent,
            t.IsDeleted,
            0 AS IsCurrent,
            t.IsDeleted,
            0 AS ExistingCurrent,
            1 AS ExistingHistory
    FROM    AnalyticsModel.tbl_WorkItemHistory t
    JOIN    (SELECT DISTINCT WorkItemId FROM @deleted) tid
    ON      tid.WorkItemId = t.WorkItemId
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    UPDATE  t
    SET     IsDeleted = wilast.IsDeleted,
            IsCurrent = IIF(t.Revision = wilast.Revision, 1, 0)
    FROM    #WorkItemRevsAll t
    CROSS APPLY
    (
        SELECT  TOP 1 wiall.Revision, wiall.IsDeleted
        FROM    #WorkItemRevsAll wiall
        WHERE   wiall.WorkItemId = t.WorkItemId
        ORDER BY wiall.Revision DESC
    ) wilast

    DELETE  s
    FROM    #WorkItemRevsAll AS s
    WHERE   IsCurrent = PrevIsCurrent
            AND IsDeleted = PrevIsDeleted

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            IsDeleted = s.IsDeleted
    FROM    #WorkItemRevsAll s
    INNER LOOP JOIN    AnalyticsModel.tbl_WorkItem t
    ON      @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount += @@ROWCOUNT

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            IsDeleted = s.IsDeleted
    FROM    #WorkItemRevsAll s
    INNER LOOP JOIN    AnalyticsModel.tbl_WorkItemHistory t
    ON      @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount += @@ROWCOUNT

    -- if IsCurrent changes, we may need to move a row from WorkItemHistory to WorkItem
    DELETE  s
    FROM    #WorkItemRevsAll AS s
    WHERE   ExistingHistory = 0
            OR IsCurrent = 0

    INSERT AnalyticsModel.tbl_WorkItem
    (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        WorkItemRevisionSK,
        WorkItemId,
        Revision,
        Watermark,
        Title,
        WorkItemType,
        ChangedDate,
        CreatedDate,
        [State],
        Reason,
        FoundIn,
        IntegrationBuild,
        ActivatedDate,
        Activity,
        BacklogPriority,
        BusinessValue,
        ClosedDate,
        Discipline,
        Issue,
        Priority,
        Rating,
        ResolvedDate,
        ResolvedReason,
        Risk,
        Severity,
        StackRank,
        TimeCriticality,
        Triage,
        ValueArea,
        DueDate,
        FinishDate,
        StartDate,
        TargetDate,
        Blocked,
        Committed,
        Escalate,
        FoundInEnvironment,
        HowFound,
        Probability,
        RequirementType,
        RequiresReview,
        RequiresTest,
        RootCause,
        SubjectMatterExpert1,
        SubjectMatterExpert2,
        SubjectMatterExpert3,
        TargetResolveDate,
        TaskType,
        UserAcceptanceTest,
        IsDeleted,
        AutomatedTestId,
        AutomatedTestName,
        AutomatedTestStorage,
        AutomatedTestType,
        AutomationStatus,
        ProjectSK,
        DateSK,
        AreaSK,
        IterationSK,
        CompletedWork,
        Effort,
        OriginalEstimate,
        RemainingWork,
        Size,
        StoryPoints,
        CreatedDateSK,
        ActivatedDateSK,
        ClosedDateSK,
        ResolvedDateSK,
        AssignedToUserSK,
        ChangedByUserSK,
        CreatedByUserSK,
        ActivatedByUserSK,
        ClosedByUserSK,
        ResolvedByUserSK,
        ParentWorkItemId,
        TagNames,
        StateCategory,
        InProgressDate,
        InProgressDateSK,
        CompletedDate,
        CompletedDateSK,
        LeadTimeDays,
        CycleTimeDays,
        InternalForSnapshotHashJoin,
        AuthorizedDate,
        StateChangeDate,
        StateChangeDateSK
    )
    SELECT
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.WorkItemRevisionSK,
        s.WorkItemId,
        s.Revision,
        s.Watermark,
        s.Title,
        s.WorkItemType,
        s.ChangedDate,
        s.CreatedDate,
        s.[State],
        s.Reason,
        s.FoundIn,
        s.IntegrationBuild,
        s.ActivatedDate,
        s.Activity,
        s.BacklogPriority,
        s.BusinessValue,
        s.ClosedDate,
        s.Discipline,
        s.Issue,
        s.Priority,
        s.Rating,
        s.ResolvedDate,
        s.ResolvedReason,
        s.Risk,
        s.Severity,
        s.StackRank,
        s.TimeCriticality,
        s.Triage,
        s.ValueArea,
        s.DueDate,
        s.FinishDate,
        s.StartDate,
        s.TargetDate,
        s.Blocked,
        s.Committed,
        s.Escalate,
        s.FoundInEnvironment,
        s.HowFound,
        s.Probability,
        s.RequirementType,
        s.RequiresReview,
        s.RequiresTest,
        s.RootCause,
        s.SubjectMatterExpert1,
        s.SubjectMatterExpert2,
        s.SubjectMatterExpert3,
        s.TargetResolveDate,
        s.TaskType,
        s.UserAcceptanceTest,
        s.IsDeleted,
        s.AutomatedTestId,
        s.AutomatedTestName,
        s.AutomatedTestStorage,
        s.AutomatedTestType,
        s.AutomationStatus,
        s.ProjectSK,
        s.DateSK,
        s.AreaSK,
        s.IterationSK,
        s.CompletedWork,
        s.Effort,
        s.OriginalEstimate,
        s.RemainingWork,
        s.Size,
        s.StoryPoints,
        s.CreatedDateSK,
        s.ActivatedDateSK,
        s.ClosedDateSK,
        s.ResolvedDateSK,
        s.AssignedToUserSK,
        s.ChangedByUserSK,
        s.CreatedByUserSK,
        s.ActivatedByUserSK,
        s.ClosedByUserSK,
        s.ResolvedByUserSK,
        s.ParentWorkItemId,
        s.TagNames,
        s.StateCategory,
        s.InProgressDate,
        s.InProgressDateSK,
        s.CompletedDate,
        s.CompletedDateSK,
        s.LeadTimeDays,
        s.CycleTimeDays,
        1 AS InternalForSnapshotHashJoin,
        s.AuthorizedDate,
        s.StateChangeDate,
        s.StateChangeDateSK
    FROM    #WorkItemRevsAll a
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemHistory s
    ON      @partitionId = s.PartitionId
            AND a.WorkItemId = s.WorkItemId
            AND a.Revision = s.Revision
    WHERE   a.ExistingHistory = 1
            AND a.IsCurrent = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    FROM    #WorkItemRevsAll a
    INNER LOOP JOIN AnalyticsModel.tbl_WorkItemHistory t
    ON      @partitionId = t.PartitionId
            AND a.WorkItemId = t.WorkItemId
            AND a.Revision = t.Revision
    WHERE   a.ExistingHistory = 1
            AND a.IsCurrent = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- Delete from custom tables
    DECLARE @CustomTables TABLE
    (
        ModelTableName NVARCHAR(50)
    )

    INSERT INTO @CustomTables
    SELECT DISTINCT ModelTableName
    FROM    AnalyticsInternal.tbl_ProcessField
    WHERE   PartitionId = @partitionId
            AND ModelTableName IS NOT NULL
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- Iterate via tables and fill them (without cursors)
    WHILE EXISTS(SELECT * FROM @CustomTables)
    BEGIN
        DECLARE @CustomTableName NVARCHAR(50)
        DECLARE @SchemaName NVARCHAR(20) = 'AnalyticsModel'

        SELECT TOP(1) @CustomTableName = ModelTableName FROM @CustomTables

        DECLARE @deletedCustomRows INT

        DECLARE @cmd NVARCHAR(MAX) = '
        DELETE  r
        FROM    <TABLE_NAME> r
        JOIN    #DeletedWorkItemRevision d
        ON      r.WorkItemId = d.WorkItemId
                AND r.Revision = d.Revision
        WHERE   r.PartitionId = @partitionId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
        '

        SET @cmd = REPLACE(@cmd,'<TABLE_NAME>', QUOTENAME(@SchemaName) + '.' +QUOTENAME('tbl_' + @CustomTableName))

        PRINT @cmd

        EXEC sp_executesql @cmd,
            N'@partitionId INT',
            @partitionId = @partitionId

        DELETE FROM @CustomTables WHERE @CustomTableName = ModelTableName
    END

    DROP TABLE #DeletedWorkItemRevision
    DROP TABLE #WorkItemRevsAll

    RETURN 0
END

GO

