/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D4794B2CEC6E0C52ABF6DD0B5899039A01CEF9DF
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_InternalWorkItemRevisionReserved_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 50000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    SET @insertedCount = @@ROWCOUNT
    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)

    CREATE TABLE #Src
    (
        WorkItemId          INT NOT NULL,
        Revision            INT NOT NULL,
        ChangedDate         DATETIMEOFFSET,
        WorkItemRevisionSK  INT,
        PRIMARY KEY CLUSTERED (WorkItemId, Revision)
    )

    IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
    BEGIN
        INSERT  #Src (WorkItemId, Revision, ChangedDate)
        SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev, System_ChangedDate
        FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
        WHERE   wi.PartitionId = @partitionId
                AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND wi.System_Id >= @workItemIdStart
                AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
        ORDER BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @endState = IIF(@@ROWCOUNT >= @batchSizeMax, 'dense', '')
        SET @endStateData = ISNULL((SELECT MAX(WorkItemId) FROM #Src), @stateData)
        SET @complete = 0
    END
    ELSE -- use NCI
    BEGIN
        INSERT  #Src (WorkItemId, Revision, ChangedDate)
        SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev, System_ChangedDate
        FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
        WHERE   wi.PartitionId = @partitionId
                AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND wi.System_Id >= @workItemIdStart
        ORDER BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

        SET @complete = IIF(@@ROWCOUNT >= @batchSizeMax, 0, 1)
        SET @endStateData = ISNULL((SELECT MAX(WorkItemId) FROM #Src), @stateData)
        SET @endState = IIF(@complete = 0 AND @endStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
    END

    DELETE  s
    FROM    #Src s
    JOIN    AnalyticsInternal.vw_WorkItemRevisionSK e
    ON      e.PartitionId = @partitionId
            AND e.WorkItemId = s.WorkItemId
            AND e.Revision = s.Revision
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE @toInsertCount INT
    SELECT  @toInsertCount = COUNT(*)
    FROM    #Src

    IF (@toInsertCount > 0)
    BEGIN
        DECLARE @newWorkItemRevisionSK INT
        EXEC @status = prc_iCounterGetNext @partitionId = @partitionId, @counterName = 'AnalyticsWorkItemRevisionSKCounter', @countToReserve = @toInsertCount, @firstIdToUse = @newWorkItemRevisionSK OUTPUT
        IF (@status <> 0)
        BEGIN
            SET @tfError = dbo.func_GetMessage(1670005); RAISERROR(@tfError, 16, -1, @procedureName, 'AnalyticsWorkItemRevisionSKCounter', @status)
            RETURN 1670005
        END

        ;WITH ToInsert AS
        (
            SELECT  *, @newWorkItemRevisionSK + (ROW_NUMBER() OVER (ORDER BY ChangedDate) - 1) AS NewWorkItemRevisionSK
            FROM    #Src
        )
        UPDATE  t
        SET     WorkItemRevisionSK = NewWorkItemRevisionSK
        FROM    ToInsert t

        INSERT  AnalyticsInternal.tbl_WorkItemRevisionReserved WITH (ROWLOCK)
        (
            PartitionId,
            AnalyticsCreatedDate,
            AnalyticsBatchId,
            WorkItemRevisionSK,
            WorkItemId,
            Revision
        )
        SELECT  @partitionId,
                @batchDt,
                @batchId,
                WorkItemRevisionSK,
                WorkItemId,
                Revision
        FROM    #Src
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @insertedCount = @@ROWCOUNT
    END

    DROP TABLE #Src

    RETURN 0
END

GO

