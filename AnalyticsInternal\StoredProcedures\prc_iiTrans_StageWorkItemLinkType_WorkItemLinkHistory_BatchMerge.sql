/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: E9E2E7201D5CB817EDED27396C7A1B2D1A0F27DA
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemLinkType_WorkItemLinkHistory_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @localizedUnknown   NVARCHAR(230)

    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_UNKNOWN',
                                                            @defaultValue = 'Unknown',
                                                            @localizedValue =  @localizedUnknown OUTPUT

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @subBatchSourceWorkItemIdStart BIGINT
    DECLARE @subBatchSourceWorkItemIdEnd BIGINT
    SET @subBatchSourceWorkItemIdStart = ISNULL(@stateData + 1, 0)

    SELECT @subBatchSourceWorkItemIdEnd = ISNULL(MAX(SourceWorkItemId), 0)
    FROM (
        SELECT      TOP (@batchSizeMax) SourceWorkItemId
        FROM        AnalyticsModel.tbl_WorkItemLinkHistory
        WHERE       PartitionId = @partitionId AND SourceWorkItemId >= @subBatchSourceWorkItemIdStart
        ORDER BY    SourceWorkItemId
    ) T
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @subBatchSourceWorkItemIdStart = 0))

    SET @endStateData = @subBatchSourceWorkItemIdEnd

    DECLARE @maxWorkItemId INT

    SELECT  @maxWorkItemId = MAX(SourceWorkItemId)
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@subBatchSourceWorkItemIdEnd >= ISNULL(@maxWorkItemId, 0), 1, 0)

    CREATE TABLE #SourceWorkItemId (
        SourceWorkItemId INT PRIMARY KEY
    )

    INSERT  #SourceWorkItemId
    SELECT  DISTINCT t.SourceWorkItemId
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory AS t
    WHERE   t.partitionId = @partitionId
            AND t.SourceWorkItemId BETWEEN @subBatchSourceWorkItemIdStart AND @subBatchSourceWorkItemIdEnd

    -- Fwd Links
    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            LinkTypeReferenceName = ISNULL(s.ReferenceName + '-Forward', 'Unknown'),
            LinkTypeName = ISNULL(s.ForwardName, @localizedUnknown),
            LinkTypeIsAcyclic = s.IsAcyclic,
            LinkTypeIsDirectional = s.IsDirectional
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory t
    JOIN    #SourceWorkItemId AS i
    ON      i.SourceWorkItemId = t.SourceWorkItemId
    JOIN    AnalyticsStage.tbl_WorkItemLinkType AS s
    ON      t.PartitionId = s.PartitionId
            AND t.LinkTypeId = s.LinkTypeId
    WHERE   t.partitionId = @partitionId
            AND NOT EXISTS
                (
                SELECT  t.LinkTypeReferenceName,
                        t.LinkTypeName,
                        t.LinkTypeIsAcyclic,
                        t.LinkTypeIsDirectional
                INTERSECT
                SELECT  ISNULL(s.ReferenceName + '-Forward', 'Unknown'),
                        ISNULL(s.ForwardName, @localizedUnknown),
                        s.IsAcyclic,
                        s.IsDirectional
                )
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    -- Rev Link
    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            LinkTypeReferenceName = ISNULL(s.ReferenceName + '-Reverse', 'Unknown'),
            LinkTypeName = ISNULL(s.ReverseName, @localizedUnknown),
            LinkTypeIsAcyclic = s.IsAcyclic,
            LinkTypeIsDirectional = s.IsDirectional
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory t
    JOIN    #SourceWorkItemId AS i
    ON      i.SourceWorkItemId = t.SourceWorkItemId
    JOIN    AnalyticsStage.tbl_WorkItemLinkType AS s
    ON      t.PartitionId = s.PartitionId
            AND t.LinkTypeId = -1 * s.LinkTypeId
    WHERE   t.partitionId = @partitionId
            AND NOT EXISTS
                (
                SELECT  t.LinkTypeReferenceName,
                        t.LinkTypeName,
                        t.LinkTypeIsAcyclic,
                        t.LinkTypeIsDirectional
                INTERSECT
                SELECT  ISNULL(s.ReferenceName + '-Reverse', 'Unknown'),
                        ISNULL(s.ReverseName, @localizedUnknown),
                        s.IsAcyclic,
                        s.IsDirectional
                )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @updatedCount + @@ROWCOUNT

    -- check for any new duplicate parent links due to possible typerefname changes
    -- note this sproc will not recreate links previously deduped - extrememly unlikely
    SET @deletedCount = 0

    IF (@updatedCount > 0)
    BEGIN
        -- Dedupe Fwd
        DELETE  l
        FROM    AnalyticsModel.tbl_WorkItemLinkHistory l
        JOIN    #SourceWorkItemId AS i
        ON      i.SourceWorkItemId = l.SourceWorkItemId
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory lp
        ON      lp.PartitionId = l.PartitionId
                AND lp.TargetWorkItemId = l.TargetWorkItemId
                AND lp.SourceWorkItemId != l.SourceWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
                AND lp.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
                AND lp.CreatedDate < l.DeletedDate
                AND lp.DeletedDate > l.CreatedDate
                AND ((lp.DeletedDate > l.DeletedDate)
                      OR (lp.DeletedDate = l.DeletedDate
                          AND lp.CreatedDate > l.CreatedDate
                         )
                      OR (lp.DeletedDate = l.DeletedDate
                          AND lp.CreatedDate = l.CreatedDate
                          AND lp.SourceWorkItemId > l.SourceWorkItemId
                         )
                    )
        WHERE   l.partitionId = @partitionId

        SET @deletedCount += @@ROWCOUNT

        -- Dedupe Rev
        DELETE  l
        FROM    AnalyticsModel.tbl_WorkItemLinkHistory l
        JOIN    #SourceWorkItemId AS i
        ON      i.SourceWorkItemId = l.SourceWorkItemId
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory lp
        ON      lp.PartitionId = l.PartitionId
                AND lp.TargetWorkItemId = l.TargetWorkItemId
                AND lp.SourceWorkItemId != l.SourceWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
                AND lp.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
                AND lp.CreatedDate < l.DeletedDate
                AND lp.DeletedDate > l.CreatedDate
                AND ((lp.DeletedDate > l.DeletedDate)
                      OR (lp.DeletedDate = l.DeletedDate
                          AND lp.CreatedDate > l.CreatedDate
                         )
                      OR (lp.DeletedDate = l.DeletedDate
                          AND lp.CreatedDate = l.CreatedDate
                          AND lp.SourceWorkItemId > l.SourceWorkItemId
                         )
                    )
        WHERE   l.PartitionId = @partitionId

        SET @deletedCount = @deletedCount + @@ROWCOUNT
    END

    DROP TABLE #SourceWorkItemId

    RETURN 0
END

GO

