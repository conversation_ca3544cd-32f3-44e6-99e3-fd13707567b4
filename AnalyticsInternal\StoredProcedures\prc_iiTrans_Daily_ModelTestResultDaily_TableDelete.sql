/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 83FA0816AF712B56109CA30824E5B51CC04415C4
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Daily_ModelTestResultDaily_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 102400) -- default size should use CCI bulk operation
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    --Get the retention date
    DECLARE @retentionDays INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'Target.RetentionDays'), 30)
    DECLARE @retentionDateSK INT = AnalyticsInternal.func_GenDateSK(DATEADD(day, 0 - @retentionDays, GETUTCDATE() AT TIME ZONE @timeZone))

    DECLARE @noWork BIT = 0
    SET @deletedCount = 0

    -- optimization - use SELECT on first subbatch to see if any work exists
    -- this prevents accounts not using test from applying locks on the table
    IF (@subBatchCount = 1)
    BEGIN
        SET @noWork = IIF(EXISTS (
                    SELECT *
                    FROM    AnalyticsModel.tbl_TestResultDaily t WITH (INDEX (CL_AnalyticsModel_tbl_TestResultDaily))
                    WHERE   t.PartitionId = @partitionId
                            AND DateSK < @retentionDateSK
                    ), 0, 1)
    END

    IF (@noWork = 0)
    BEGIN
        -- forcing CCI on DELETE to avoid using potentially fragmented mapping index via NCI
        DELETE  TOP (@batchSizeMax) t
        FROM    AnalyticsModel.tbl_TestResultDaily t WITH (INDEX (CL_AnalyticsModel_tbl_TestResultDaily))
        WHERE   t.PartitionId = @partitionId
                AND DateSK < @retentionDateSK
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @deletedCount = @@ROWCOUNT
    END

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    RETURN 0
END

GO

