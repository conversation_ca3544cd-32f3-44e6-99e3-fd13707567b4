/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 773C19C5C7258A12CB580404C71A487211A16564
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Any_ModelPipelineJob_Stage_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    -- Populate PipelineJobTable
    CREATE TABLE #TimelineRecordHierarchy
    (
        ProjectSK                       UNIQUEIDENTIFIER    NULL,
        BuildPipelineId                 INT                 NULL,
        StageIdentifier                 NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        StageName                       NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
        INDEX IX_#TimelineRecordHierarchy_BuildPipelineId_StageIdentifier(BuildPipelineId, StageIdentifier)
    )

    IF (@triggerTableName = 'Build' OR @triggerTableName = 'TaskPlan')
    BEGIN
        CREATE TABLE #ImpactedBuild
        (
            ProjectSK        UNIQUEIDENTIFIER NOT NULL,
            BuildId          INT              NOT NULL,
            PlanId           INT              NOT NULL,
        )

        IF (@triggerTableName = 'Build')
        BEGIN
            INSERT  #ImpactedBuild (ProjectSK, BuildId, PlanId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    sb.ProjectGuid,
                    sb.BuildId,
                    stp.PlanId
            FROM    AnalyticsStage.tbl_Build sb
            JOIN    AnalyticsStage.tbl_TaskPlan stp
            ON      stp.PartitionId = @partitionId
                    AND stp.ProjectGuid = sb.ProjectGuid
                    AND stp.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                    AND stp.PlanGuid = sb.PlanId
            WHERE   sb.PartitionId = @partitionId
                    AND sb.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND sb.BuildId > ISNULL(@stateData, -1)
            ORDER BY sb.BuildId
            OPTION (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(BuildId) FROM #ImpactedBuild)
        END
        ELSE IF (@triggerTableName = 'TaskPlan')
        BEGIN
            INSERT  #ImpactedBuild (ProjectSK, BuildId, PlanId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    stp.ProjectGuid,
                    sb.BuildId,
                    stp.PlanId
            FROM    AnalyticsStage.tbl_TaskPlan stp
            JOIN    AnalyticsStage.tbl_Build sb
            ON      sb.PartitionId = @partitionId
                    AND sb.ProjectGuid = stp.ProjectGuid
                    AND sb.PlanId = stp.PlanGuid
            WHERE   stp.PartitionId = @partitionId
                    AND stp.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                    AND stp.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND stp.PlanId > ISNULL(@stateData, -1)
            ORDER BY stp.PlanId
            OPTION (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(PlanId) FROM #ImpactedBuild)
        END

        -- if we change this also ensure you change query in else part as well.
        INSERT #TimelineRecordHierarchy(
            ProjectSK,
            BuildPipelineId,
            StageIdentifier,
            StageName
         )
        SELECT DISTINCT sttr.ProjectGuid,
               sb.DefinitionId,
               sttr.StageIdentifier,
               -- need same logic  while  filling JobSk
               IIF(sttr.StageName IS NULL, '_generatedDefault', IIF(sttr.StageName != '', sttr.StageName, '_generatedDefaultForEmpty' + '.' + sttr.StageIdentifier))
        FROM   #ImpactedBuild ib
        JOIN   AnalyticsStage.tbl_TaskTimelineRecord sttr WITH (INDEX(CI_tbl_TaskTimelineRecord))
        ON     sttr.PartitionId = @partitionId
               AND sttr.ProjectGuid = ib.ProjectSK
               AND sttr.PipelineType = 'Build'
               AND sttr.PlanId = ib.PlanId
               AND sttr.[Type] IN ('Task', 'Job')
        JOIN   AnalyticsStage.tbl_TaskPlan stp  WITH (INDEX(CI_tbl_TaskPlan))
        ON     stp.PartitionId = @partitionId
               AND stp.ProjectGuid = ib.ProjectSK
               AND stp.PipelineType = 'Build'
               AND stp.PlanId = ib.PlanId
        JOIN   AnalyticsStage.tbl_Build sb  WITH (INDEX(IX_tbl_Build_PlanId))
        ON     sb.PartitionId = @partitionId
               AND sb.PlanId = stp.PlanGuid
        WHERE  sttr.PartitionId = @partitionId
               AND (   -- this is to handle not to add Jobs for the old null entries.
                        sttr.StageIdentifier IS NOT NULL
                        OR sttr.PhaseIdentifier IS NOT NULL
                        OR sttr.JobIdentifier   IS NOT NULL
                    )
               AND sttr.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
               AND stp.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
               AND sb.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
        OPTION (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN));

        DROP TABLE #ImpactedBuild
    END
    ELSE IF (@triggerTableName = 'TaskTimelineRecord' OR @triggerTableName = 'TaskDefinitionReference')
    BEGIN
        CREATE TABLE #ImpactedTimelineRecord
        (   ProjectSK                UNIQUEIDENTIFIER NOT NULL,
            PipelineType             NVARCHAR(260)    COLLATE DATABASE_DEFAULT NOT NULL,
            PlanId                   INT              NOT NULL,
            TimelineId               INT              NOT NULL,
            TimelineRecordId         UNIQUEIDENTIFIER NOT NULL
        )

        IF (@triggerTableName = 'TaskTimelineRecord')
        BEGIN
            IF (@triggerBatchIdStart > 1 AND @stateData IS NULL) -- use batchID changed index for first batches
            BEGIN
                INSERT  #ImpactedTimelineRecord (ProjectSK, PipelineType, PlanId, TimelineId, TimelineRecordId)
                SELECT  TOP(@batchSizeMax) WITH TIES
                        ProjectGuid,
                        PipelineType,
                        PlanId,
                        TimelineId,
                        TimelineRecordGuid
                FROM    AnalyticsStage.tbl_TaskTimelineRecord WITH (INDEX(IX_tbl_TaskTimelineRecord_AxBatchIdChanged))
                WHERE   PartitionId = @partitionId
                        AND PipelineType = 'Build'
                        AND [Type] IN ('Task', 'Job')
                        AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND PlanId > ISNULL(@stateData, -1)
                ORDER BY PlanId
                OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
            END
            ELSE --Otherwise use the clustered index that supports the sub-batching on PlanId
            BEGIN
                INSERT  #ImpactedTimelineRecord (ProjectSK, PipelineType, PlanId, TimelineId, TimelineRecordId)
                SELECT  TOP(@batchSizeMax) WITH TIES
                        ProjectGuid,
                        PipelineType,
                        PlanId,
                        TimelineId,
                        TimelineRecordGuid
                FROM    AnalyticsStage.tbl_TaskTimelineRecord WITH (INDEX(CI_tbl_TaskTimelineRecord))
                WHERE   PartitionId = @partitionId
                        AND PipelineType = 'Build'
                        AND [Type] IN ('Task', 'Job')
                        AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND PlanId > ISNULL(@stateData, -1)
                ORDER BY PlanId
                OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
            END
         END
         ELSE IF (@triggerTableName = 'TaskDefinitionReference')
         BEGIN
             IF (@triggerBatchIdStart > 1 AND @stateData IS NULL) -- use batchID changed index for first batches
             BEGIN
                INSERT  #ImpactedTimelineRecord (ProjectSK, PipelineType, PlanId, TimelineId, TimelineRecordId)
                SELECT  TOP(@batchSizeMax) WITH TIES
                        stdr.ProjectGuid,
                        sttr.PipelineType,
                        sttr.PlanId,
                        sttr.TimelineId,
                        sttr.TimelineRecordGuid
                FROM    AnalyticsStage.tbl_TaskDefinitionReference stdr  WITH (INDEX(IX_tbl_TaskDefinitionReference_AxBatchIdChanged))
                INNER LOOP JOIN    AnalyticsStage.tbl_TaskTimelineRecord sttr WITH (INDEX(IX_tbl_TaskTimelineRecord_TaskDefinitionReferenceId))
                ON      sttr.PartitionId = stdr.PartitionId
                        AND sttr.ProjectGuid = stdr.ProjectGuid
                        AND sttr.PipelineType = stdr.PipelineType
                        AND sttr.TaskDefinitionReferenceId = stdr.TaskDefinitionReferenceId
                WHERE   stdr.PartitionId = @partitionId
                        AND stdr.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                        AND stdr.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND sttr.PlanId > ISNULL(@stateData, -1)
                ORDER BY sttr.PlanId
                OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
            END
            ELSE
            BEGIN
            INSERT  #ImpactedTimelineRecord (ProjectSK, PipelineType, PlanId, TimelineId, TimelineRecordId)
                SELECT  TOP(@batchSizeMax) WITH TIES
                        stdr.ProjectGuid,
                        sttr.PipelineType,
                        sttr.PlanId,
                        sttr.TimelineId,
                        sttr.TimelineRecordGuid
                FROM    AnalyticsStage.tbl_TaskTimelineRecord sttr WITH (INDEX(CI_tbl_TaskTimelineRecord))
                INNER LOOP JOIN    AnalyticsStage.tbl_TaskDefinitionReference stdr  WITH (INDEX(CI_tbl_TaskDefinitionReference))
                ON      stdr.PartitionId = sttr.PartitionId
                        AND stdr.ProjectGuid = sttr.ProjectGuid
                        AND stdr.PipelineType = sttr.PipelineType
                        AND stdr.TaskDefinitionReferenceId = sttr.TaskDefinitionReferenceId
                WHERE   stdr.PartitionId = @partitionId
                        AND stdr.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                        AND stdr.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND sttr.PlanId > ISNULL(@stateData, -1)
                ORDER BY sttr.PlanId
                OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
            END
         END

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(PlanId) FROM #ImpactedTimelineRecord)

        INSERT #TimelineRecordHierarchy(
            ProjectSk,
            BuildPipelineId,
            StageIdentifier,
            StageName
         )
        SELECT DISTINCT sttr.ProjectGuid,
               sb.DefinitionId,
               sttr.StageIdentifier,
               -- need same logic  while  filling JobSk
               IIF(sttr.StageName IS NULL, '_generatedDefault', IIF(sttr.StageName != '', sttr.StageName, '_generatedDefaultForEmpty' + '.' + sttr.StageIdentifier))
         FROM   #ImpactedTimelineRecord trec
         JOIN   AnalyticsStage.tbl_TaskTimelineRecord sttr WITH (INDEX(CI_tbl_TaskTimelineRecord))
         ON     trec.ProjectSK = sttr.ProjectGuid
                AND trec.PipelineType = sttr.PipelineType
                AND trec.PlanId = sttr.PlanId
                AND trec.TimelineId = sttr.TimelineId
                AND trec.TimelineRecordId = sttr.TimelineRecordGuid
         JOIN   AnalyticsStage.tbl_TaskPlan stp
         ON     stp.PartitionId = @partitionId
                AND stp.ProjectGuid = trec.ProjectSK
                AND stp.PipelineType = trec.PipelineType
                AND stp.PlanId = trec.PlanId
         JOIN   AnalyticsStage.tbl_Build sb
         ON     sb.PartitionId = @partitionId
                AND sb.ProjectGuid = stp.ProjectGuid
                AND sb.PlanId = stp.PlanGuid
         WHERE  sttr.PartitionId = @partitionId
                AND (  -- this is to handle not to add Jobs for the old null entries.
                        sttr.StageIdentifier IS NOT NULL
                        OR sttr.PhaseIdentifier IS NOT NULL
                        OR sttr.JobIdentifier   IS NOT NULL
                    )
                AND sttr.AnalyticsBatchIdChanged <= @triggerBatchIdEnd  -- Adding check here  to avoid records picked by TaskDefinition reference.
         OPTION (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN));

         DROP TABLE #ImpactedTimelineRecord
    END;

    -- below CTE uses the index created on #TimelineRecordHierarchy
    WITH TimelineRecordHierarchyCTE AS
    (
       SELECT * , ROW_NUMBER() OVER (PARTITION BY BuildPipelineId, StageIdentifier ORDER BY BuildPipelineId, StageIdentifier) AS RowNumber
       FROM #TimelineRecordHierarchy
    )
    DELETE FROM TimelineRecordHierarchyCTE WHERE RowNumber > 1

    UPDATE t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            StageName = s.StageName
    FROM    #TimelineRecordHierarchy AS s
    JOIN    AnalyticsModel.tbl_PipelineJob AS t  WITH (INDEX(IX_tbl_PipelineJob_BuildPipelineId_StageIdentifier_PhaseIdentifier_JobIdentifier))
    ON      t.PartitionId = @partitionId
            AND t.BuildPipelineId = s.BuildPipelineId
            AND  EXISTS
            (
                SELECT
                t.StageIdentifier
                INTERSECT
                SELECT
                s.StageIdentifier
            )
    WHERE   NOT EXISTS
            (
                SELECT
                t.StageName
                INTERSECT
                SELECT
                s.StageName
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @updatedCount = @@ROWCOUNT
    DROP TABLE #TimelineRecordHierarchy
    RETURN 0
END

GO

