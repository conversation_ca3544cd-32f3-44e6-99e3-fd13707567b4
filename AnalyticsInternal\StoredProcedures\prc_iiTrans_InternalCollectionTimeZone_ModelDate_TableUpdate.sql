/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: E7E49D4B3ACC9B1670AC3305D411F5291F7A7A3F
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelDate_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    --this sproc doesn't do any batching because it updates small # of rows (if it is year 2030, it would update 7963 rows)
    UPDATE  d
    SET     d.[Date] = CAST([Date] AS DATETIME) AT TIME ZONE @timeZone,
            d.WeekStartingDate = CAST(WeekStartingDate AS DATETIME) AT TIME ZONE @timeZone,
            d.WeekEndingDate = CAST(WeekEndingDate AS DATETIME) AT TIME ZONE @timeZone
    FROM    AnalyticsModel.tbl_Date d
    WHERE   d.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @updatedCount = @@ROWCOUNT
    SET @complete = 1

    RETURN 0
END

GO

