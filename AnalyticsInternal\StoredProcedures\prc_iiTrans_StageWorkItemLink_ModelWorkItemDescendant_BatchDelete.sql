/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 5743571612209FC523C428E30F7B1816763493C2
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemLink_ModelWorkItemDescendant_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #TriggerWorkItemId
    (
         ChildWorkItemId     INT   NOT NULL PRIMARY KEY
    )

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)

    INSERT  #TriggerWorkItemId
    SELECT  TOP (@batchSizeMax)
            TargetId
    FROM    AnalyticsStage.tbl_WorkItemLink_Deleted l WITH (INDEX (IX_tbl_WorkItemLink_Deleted_AxBatchIdDeleted))
    INNER LOOP JOIN    AnalyticsStage.tbl_WorkItemLinkType lt
    ON      lt.PartitionId = l.PartitionId
            AND lt.LinkTypeId = l.LinkTypeId
    WHERE   l.PartitionId = @partitionId
            AND l.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND lt.ReferenceName = 'System.LinkTypes.Hierarchy'
            AND TargetId >= @workItemIdStart
    GROUP BY TargetId
    ORDER BY TargetId ASC
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #SrcDown
    (
        WorkItemId                      INT                 NOT NULL,
        DescendantWorkItemId            INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
    )

    -- recurse down to find descendants of the ChildWorkItemId
    ;WITH WorkItemParents AS
    (
        SELECT  p.ChildWorkItemId AS WorkItemId,
                p.ChildWorkItemId AS ChildWorkItemId,
                0 AS Depth
        FROM    #TriggerWorkItemId p

        UNION ALL

        SELECT  p.WorkItemId,
                l.TargetWorkItemId AS ChildWorkItemId,
                p.Depth + 1 AS Depth
        FROM    WorkItemParents p
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND p.ChildWorkItemId = l.SourceWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward' -- 'System.LinkTypes.Hierarchy-Forward'
                AND l.DeletedDate = '9999-01-01'
        WHERE   p.Depth < 50
    )
    INSERT  #SrcDown
    SELECT  WorkItemId,
            ChildWorkItemId AS DescendantWorkItemId,
            MIN(Depth) -- loop detection
    FROM    WorkItemParents
    GROUP BY WorkItemId,
            ChildWorkItemId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #SrcUp
    (
        AncestorWorkItemId              INT                 NOT NULL,
        WorkItemId                      INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
    )

    -- recurse up to find ancestors of the ChildWorkItemId
    ;WITH WorkItemChildren AS
    (
        SELECT  c.ChildWorkItemId AS WorkItemId,
                c.ChildWorkItemId AS ParentWorkItemId,
                0 AS Depth
        FROM    #TriggerWorkItemId c

        UNION ALL

        SELECT  c.WorkItemId,
                l.SourceWorkItemId AS ParentWorkItemId,
                c.Depth + 1 AS Depth
        FROM    WorkItemChildren c
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND c.ParentWorkItemId = l.TargetWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward' -- 'System.LinkTypes.Hierarchy-Forward'
                AND l.DeletedDate = '9999-01-01'
        WHERE   c.Depth < 50
    )
    INSERT  #SrcUp
    SELECT  ParentWorkItemId AS AncestorWorkItemId,
            WorkItemId,
            MIN(Depth) -- loop detection
    FROM    WorkItemChildren
    GROUP BY ParentWorkItemId,
            WorkItemId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #Src
    (
        WorkItemId                      INT                 NOT NULL,
        DescendantWorkItemId            INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
    )

    -- flatten the descendants with the ancestors of the impacted children
    INSERT  #Src
    SELECT  DISTINCT AncestorWorkItemId,
            DescendantWorkItemId,
            d.Depth + u.Depth
    FROM    #SrcDown d
    JOIN    #SrcUp u
    ON      d.WorkItemId = u.WorkItemId

    -- cleanup rollup roots for descendents of impacted child workitems (if deletes found)
    DELETE  t
    FROM    AnalyticsModel.tbl_WorkItemDescendant t
    JOIN    #SrcDown d -- the subtree of the impacted child
    ON      d.DescendantWorkItemId = t.DescendantWorkItemId
            AND d.Depth < t.Depth
    LEFT JOIN #Src s
    ON      t.WorkItemId = s.WorkItemId
            AND t.DescendantWorkItemId = s.DescendantWorkItemId
    WHERE   t.PartitionId = @partitionId
            AND (s.DescendantWorkItemId IS NULL OR (s.Depth != t.Depth))
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    SET @endStateData = (SELECT MAX(ChildWorkItemId) FROM #TriggerWorkItemId)
    SET @complete = IIF((SELECT COUNT(*) FROM #TriggerWorkItemId) >= @batchSizeMax, 0, 1)

    DROP TABLE #TriggerWorkItemId
    DROP TABLE #SrcDown
    DROP TABLE #SrcUp
    DROP TABLE #Src

    RETURN 0
END

GO

