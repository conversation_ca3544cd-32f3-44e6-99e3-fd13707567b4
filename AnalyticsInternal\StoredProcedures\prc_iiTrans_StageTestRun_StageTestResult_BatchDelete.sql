/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 83519E3B8E4EF745AA60C43F851CB986944217FE
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestRun_StageTestResult_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #DeletedTestRun (TestRunId INT, DataSourceId INT)

    INSERT  #DeletedTestRun
    SELECT  TOP (@batchSizeMax) WITH TIES
            TestRunId,
            DataSourceId
    FROM    AnalyticsStage.tbl_TestRun_Deleted d
    WHERE   PartitionId = @partitionId
            AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND d.TestRunId > ISNULL(@stateData, -1)
    GROUP BY TestRunId, DataSourceId
    ORDER BY TestRunId ASC
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)
    SET @endStateData = (SELECT MAX(TestRunId) FROM #DeletedTestRun)

    -- make sure it's still gone from stage
    DELETE  t
    FROM    #DeletedTestRun t
    JOIN    AnalyticsStage.tbl_TestRun s
    ON      s.TestRunId = t.TestRunId
            AND s.DataSourceId = t.DataSourceId
    WHERE   s.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    FROM    AnalyticsStage.tbl_TestResult t
    JOIN    #DeletedTestRun d
    ON      d.TestRunId = t.TestRunId
            AND d.DataSourceId = t.DataSourceId
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #DeletedTestRun

    RETURN 0
END

GO

