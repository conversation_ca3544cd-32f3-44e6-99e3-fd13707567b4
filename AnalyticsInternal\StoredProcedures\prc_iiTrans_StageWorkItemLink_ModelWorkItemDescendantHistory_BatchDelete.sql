/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A4D36B154C3BF12DC5B49755A82805C4761BA78E
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemLink_ModelWorkItemDescendantHistory_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    CREATE TABLE #TriggerWorkItemId
    (
         ChildWorkItemId     INT   NOT NULL PRIMARY KEY
    )

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)

    INSERT  #TriggerWorkItemId
    SELECT  TOP (@batchSizeMax)
            TargetId
    FROM    AnalyticsStage.tbl_WorkItemLink_Deleted l WITH (INDEX (IX_tbl_WorkItemLink_Deleted_AxBatchIdDeleted))
    INNER LOOP JOIN    AnalyticsStage.tbl_WorkItemLinkType lt
    ON      lt.PartitionId = l.PartitionId
            AND lt.LinkTypeId = l.LinkTypeId
    WHERE   l.PartitionId = @partitionId
            AND l.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND lt.ReferenceName = 'System.LinkTypes.Hierarchy'
            AND TargetId >= @workItemIdStart
    GROUP BY TargetId
    ORDER BY TargetId ASC
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #SrcDown
    (
        WorkItemId                      INT                 NOT NULL,
        DescendantWorkItemId            INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
        CreatedDate                     DATETIMEOFFSET      NOT NULL,
        DeletedDate                     DATETIMEOFFSET      NOT NULL,
        IsCurrent                       BIT                 NOT NULL,
    )

    -- recurse down to find descendants of the ChildWorkItemId
    ;WITH WorkItemParents AS
    (
        SELECT  tc.ChildWorkItemId AS WorkItemId,
                tc.ChildWorkItemId AS ChildWorkItemId,
                0 AS Depth,
                CAST('1900-01-01' AS DATETIMEOFFSET) AS CreatedDate, -- TODO - make this Syste_CreatedDate, which turns this into a MERGE operation
                CAST('9999-01-01' AS DATETIMEOFFSET) AS DeletedDate,
                CAST(1 AS BIT) AS IsCurrent
        FROM    #TriggerWorkItemId tc
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND tc.ChildWorkItemId = l.TargetWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'

        UNION ALL

        SELECT  p.WorkItemId,
                l.TargetWorkItemId AS ChildWorkItemId,
                p.Depth + 1 AS Depth,
                IIF(p.CreatedDate > l.CreatedDate, p.CreatedDate, l.CreatedDate) CreatedDate,
                IIF(p.DeletedDate < l.DeletedDate, p.DeletedDate, l.DeletedDate) DeletedDate,
                CAST(IIF(p.IsCurrent = 1 AND l.IsCurrent = 1, 1, 0) AS BIT) AS IsCurrent
        FROM    WorkItemParents p
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND p.ChildWorkItemId = l.SourceWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
                AND l.CreatedDate < p.DeletedDate
                AND l.DeletedDate > p.CreatedDate
        WHERE   p.Depth < 50
    )
    INSERT  #SrcDown
    SELECT  WorkItemId,
            ChildWorkItemId AS DescendantWorkItemId,
            MIN(Depth), -- loop detection
            CreatedDate,
            DeletedDate,
            IsCurrent
    FROM    WorkItemParents
    GROUP BY WorkItemId,
            ChildWorkItemId,
            CreatedDate,
            DeletedDate,
            IsCurrent
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #SrcUp
    (
        AncestorWorkItemId              INT                 NOT NULL,
        WorkItemId                      INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
        CreatedDate                     DATETIMEOFFSET      NOT NULL,
        DeletedDate                     DATETIMEOFFSET      NOT NULL,
        IsCurrent                       BIT                 NOT NULL,
    )

    -- recurse up to find ancestors of the ChildWorkItemId
    ;WITH WorkItemChildren AS
    (
        SELECT  tc.ChildWorkItemId AS WorkItemId,
                l.SourceWorkItemId AS ParentWorkItemId,
                1 AS Depth,
                l.CreatedDate,
                l.DeletedDate,
                l.IsCurrent
        FROM    #TriggerWorkItemId tc
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND tc.ChildWorkItemId = l.TargetWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'

        UNION ALL

        SELECT  c.WorkItemId,
                l.SourceWorkItemId AS ParentWorkItemId,
                c.Depth + 1 AS Depth,
                IIF(c.CreatedDate > l.CreatedDate, c.CreatedDate, l.CreatedDate) CreatedDate,
                IIF(c.DeletedDate < l.DeletedDate, c.DeletedDate, l.DeletedDate) DeletedDate,
                CAST(IIF(c.IsCurrent = 1 AND l.IsCurrent = 1, 1, 0) AS BIT) AS IsCurrent
        FROM    WorkItemChildren c
        JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = @partitionId
                AND c.ParentWorkItemId = l.TargetWorkItemId
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
                AND l.CreatedDate < c.DeletedDate
                AND l.DeletedDate > c.CreatedDate
        WHERE   c.Depth < 50
    )
    INSERT  #SrcUp
    SELECT  ParentWorkItemId AS AncestorWorkItemId,
            WorkItemId,
            MIN(Depth), -- loop detection
            CreatedDate,
            DeletedDate,
            IsCurrent
    FROM    WorkItemChildren
    GROUP BY ParentWorkItemId,
            WorkItemId,
            CreatedDate,
            DeletedDate,
            IsCurrent
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #Src
    (
        WorkItemId                      INT                 NOT NULL,
        DescendantWorkItemId            INT                 NOT NULL,
        Depth                           INT                 NOT NULL,
        CreatedDate                     DATETIMEOFFSET      NOT NULL,
        DeletedDate                     DATETIMEOFFSET      NOT NULL,
        IsCurrent                       BIT                 NOT NULL,
        IsLastRevisionOfPeriod          INT                 NOT NULL,
        PRIMARY KEY (WorkItemId, DescendantWorkItemId, CreatedDate)
    )

    -- flatten the descendants with the ancestors of the impacted children
    INSERT  #Src
    SELECT  DISTINCT AncestorWorkItemId,
            DescendantWorkItemId,
            d.Depth + u.Depth,
            IIF(u.CreatedDate > d.CreatedDate, u.CreatedDate, d.CreatedDate) AT TIME ZONE @timeZone AS CreatedDate,
            IIF(u.DeletedDate < d.DeletedDate, u.DeletedDate, d.DeletedDate) AT TIME ZONE @timeZone AS DeletedDate,
            IIF(u.IsCurrent = 1 AND d.IsCurrent = 1, 1, 0) AS IsCurrent,
            2047 AS IsLastRevisionOfPeriod -- set in next step
    FROM    #SrcDown d
    JOIN    #SrcUp u
    ON      d.WorkItemId = u.WorkItemId
            AND u.CreatedDate < d.DeletedDate
            AND u.DeletedDate > d.CreatedDate

    -- cleanup rollup roots for descendents of impacted child workitems (if deletes found)
    DELETE  t
    FROM    AnalyticsModel.tbl_WorkItemDescendantHistory t
    JOIN    #SrcDown d -- the subtree of the impacted child
    ON      d.DescendantWorkItemId = t.DescendantWorkItemId
            AND d.Depth < t.Depth
    LEFT JOIN #Src s
    ON      t.WorkItemId = s.WorkItemId
            AND t.DescendantWorkItemId = s.DescendantWorkItemId
            AND t.CreatedDate = s.CreatedDate
            AND t.DeletedDate = s.DeletedDate
    WHERE   t.PartitionId = @partitionId
--            AND d.WorkItemId IN (SELECT ChildWorkItemId FROM #TriggerWorkItemId tr WHERE HasDeletes = 1)
            AND (s.DescendantWorkItemId IS NULL OR s.Depth != t.Depth OR s.IsCurrent != t.IsCurrent)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    SET @endStateData = (SELECT MAX(ChildWorkItemId) FROM #TriggerWorkItemId)
    SET @complete = IIF((SELECT COUNT(*) FROM #TriggerWorkItemId) >= @batchSizeMax, 0, 1)

    DROP TABLE #TriggerWorkItemId
    DROP TABLE #SrcDown
    DROP TABLE #SrcUp
    DROP TABLE #Src

    RETURN 0
END

GO

