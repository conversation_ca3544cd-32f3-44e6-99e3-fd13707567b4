/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B1BD535696CCE4147F0F383AC60CFA6039FA1354
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestRun_ModelTestRun_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 1000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    CREATE TABLE #ImpactedRuns
    (
        TestRunId INT NOT NULL,
        DataSourceId INT NOT NULL,
        PRIMARY KEY(TestRunId, DataSourceId)
    )

    INSERT  #ImpactedRuns
    SELECT  TOP (@batchSizeMax) WITH TIES
            TestRunId,
            DataSourceId
    FROM    AnalyticsStage.tbl_TestRun
    WHERE   PartitionId = @partitionId
            AND TestRunId > ISNULL(@stateData, -1)
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    ORDER BY TestRunId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(TestRunId) FROM #ImpactedRuns)

    CREATE TABLE #TestRun
    (
        TestRunSK                       INT                 NULL,
        TestRunId                       INT                 NOT NULL,
        DataSourceId                    INT                 NOT NULL,
        ProjectSK                       UNIQUEIDENTIFIER    NULL,
        Title                           NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        IsAutomated                     BIT                 NULL,
        ReleaseSK                       INT                 NULL,
        ReleaseEnvironmentSK            INT                 NULL,
        BuildSK                         INT                 NULL,
        ReleasePipelineSK               INT                 NULL,
        ReleaseStageSK                  INT                 NULL,
        BuildPipelineSK                 INT                 NULL,
        BranchSK                        INT                 NULL,
        StartedDate                     DATETIMEOFFSET      NULL,
        StartedDateSK                   INT                 NULL,
        CompletedDate                   DATETIMEOFFSET      NULL,
        CompletedDateSK                 INT                 NULL,
        RunDurationSeconds              DECIMAL(18,3)       NULL,
        -- aggregations of test results
        ResultDurationSeconds           DECIMAL(18,3)       NULL,
        ResultCount                     INT                 NULL,
        ResultPassCount                 INT                 NULL,
        ResultFailCount                 INT                 NULL,
        -- counts for invididual outcomes
        ResultOutcomeNoneCount          INT                 NULL,
        ResultOutcomePassedCount        INT                 NULL,
        ResultOutcomeFailedCount        INT                 NULL,
        ResultOutcomeInconclusiveCount  INT                 NULL,
        ResultOutcomeTimeoutCount       INT                 NULL,
        ResultOutcomeAbortedCount       INT                 NULL,
        ResultOutcomeBlockedCount       INT                 NULL,
        ResultOutcomeNotExecutedCount   INT                 NULL,
        ResultOutcomeWarningCount       INT                 NULL,
        ResultOutcomeErrorCount         INT                 NULL,
        ResultOutcomeNotApplicableCount INT                 NULL,
        ResultOutcomeNotImpactedCount   INT                 NULL,
        Workflow                        TINYINT             NULL,
        TestRunType                     TINYINT             NULL,
        PRIMARY KEY(TestRunId, DataSourceId)
    )

    INSERT  #TestRun
    (
        TestRunSK,
        TestRunId,
        DataSourceId,
        ProjectSK,
        Title,
        IsAutomated,
        ReleaseSK,
        ReleaseEnvironmentSK,
        BuildSK,
        ReleasePipelineSK,
        ReleaseStageSK,
        BuildPipelineSK,
        BranchSK,
        StartedDate,
        StartedDateSK,
        CompletedDate,
        CompletedDateSK,
        RunDurationSeconds,
        Workflow,
        TestRunType
    )
    SELECT  mtr.TestRunSK,
            s.TestRunId,
            s.DataSourceId,
            s.ProjectGuid,
            s.TestRunTitle,
            s.IsAutomated,
            mr.ReleaseSK,
            me.ReleaseEnvironmentSK,
            mb.BuildSK,
            mrp.ReleasePipelineSK,
            mep.ReleaseStageSK,
            mbp.BuildPipelineSK,
            mbr.BranchSK,
            s.DateStarted AT TIME ZONE @timeZone,
            AnalyticsInternal.func_GenDateSK(s.DateStarted AT TIME ZONE @timeZone),
            s.DateCompleted AT TIME ZONE @timeZone,
            AnalyticsInternal.func_GenDateSK(s.DateCompleted AT TIME ZONE @timeZone),
            DATEDIFF_BIG(millisecond, s.DateStarted, s.DateCompleted) / 1000.0,
            CASE
                WHEN s.SourceWorkflow = 'CI' THEN 1
                WHEN s.SourceWorkflow = 'CD' THEN 2
            END AS Workflow,
            CASE
                WHEN s.IsAutomated = 1 THEN 1 ELSE 2
            END AS TestRunType
    FROM    AnalyticsStage.tbl_TestRun s
    JOIN    #ImpactedRuns ir
    ON      ir.TestRunId = s.TestRunId
            AND ir.DataSourceId = s.DataSourceId
    LEFT JOIN AnalyticsModel.tbl_Release mr
    ON      mr.PartitionId = @partitionId
            AND mr.ReleaseId = s.ReleaseId
            AND mr.ProjectSK = s.ProjectGuid
    LEFT JOIN AnalyticsModel.tbl_ReleasePipeline mrp
    ON      mrp.PartitionId = @partitionId
            AND mrp.ReleasePipelineId = s.ReleaseDefinitionId
            AND mrp.ProjectSK = s.ProjectGuid
    LEFT JOIN AnalyticsModel.tbl_Branch mbr
    ON      mbr.PartitionId = @partitionId
            AND mbr.RepositoryId = s.RepositoryId
            AND mbr.BranchName = s.BranchName
    LEFT JOIN AnalyticsModel.tbl_Build mb
    ON      mb.PartitionId = @partitionId
            AND mb.BuildId = s.BuildId
    LEFT JOIN AnalyticsModel.tbl_BuildPipeline mbp
    ON      mbp.PartitionId = @partitionId
            AND mbp.BuildPipelineId = s.BuildDefinitionId
    LEFT JOIN AnalyticsModel.tbl_ReleaseEnvironment me
    ON      me.PartitionId = @partitionId
            AND me.ReleaseEnvironmentId = s.ReleaseEnvironmentId
            AND me.ProjectSK = s.ProjectGuid
    LEFT JOIN AnalyticsModel.tbl_ReleaseStage mep
    ON      mep.PartitionId = @partitionId
            AND mep.ReleaseStageId = s.ReleaseEnvironmentDefinitionId
            AND mep.ProjectSK = s.ProjectGuid
    LEFT JOIN AnalyticsModel.tbl_TestRun mtr
    ON      mtr.PartitionId = @partitionId
            AND mtr.TestRunId = s.TestRunId
            AND mtr.DataSourceId = s.DataSourceId
    WHERE   s.PartitionId = @partitionId
            AND s.DateCompleted IS NOT NULL -- TestRun must be completed
            AND s.DateCompleted <= DATEADD(DAY, 1, SYSUTCDATETIME()) -- Ignore Test run from distant future
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    UPDATE  t
    SET     ResultCount                     = s.ResultCount,
            ResultPassCount                 = s.ResultPassCount,
            ResultFailCount                 = s.ResultFailCount,
            ResultDurationSeconds           = s.ResultDurationSeconds,
            ResultOutcomeNoneCount          = s.ResultOutcomeNoneCount,
            ResultOutcomePassedCount        = s.ResultOutcomePassedCount,
            ResultOutcomeFailedCount        = s.ResultOutcomeFailedCount,
            ResultOutcomeInconclusiveCount  = s.ResultOutcomeInconclusiveCount,
            ResultOutcomeTimeoutCount       = s.ResultOutcomeTimeoutCount,
            ResultOutcomeAbortedCount       = s.ResultOutcomeAbortedCount,
            ResultOutcomeBlockedCount       = s.ResultOutcomeBlockedCount,
            ResultOutcomeNotExecutedCount   = s.ResultOutcomeNotExecutedCount,
            ResultOutcomeWarningCount       = s.ResultOutcomeWarningCount,
            ResultOutcomeErrorCount         = s.ResultOutcomeErrorCount,
            ResultOutcomeNotApplicableCount = s.ResultOutcomeNotApplicableCount,
            ResultOutcomeNotImpactedCount   = s.ResultOutcomeNotImpactedCount
    FROM    #TestRun t
    CROSS APPLY
    (
        SELECT  COUNT(*) AS ResultCount,
                ISNULL(SUM(IIF(Outcome IN (2), 1, 0)), 0) AS ResultPassCount,
                ISNULL(SUM(IIF(Outcome IN (3, 5, 6, 7, 10), 1, 0)), 0) AS ResultFailCount,
                ISNULL(SUM(DATEDIFF_BIG(millisecond, DateStarted, DateCompleted) / 1000.0), 0.0) AS ResultDurationSeconds,
                ISNULL(SUM(IIF(Outcome = 1, 1, 0)), 0) AS ResultOutcomeNoneCount,
                ISNULL(SUM(IIF(Outcome = 2, 1, 0)), 0) AS ResultOutcomePassedCount,
                ISNULL(SUM(IIF(Outcome = 3, 1, 0)), 0) AS ResultOutcomeFailedCount,
                ISNULL(SUM(IIF(Outcome = 4, 1, 0)), 0) AS ResultOutcomeInconclusiveCount,
                ISNULL(SUM(IIF(Outcome = 5, 1, 0)), 0) AS ResultOutcomeTimeoutCount,
                ISNULL(SUM(IIF(Outcome = 6, 1, 0)), 0) AS ResultOutcomeAbortedCount,
                ISNULL(SUM(IIF(Outcome = 7, 1, 0)), 0) AS ResultOutcomeBlockedCount,
                ISNULL(SUM(IIF(Outcome = 8, 1, 0)), 0) AS ResultOutcomeNotExecutedCount,
                ISNULL(SUM(IIF(Outcome = 9, 1, 0)), 0) AS ResultOutcomeWarningCount,
                ISNULL(SUM(IIF(Outcome = 10, 1, 0)), 0) AS ResultOutcomeErrorCount,
                ISNULL(SUM(IIF(Outcome = 11, 1, 0)), 0) AS ResultOutcomeNotApplicableCount,
                ISNULL(SUM(IIF(Outcome = 14, 1, 0)), 0) AS ResultOutcomeNotImpactedCount
        FROM    AnalyticsStage.tbl_TestResult r
        WHERE   PartitionId = @partitionId
        AND     TestRunId = t.TestRunId
        AND     DataSourceId = t.DataSourceId
    ) s
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    MERGE AnalyticsModel.tbl_TestRun AS t
    USING #TestRun AS s
    ON (t.PartitionId = @partitionId AND t.TestRunSK = s.TestRunSK)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.ProjectSK,
        s.Title,
        s.IsAutomated,
        s.ReleaseSK,
        s.ReleaseEnvironmentSK,
        s.BuildSK,
        s.BranchSK,
        s.ReleasePipelineSK,
        s.ReleaseStageSK,
        s.BuildPipelineSK,
        s.StartedDate,
        s.StartedDateSK,
        s.CompletedDate,
        s.CompletedDateSK,
        s.RunDurationSeconds,
        s.ResultDurationSeconds,
        s.ResultCount,
        s.ResultPassCount,
        s.ResultFailCount,
        s.ResultOutcomeNoneCount,
        s.ResultOutcomePassedCount,
        s.ResultOutcomeFailedCount,
        s.ResultOutcomeInconclusiveCount,
        s.ResultOutcomeTimeoutCount,
        s.ResultOutcomeAbortedCount,
        s.ResultOutcomeBlockedCount,
        s.ResultOutcomeNotExecutedCount,
        s.ResultOutcomeWarningCount,
        s.ResultOutcomeErrorCount,
        s.ResultOutcomeNotApplicableCount,
        s.ResultOutcomeNotImpactedCount,
        s.Workflow,
        s.TestRunType
        INTERSECT
        SELECT
        t.ProjectSK,
        t.Title,
        t.IsAutomated,
        t.ReleaseSK,
        t.ReleaseEnvironmentSK,
        t.BuildSK,
        t.BranchSK,
        t.ReleasePipelineSK,
        t.ReleaseStageSK,
        t.BuildPipelineSK,
        t.StartedDate,
        t.StartedDateSK,
        t.CompletedDate,
        t.CompletedDateSK,
        t.RunDurationSeconds,
        t.ResultDurationSeconds,
        t.ResultCount,
        t.ResultPassCount,
        t.ResultFailCount,
        t.ResultOutcomeNoneCount,
        t.ResultOutcomePassedCount,
        t.ResultOutcomeFailedCount,
        t.ResultOutcomeInconclusiveCount,
        t.ResultOutcomeTimeoutCount,
        t.ResultOutcomeAbortedCount,
        t.ResultOutcomeBlockedCount,
        t.ResultOutcomeNotExecutedCount,
        t.ResultOutcomeWarningCount,
        t.ResultOutcomeErrorCount,
        t.ResultOutcomeNotApplicableCount,
        t.ResultOutcomeNotImpactedCount,
        t.Workflow,
        t.TestRunType
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate            = @batchDt,
        AnalyticsBatchId                = @batchId,
        ProjectSK                       = s.ProjectSK,
        Title                           = s.Title,
        IsAutomated                     = s.IsAutomated,
        ReleaseSK                       = s.ReleaseSK,
        ReleaseEnvironmentSK            = s.ReleaseEnvironmentSK,
        BuildSK                         = s.BuildSK,
        ReleasePipelineSK               = s.ReleasePipelineSK,
        ReleaseStageSK                  = s.ReleaseStageSK,
        BuildPipelineSK                 = s.BuildPipelineSK,
        BranchSK                        = s.BranchSK,
        StartedDate                     = s.StartedDate,
        StartedDateSK                   = s.StartedDateSK,
        CompletedDate                   = s.CompletedDate,
        CompletedDateSK                 = s.CompletedDateSK,
        RunDurationSeconds              = s.RunDurationSeconds,
        ResultDurationSeconds           = s.ResultDurationSeconds,
        ResultCount                     = s.ResultCount,
        ResultPassCount                 = s.ResultPassCount,
        ResultFailCount                 = s.ResultFailCount,
        ResultOutcomeNoneCount          = s.ResultOutcomeNoneCount,
        ResultOutcomePassedCount        = s.ResultOutcomePassedCount,
        ResultOutcomeFailedCount        = s.ResultOutcomeFailedCount,
        ResultOutcomeInconclusiveCount  = s.ResultOutcomeInconclusiveCount,
        ResultOutcomeTimeoutCount       = s.ResultOutcomeTimeoutCount,
        ResultOutcomeAbortedCount       = s.ResultOutcomeAbortedCount,
        ResultOutcomeBlockedCount       = s.ResultOutcomeBlockedCount,
        ResultOutcomeNotExecutedCount   = s.ResultOutcomeNotExecutedCount,
        ResultOutcomeWarningCount       = s.ResultOutcomeWarningCount,
        ResultOutcomeErrorCount         = s.ResultOutcomeErrorCount,
        ResultOutcomeNotApplicableCount = s.ResultOutcomeNotApplicableCount,
        ResultOutcomeNotImpactedCount   = s.ResultOutcomeNotImpactedCount,
        Workflow                        = s.Workflow,
        TestRunType                     = s.TestRunType
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        TestRunId,
        DataSourceId,
        ProjectSK,
        Title,
        IsAutomated,
        ReleaseSK,
        ReleaseEnvironmentSK,
        BuildSK,
        ReleasePipelineSK,
        ReleaseStageSK,
        BuildPipelineSK,
        BranchSK,
        StartedDate,
        StartedDateSK,
        CompletedDate,
        CompletedDateSK,
        RunDurationSeconds,
        ResultDurationSeconds,
        ResultCount,
        ResultPassCount,
        ResultFailCount,
        ResultOutcomeNoneCount,
        ResultOutcomePassedCount,
        ResultOutcomeFailedCount,
        ResultOutcomeInconclusiveCount,
        ResultOutcomeTimeoutCount,
        ResultOutcomeAbortedCount,
        ResultOutcomeBlockedCount,
        ResultOutcomeNotExecutedCount,
        ResultOutcomeWarningCount,
        ResultOutcomeErrorCount,
        ResultOutcomeNotApplicableCount,
        ResultOutcomeNotImpactedCount,
        Workflow,
        TestRunType
    )
    VALUES (
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.TestRunId,
        s.DataSourceId,
        s.ProjectSK,
        s.Title,
        s.IsAutomated,
        s.ReleaseSK,
        s.ReleaseEnvironmentSK,
        s.BuildSK,
        s.ReleasePipelineSK,
        s.ReleaseStageSK,
        s.BuildPipelineSK,
        s.BranchSK,
        s.StartedDate,
        s.StartedDateSK,
        s.CompletedDate,
        s.CompletedDateSK,
        s.RunDurationSeconds,
        s.ResultDurationSeconds,
        s.ResultCount,
        s.ResultPassCount,
        s.ResultFailCount,
        s.ResultOutcomeNoneCount,
        s.ResultOutcomePassedCount,
        s.ResultOutcomeFailedCount,
        s.ResultOutcomeInconclusiveCount,
        s.ResultOutcomeTimeoutCount,
        s.ResultOutcomeAbortedCount,
        s.ResultOutcomeBlockedCount,
        s.ResultOutcomeNotExecutedCount,
        s.ResultOutcomeWarningCount,
        s.ResultOutcomeErrorCount,
        s.ResultOutcomeNotApplicableCount,
        s.ResultOutcomeNotImpactedCount,
        s.Workflow,
        s.TestRunType
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    DROP TABLE #TestRun
    DROP TABLE #ImpactedRuns

    RETURN 0
END

GO

