CREATE TABLE [AnalyticsModel].[tbl_WorkItemRevisionCustom07] (
    [PartitionId]          INT                NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]     BIGINT             NOT NULL,
    [WorkItemId]           INT                NOT NULL,
    [Revision]             INT                NOT NULL,
    [WorkItemRevisionSK]   INT                NOT NULL,
    [String1201]           NVARCHAR (256)     NULL,
    [String1202]           NVARCHAR (256)     NULL,
    [String1203]           NVARCHAR (256)     NULL,
    [String1204]           NVARCHAR (256)     NULL,
    [String1205]           NVARCHAR (256)     NULL,
    [String1206]           NVARCHAR (256)     NULL,
    [String1207]           NVARCHAR (256)     NULL,
    [String1208]           NVARCHAR (256)     NULL,
    [String1209]           NVARCHAR (256)     NULL,
    [String1210]           NVARCHAR (256)     NULL,
    [String1211]           NVARCHAR (256)     NULL,
    [String1212]           NVARCHA<PERSON> (256)     NULL,
    [String1213]           NVARCHA<PERSON> (256)     NULL,
    [String1214]           NVARCHAR (256)     NULL,
    [String1215]           NVARCHAR (256)     NULL,
    [String1216]           NVARCHAR (256)     NULL,
    [String1217]           NVARCHAR (256)     NULL,
    [String1218]           NVARCHAR (256)     NULL,
    [String1219]           NVARCHAR (256)     NULL,
    [String1220]           NVARCHAR (256)     NULL,
    [String1221]           NVARCHAR (256)     NULL,
    [String1222]           NVARCHAR (256)     NULL,
    [String1223]           NVARCHAR (256)     NULL,
    [String1224]           NVARCHAR (256)     NULL,
    [String1225]           NVARCHAR (256)     NULL,
    [String1226]           NVARCHAR (256)     NULL,
    [String1227]           NVARCHAR (256)     NULL,
    [String1228]           NVARCHAR (256)     NULL,
    [String1229]           NVARCHAR (256)     NULL,
    [String1230]           NVARCHAR (256)     NULL,
    [String1231]           NVARCHAR (256)     NULL,
    [String1232]           NVARCHAR (256)     NULL,
    [String1233]           NVARCHAR (256)     NULL,
    [String1234]           NVARCHAR (256)     NULL,
    [String1235]           NVARCHAR (256)     NULL,
    [String1236]           NVARCHAR (256)     NULL,
    [String1237]           NVARCHAR (256)     NULL,
    [String1238]           NVARCHAR (256)     NULL,
    [String1239]           NVARCHAR (256)     NULL,
    [String1240]           NVARCHAR (256)     NULL,
    [String1241]           NVARCHAR (256)     NULL,
    [String1242]           NVARCHAR (256)     NULL,
    [String1243]           NVARCHAR (256)     NULL,
    [String1244]           NVARCHAR (256)     NULL,
    [String1245]           NVARCHAR (256)     NULL,
    [String1246]           NVARCHAR (256)     NULL,
    [String1247]           NVARCHAR (256)     NULL,
    [String1248]           NVARCHAR (256)     NULL,
    [String1249]           NVARCHAR (256)     NULL,
    [String1250]           NVARCHAR (256)     NULL,
    [String1251]           NVARCHAR (256)     NULL,
    [String1252]           NVARCHAR (256)     NULL,
    [String1253]           NVARCHAR (256)     NULL,
    [String1254]           NVARCHAR (256)     NULL,
    [String1255]           NVARCHAR (256)     NULL,
    [String1256]           NVARCHAR (256)     NULL,
    [String1257]           NVARCHAR (256)     NULL,
    [String1258]           NVARCHAR (256)     NULL,
    [String1259]           NVARCHAR (256)     NULL,
    [String1260]           NVARCHAR (256)     NULL,
    [String1261]           NVARCHAR (256)     NULL,
    [String1262]           NVARCHAR (256)     NULL,
    [String1263]           NVARCHAR (256)     NULL,
    [String1264]           NVARCHAR (256)     NULL,
    [String1265]           NVARCHAR (256)     NULL,
    [String1266]           NVARCHAR (256)     NULL,
    [String1267]           NVARCHAR (256)     NULL,
    [String1268]           NVARCHAR (256)     NULL,
    [String1269]           NVARCHAR (256)     NULL,
    [String1270]           NVARCHAR (256)     NULL,
    [String1271]           NVARCHAR (256)     NULL,
    [String1272]           NVARCHAR (256)     NULL,
    [String1273]           NVARCHAR (256)     NULL,
    [String1274]           NVARCHAR (256)     NULL,
    [String1275]           NVARCHAR (256)     NULL,
    [String1276]           NVARCHAR (256)     NULL,
    [String1277]           NVARCHAR (256)     NULL,
    [String1278]           NVARCHAR (256)     NULL,
    [String1279]           NVARCHAR (256)     NULL,
    [String1280]           NVARCHAR (256)     NULL,
    [String1281]           NVARCHAR (256)     NULL,
    [String1282]           NVARCHAR (256)     NULL,
    [String1283]           NVARCHAR (256)     NULL,
    [String1284]           NVARCHAR (256)     NULL,
    [String1285]           NVARCHAR (256)     NULL,
    [String1286]           NVARCHAR (256)     NULL,
    [String1287]           NVARCHAR (256)     NULL,
    [String1288]           NVARCHAR (256)     NULL,
    [String1289]           NVARCHAR (256)     NULL,
    [String1290]           NVARCHAR (256)     NULL,
    [String1291]           NVARCHAR (256)     NULL,
    [String1292]           NVARCHAR (256)     NULL,
    [String1293]           NVARCHAR (256)     NULL,
    [String1294]           NVARCHAR (256)     NULL,
    [String1295]           NVARCHAR (256)     NULL,
    [String1296]           NVARCHAR (256)     NULL,
    [String1297]           NVARCHAR (256)     NULL,
    [String1298]           NVARCHAR (256)     NULL,
    [String1299]           NVARCHAR (256)     NULL,
    [String1300]           NVARCHAR (256)     NULL,
    [String1301]           NVARCHAR (256)     NULL,
    [String1302]           NVARCHAR (256)     NULL,
    [String1303]           NVARCHAR (256)     NULL,
    [String1304]           NVARCHAR (256)     NULL,
    [String1305]           NVARCHAR (256)     NULL,
    [String1306]           NVARCHAR (256)     NULL,
    [String1307]           NVARCHAR (256)     NULL,
    [String1308]           NVARCHAR (256)     NULL,
    [String1309]           NVARCHAR (256)     NULL,
    [String1310]           NVARCHAR (256)     NULL,
    [String1311]           NVARCHAR (256)     NULL,
    [String1312]           NVARCHAR (256)     NULL,
    [String1313]           NVARCHAR (256)     NULL,
    [String1314]           NVARCHAR (256)     NULL,
    [String1315]           NVARCHAR (256)     NULL,
    [String1316]           NVARCHAR (256)     NULL,
    [String1317]           NVARCHAR (256)     NULL,
    [String1318]           NVARCHAR (256)     NULL,
    [String1319]           NVARCHAR (256)     NULL,
    [String1320]           NVARCHAR (256)     NULL,
    [String1321]           NVARCHAR (256)     NULL,
    [String1322]           NVARCHAR (256)     NULL,
    [String1323]           NVARCHAR (256)     NULL,
    [String1324]           NVARCHAR (256)     NULL,
    [String1325]           NVARCHAR (256)     NULL,
    [String1326]           NVARCHAR (256)     NULL,
    [String1327]           NVARCHAR (256)     NULL,
    [String1328]           NVARCHAR (256)     NULL,
    [String1329]           NVARCHAR (256)     NULL,
    [String1330]           NVARCHAR (256)     NULL,
    [String1331]           NVARCHAR (256)     NULL,
    [String1332]           NVARCHAR (256)     NULL,
    [String1333]           NVARCHAR (256)     NULL,
    [String1334]           NVARCHAR (256)     NULL,
    [String1335]           NVARCHAR (256)     NULL,
    [String1336]           NVARCHAR (256)     NULL,
    [String1337]           NVARCHAR (256)     NULL,
    [String1338]           NVARCHAR (256)     NULL,
    [String1339]           NVARCHAR (256)     NULL,
    [String1340]           NVARCHAR (256)     NULL,
    [String1341]           NVARCHAR (256)     NULL,
    [String1342]           NVARCHAR (256)     NULL,
    [String1343]           NVARCHAR (256)     NULL,
    [String1344]           NVARCHAR (256)     NULL,
    [String1345]           NVARCHAR (256)     NULL,
    [String1346]           NVARCHAR (256)     NULL,
    [String1347]           NVARCHAR (256)     NULL,
    [String1348]           NVARCHAR (256)     NULL,
    [String1349]           NVARCHAR (256)     NULL,
    [String1350]           NVARCHAR (256)     NULL,
    [String1351]           NVARCHAR (256)     NULL,
    [String1352]           NVARCHAR (256)     NULL,
    [String1353]           NVARCHAR (256)     NULL,
    [String1354]           NVARCHAR (256)     NULL,
    [String1355]           NVARCHAR (256)     NULL,
    [String1356]           NVARCHAR (256)     NULL,
    [String1357]           NVARCHAR (256)     NULL,
    [String1358]           NVARCHAR (256)     NULL,
    [String1359]           NVARCHAR (256)     NULL,
    [String1360]           NVARCHAR (256)     NULL,
    [String1361]           NVARCHAR (256)     NULL,
    [String1362]           NVARCHAR (256)     NULL,
    [String1363]           NVARCHAR (256)     NULL,
    [String1364]           NVARCHAR (256)     NULL,
    [String1365]           NVARCHAR (256)     NULL,
    [String1366]           NVARCHAR (256)     NULL,
    [String1367]           NVARCHAR (256)     NULL,
    [String1368]           NVARCHAR (256)     NULL,
    [String1369]           NVARCHAR (256)     NULL,
    [String1370]           NVARCHAR (256)     NULL,
    [String1371]           NVARCHAR (256)     NULL,
    [String1372]           NVARCHAR (256)     NULL,
    [String1373]           NVARCHAR (256)     NULL,
    [String1374]           NVARCHAR (256)     NULL,
    [String1375]           NVARCHAR (256)     NULL,
    [String1376]           NVARCHAR (256)     NULL,
    [String1377]           NVARCHAR (256)     NULL,
    [String1378]           NVARCHAR (256)     NULL,
    [String1379]           NVARCHAR (256)     NULL,
    [String1380]           NVARCHAR (256)     NULL,
    [String1381]           NVARCHAR (256)     NULL,
    [String1382]           NVARCHAR (256)     NULL,
    [String1383]           NVARCHAR (256)     NULL,
    [String1384]           NVARCHAR (256)     NULL,
    [String1385]           NVARCHAR (256)     NULL,
    [String1386]           NVARCHAR (256)     NULL,
    [String1387]           NVARCHAR (256)     NULL,
    [String1388]           NVARCHAR (256)     NULL,
    [String1389]           NVARCHAR (256)     NULL,
    [String1390]           NVARCHAR (256)     NULL,
    [String1391]           NVARCHAR (256)     NULL,
    [String1392]           NVARCHAR (256)     NULL,
    [String1393]           NVARCHAR (256)     NULL,
    [String1394]           NVARCHAR (256)     NULL,
    [String1395]           NVARCHAR (256)     NULL,
    [String1396]           NVARCHAR (256)     NULL,
    [String1397]           NVARCHAR (256)     NULL,
    [String1398]           NVARCHAR (256)     NULL,
    [String1399]           NVARCHAR (256)     NULL,
    [String1400]           NVARCHAR (256)     NULL,
    [Integer0301]          BIGINT             NULL,
    [Integer0302]          BIGINT             NULL,
    [Integer0303]          BIGINT             NULL,
    [Integer0304]          BIGINT             NULL,
    [Integer0305]          BIGINT             NULL,
    [Integer0306]          BIGINT             NULL,
    [Integer0307]          BIGINT             NULL,
    [Integer0308]          BIGINT             NULL,
    [Integer0309]          BIGINT             NULL,
    [Integer0310]          BIGINT             NULL,
    [Integer0311]          BIGINT             NULL,
    [Integer0312]          BIGINT             NULL,
    [Integer0313]          BIGINT             NULL,
    [Integer0314]          BIGINT             NULL,
    [Integer0315]          BIGINT             NULL,
    [Integer0316]          BIGINT             NULL,
    [Integer0317]          BIGINT             NULL,
    [Integer0318]          BIGINT             NULL,
    [Integer0319]          BIGINT             NULL,
    [Integer0320]          BIGINT             NULL,
    [Integer0321]          BIGINT             NULL,
    [Integer0322]          BIGINT             NULL,
    [Integer0323]          BIGINT             NULL,
    [Integer0324]          BIGINT             NULL,
    [Integer0325]          BIGINT             NULL,
    [Integer0326]          BIGINT             NULL,
    [Integer0327]          BIGINT             NULL,
    [Integer0328]          BIGINT             NULL,
    [Integer0329]          BIGINT             NULL,
    [Integer0330]          BIGINT             NULL,
    [Integer0331]          BIGINT             NULL,
    [Integer0332]          BIGINT             NULL,
    [Integer0333]          BIGINT             NULL,
    [Integer0334]          BIGINT             NULL,
    [Integer0335]          BIGINT             NULL,
    [Integer0336]          BIGINT             NULL,
    [Integer0337]          BIGINT             NULL,
    [Integer0338]          BIGINT             NULL,
    [Integer0339]          BIGINT             NULL,
    [Integer0340]          BIGINT             NULL,
    [Integer0341]          BIGINT             NULL,
    [Integer0342]          BIGINT             NULL,
    [Integer0343]          BIGINT             NULL,
    [Integer0344]          BIGINT             NULL,
    [Integer0345]          BIGINT             NULL,
    [Integer0346]          BIGINT             NULL,
    [Integer0347]          BIGINT             NULL,
    [Integer0348]          BIGINT             NULL,
    [Integer0349]          BIGINT             NULL,
    [Integer0350]          BIGINT             NULL,
    [Double0301]           FLOAT (53)         NULL,
    [Double0302]           FLOAT (53)         NULL,
    [Double0303]           FLOAT (53)         NULL,
    [Double0304]           FLOAT (53)         NULL,
    [Double0305]           FLOAT (53)         NULL,
    [Double0306]           FLOAT (53)         NULL,
    [Double0307]           FLOAT (53)         NULL,
    [Double0308]           FLOAT (53)         NULL,
    [Double0309]           FLOAT (53)         NULL,
    [Double0310]           FLOAT (53)         NULL,
    [Double0311]           FLOAT (53)         NULL,
    [Double0312]           FLOAT (53)         NULL,
    [Double0313]           FLOAT (53)         NULL,
    [Double0314]           FLOAT (53)         NULL,
    [Double0315]           FLOAT (53)         NULL,
    [Double0316]           FLOAT (53)         NULL,
    [Double0317]           FLOAT (53)         NULL,
    [Double0318]           FLOAT (53)         NULL,
    [Double0319]           FLOAT (53)         NULL,
    [Double0320]           FLOAT (53)         NULL,
    [Double0321]           FLOAT (53)         NULL,
    [Double0322]           FLOAT (53)         NULL,
    [Double0323]           FLOAT (53)         NULL,
    [Double0324]           FLOAT (53)         NULL,
    [Double0325]           FLOAT (53)         NULL,
    [Double0326]           FLOAT (53)         NULL,
    [Double0327]           FLOAT (53)         NULL,
    [Double0328]           FLOAT (53)         NULL,
    [Double0329]           FLOAT (53)         NULL,
    [Double0330]           FLOAT (53)         NULL,
    [Double0331]           FLOAT (53)         NULL,
    [Double0332]           FLOAT (53)         NULL,
    [Double0333]           FLOAT (53)         NULL,
    [Double0334]           FLOAT (53)         NULL,
    [Double0335]           FLOAT (53)         NULL,
    [Double0336]           FLOAT (53)         NULL,
    [Double0337]           FLOAT (53)         NULL,
    [Double0338]           FLOAT (53)         NULL,
    [Double0339]           FLOAT (53)         NULL,
    [Double0340]           FLOAT (53)         NULL,
    [Double0341]           FLOAT (53)         NULL,
    [Double0342]           FLOAT (53)         NULL,
    [Double0343]           FLOAT (53)         NULL,
    [Double0344]           FLOAT (53)         NULL,
    [Double0345]           FLOAT (53)         NULL,
    [Double0346]           FLOAT (53)         NULL,
    [Double0347]           FLOAT (53)         NULL,
    [Double0348]           FLOAT (53)         NULL,
    [Double0349]           FLOAT (53)         NULL,
    [Double0350]           FLOAT (53)         NULL,
    [DateTime0301]         DATETIMEOFFSET (7) NULL,
    [DateTime0302]         DATETIMEOFFSET (7) NULL,
    [DateTime0303]         DATETIMEOFFSET (7) NULL,
    [DateTime0304]         DATETIMEOFFSET (7) NULL,
    [DateTime0305]         DATETIMEOFFSET (7) NULL,
    [DateTime0306]         DATETIMEOFFSET (7) NULL,
    [DateTime0307]         DATETIMEOFFSET (7) NULL,
    [DateTime0308]         DATETIMEOFFSET (7) NULL,
    [DateTime0309]         DATETIMEOFFSET (7) NULL,
    [DateTime0310]         DATETIMEOFFSET (7) NULL,
    [DateTime0311]         DATETIMEOFFSET (7) NULL,
    [DateTime0312]         DATETIMEOFFSET (7) NULL,
    [DateTime0313]         DATETIMEOFFSET (7) NULL,
    [DateTime0314]         DATETIMEOFFSET (7) NULL,
    [DateTime0315]         DATETIMEOFFSET (7) NULL,
    [DateTime0316]         DATETIMEOFFSET (7) NULL,
    [DateTime0317]         DATETIMEOFFSET (7) NULL,
    [DateTime0318]         DATETIMEOFFSET (7) NULL,
    [DateTime0319]         DATETIMEOFFSET (7) NULL,
    [DateTime0320]         DATETIMEOFFSET (7) NULL,
    [DateTime0321]         DATETIMEOFFSET (7) NULL,
    [DateTime0322]         DATETIMEOFFSET (7) NULL,
    [DateTime0323]         DATETIMEOFFSET (7) NULL,
    [DateTime0324]         DATETIMEOFFSET (7) NULL,
    [DateTime0325]         DATETIMEOFFSET (7) NULL,
    [DateTime0326]         DATETIMEOFFSET (7) NULL,
    [DateTime0327]         DATETIMEOFFSET (7) NULL,
    [DateTime0328]         DATETIMEOFFSET (7) NULL,
    [DateTime0329]         DATETIMEOFFSET (7) NULL,
    [DateTime0330]         DATETIMEOFFSET (7) NULL,
    [DateTime0331]         DATETIMEOFFSET (7) NULL,
    [DateTime0332]         DATETIMEOFFSET (7) NULL,
    [DateTime0333]         DATETIMEOFFSET (7) NULL,
    [DateTime0334]         DATETIMEOFFSET (7) NULL,
    [DateTime0335]         DATETIMEOFFSET (7) NULL,
    [DateTime0336]         DATETIMEOFFSET (7) NULL,
    [DateTime0337]         DATETIMEOFFSET (7) NULL,
    [DateTime0338]         DATETIMEOFFSET (7) NULL,
    [DateTime0339]         DATETIMEOFFSET (7) NULL,
    [DateTime0340]         DATETIMEOFFSET (7) NULL,
    [DateTime0341]         DATETIMEOFFSET (7) NULL,
    [DateTime0342]         DATETIMEOFFSET (7) NULL,
    [DateTime0343]         DATETIMEOFFSET (7) NULL,
    [DateTime0344]         DATETIMEOFFSET (7) NULL,
    [DateTime0345]         DATETIMEOFFSET (7) NULL,
    [DateTime0346]         DATETIMEOFFSET (7) NULL,
    [DateTime0347]         DATETIMEOFFSET (7) NULL,
    [DateTime0348]         DATETIMEOFFSET (7) NULL,
    [DateTime0349]         DATETIMEOFFSET (7) NULL,
    [DateTime0350]         DATETIMEOFFSET (7) NULL,
    [Boolean0301]          BIT                NULL,
    [Boolean0302]          BIT                NULL,
    [Boolean0303]          BIT                NULL,
    [Boolean0304]          BIT                NULL,
    [Boolean0305]          BIT                NULL,
    [Boolean0306]          BIT                NULL,
    [Boolean0307]          BIT                NULL,
    [Boolean0308]          BIT                NULL,
    [Boolean0309]          BIT                NULL,
    [Boolean0310]          BIT                NULL,
    [Boolean0311]          BIT                NULL,
    [Boolean0312]          BIT                NULL,
    [Boolean0313]          BIT                NULL,
    [Boolean0314]          BIT                NULL,
    [Boolean0315]          BIT                NULL,
    [Boolean0316]          BIT                NULL,
    [Boolean0317]          BIT                NULL,
    [Boolean0318]          BIT                NULL,
    [Boolean0319]          BIT                NULL,
    [Boolean0320]          BIT                NULL,
    [Boolean0321]          BIT                NULL,
    [Boolean0322]          BIT                NULL,
    [Boolean0323]          BIT                NULL,
    [Boolean0324]          BIT                NULL,
    [Boolean0325]          BIT                NULL,
    [Boolean0326]          BIT                NULL,
    [Boolean0327]          BIT                NULL,
    [Boolean0328]          BIT                NULL,
    [Boolean0329]          BIT                NULL,
    [Boolean0330]          BIT                NULL,
    [Boolean0331]          BIT                NULL,
    [Boolean0332]          BIT                NULL,
    [Boolean0333]          BIT                NULL,
    [Boolean0334]          BIT                NULL,
    [Boolean0335]          BIT                NULL,
    [Boolean0336]          BIT                NULL,
    [Boolean0337]          BIT                NULL,
    [Boolean0338]          BIT                NULL,
    [Boolean0339]          BIT                NULL,
    [Boolean0340]          BIT                NULL,
    [Boolean0341]          BIT                NULL,
    [Boolean0342]          BIT                NULL,
    [Boolean0343]          BIT                NULL,
    [Boolean0344]          BIT                NULL,
    [Boolean0345]          BIT                NULL,
    [Boolean0346]          BIT                NULL,
    [Boolean0347]          BIT                NULL,
    [Boolean0348]          BIT                NULL,
    [Boolean0349]          BIT                NULL,
    [Boolean0350]          BIT                NULL,
    [Identity0301]         UNIQUEIDENTIFIER   NULL,
    [Identity0302]         UNIQUEIDENTIFIER   NULL,
    [Identity0303]         UNIQUEIDENTIFIER   NULL,
    [Identity0304]         UNIQUEIDENTIFIER   NULL,
    [Identity0305]         UNIQUEIDENTIFIER   NULL,
    [Identity0306]         UNIQUEIDENTIFIER   NULL,
    [Identity0307]         UNIQUEIDENTIFIER   NULL,
    [Identity0308]         UNIQUEIDENTIFIER   NULL,
    [Identity0309]         UNIQUEIDENTIFIER   NULL,
    [Identity0310]         UNIQUEIDENTIFIER   NULL,
    [Identity0311]         UNIQUEIDENTIFIER   NULL,
    [Identity0312]         UNIQUEIDENTIFIER   NULL,
    [Identity0313]         UNIQUEIDENTIFIER   NULL,
    [Identity0314]         UNIQUEIDENTIFIER   NULL,
    [Identity0315]         UNIQUEIDENTIFIER   NULL,
    [Identity0316]         UNIQUEIDENTIFIER   NULL,
    [Identity0317]         UNIQUEIDENTIFIER   NULL,
    [Identity0318]         UNIQUEIDENTIFIER   NULL,
    [Identity0319]         UNIQUEIDENTIFIER   NULL,
    [Identity0320]         UNIQUEIDENTIFIER   NULL,
    [Identity0321]         UNIQUEIDENTIFIER   NULL,
    [Identity0322]         UNIQUEIDENTIFIER   NULL,
    [Identity0323]         UNIQUEIDENTIFIER   NULL,
    [Identity0324]         UNIQUEIDENTIFIER   NULL,
    [Identity0325]         UNIQUEIDENTIFIER   NULL,
    [Identity0326]         UNIQUEIDENTIFIER   NULL,
    [Identity0327]         UNIQUEIDENTIFIER   NULL,
    [Identity0328]         UNIQUEIDENTIFIER   NULL,
    [Identity0329]         UNIQUEIDENTIFIER   NULL,
    [Identity0330]         UNIQUEIDENTIFIER   NULL,
    [Identity0331]         UNIQUEIDENTIFIER   NULL,
    [Identity0332]         UNIQUEIDENTIFIER   NULL,
    [Identity0333]         UNIQUEIDENTIFIER   NULL,
    [Identity0334]         UNIQUEIDENTIFIER   NULL,
    [Identity0335]         UNIQUEIDENTIFIER   NULL,
    [Identity0336]         UNIQUEIDENTIFIER   NULL,
    [Identity0337]         UNIQUEIDENTIFIER   NULL,
    [Identity0338]         UNIQUEIDENTIFIER   NULL,
    [Identity0339]         UNIQUEIDENTIFIER   NULL,
    [Identity0340]         UNIQUEIDENTIFIER   NULL,
    [Identity0341]         UNIQUEIDENTIFIER   NULL,
    [Identity0342]         UNIQUEIDENTIFIER   NULL,
    [Identity0343]         UNIQUEIDENTIFIER   NULL,
    [Identity0344]         UNIQUEIDENTIFIER   NULL,
    [Identity0345]         UNIQUEIDENTIFIER   NULL,
    [Identity0346]         UNIQUEIDENTIFIER   NULL,
    [Identity0347]         UNIQUEIDENTIFIER   NULL,
    [Identity0348]         UNIQUEIDENTIFIER   NULL,
    [Identity0349]         UNIQUEIDENTIFIER   NULL,
    [Identity0350]         UNIQUEIDENTIFIER   NULL
) ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_AnalyticsModel_tbl_WorkItemRevisionCustom07_WorkItemRevisionSK]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom07]([PartitionId] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_WorkItemRevisionCustom07]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom07]
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

