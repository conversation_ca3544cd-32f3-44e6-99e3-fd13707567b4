CREATE TABLE [AnalyticsModel].[tbl_WorkItemRevisionCustom01] (
    [PartitionId]          INT                NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]     BIGINT             NOT NULL,
    [WorkItemId]           INT                NOT NULL,
    [Revision]             INT                NOT NULL,
    [WorkItemRevisionSK]   INT                NOT NULL,
    [String0001]           NVARCHAR (256)     NULL,
    [String0002]           NVARCHAR (256)     NULL,
    [String0003]           NVARCHAR (256)     NULL,
    [String0004]           NVARCHAR (256)     NULL,
    [String0005]           NVARCHAR (256)     NULL,
    [String0006]           NVARCHAR (256)     NULL,
    [String0007]           NVARCHAR (256)     NULL,
    [String0008]           NVARCHAR (256)     NULL,
    [String0009]           NVARCHAR (256)     NULL,
    [String0010]           NVARCHAR (256)     NULL,
    [String0011]           NVARCHAR (256)     NULL,
    [String0012]           NVARCHAR (256)     NULL,
    [String0013]           NVARCHAR (256)     NULL,
    [String0014]           NVARCHAR (256)     NULL,
    [String0015]           NVARCHAR (256)     NULL,
    [String0016]           NVARCHAR (256)     NULL,
    [String0017]           NVARCHAR (256)     NULL,
    [String0018]           NVARCHAR (256)     NULL,
    [String0019]           NVARCHAR (256)     NULL,
    [String0020]           NVARCHAR (256)     NULL,
    [String0021]           NVARCHAR (256)     NULL,
    [String0022]           NVARCHAR (256)     NULL,
    [String0023]           NVARCHAR (256)     NULL,
    [String0024]           NVARCHAR (256)     NULL,
    [String0025]           NVARCHAR (256)     NULL,
    [String0026]           NVARCHAR (256)     NULL,
    [String0027]           NVARCHAR (256)     NULL,
    [String0028]           NVARCHAR (256)     NULL,
    [String0029]           NVARCHAR (256)     NULL,
    [String0030]           NVARCHAR (256)     NULL,
    [String0031]           NVARCHAR (256)     NULL,
    [String0032]           NVARCHAR (256)     NULL,
    [String0033]           NVARCHAR (256)     NULL,
    [String0034]           NVARCHAR (256)     NULL,
    [String0035]           NVARCHAR (256)     NULL,
    [String0036]           NVARCHAR (256)     NULL,
    [String0037]           NVARCHAR (256)     NULL,
    [String0038]           NVARCHAR (256)     NULL,
    [String0039]           NVARCHAR (256)     NULL,
    [String0040]           NVARCHAR (256)     NULL,
    [String0041]           NVARCHAR (256)     NULL,
    [String0042]           NVARCHAR (256)     NULL,
    [String0043]           NVARCHAR (256)     NULL,
    [String0044]           NVARCHAR (256)     NULL,
    [String0045]           NVARCHAR (256)     NULL,
    [String0046]           NVARCHAR (256)     NULL,
    [String0047]           NVARCHAR (256)     NULL,
    [String0048]           NVARCHAR (256)     NULL,
    [String0049]           NVARCHAR (256)     NULL,
    [String0050]           NVARCHAR (256)     NULL,
    [String0051]           NVARCHAR (256)     NULL,
    [String0052]           NVARCHAR (256)     NULL,
    [String0053]           NVARCHAR (256)     NULL,
    [String0054]           NVARCHAR (256)     NULL,
    [String0055]           NVARCHAR (256)     NULL,
    [String0056]           NVARCHAR (256)     NULL,
    [String0057]           NVARCHAR (256)     NULL,
    [String0058]           NVARCHAR (256)     NULL,
    [String0059]           NVARCHAR (256)     NULL,
    [String0060]           NVARCHAR (256)     NULL,
    [String0061]           NVARCHAR (256)     NULL,
    [String0062]           NVARCHAR (256)     NULL,
    [String0063]           NVARCHAR (256)     NULL,
    [String0064]           NVARCHAR (256)     NULL,
    [String0065]           NVARCHAR (256)     NULL,
    [String0066]           NVARCHAR (256)     NULL,
    [String0067]           NVARCHAR (256)     NULL,
    [String0068]           NVARCHAR (256)     NULL,
    [String0069]           NVARCHAR (256)     NULL,
    [String0070]           NVARCHAR (256)     NULL,
    [String0071]           NVARCHAR (256)     NULL,
    [String0072]           NVARCHAR (256)     NULL,
    [String0073]           NVARCHAR (256)     NULL,
    [String0074]           NVARCHAR (256)     NULL,
    [String0075]           NVARCHAR (256)     NULL,
    [String0076]           NVARCHAR (256)     NULL,
    [String0077]           NVARCHAR (256)     NULL,
    [String0078]           NVARCHAR (256)     NULL,
    [String0079]           NVARCHAR (256)     NULL,
    [String0080]           NVARCHAR (256)     NULL,
    [String0081]           NVARCHAR (256)     NULL,
    [String0082]           NVARCHAR (256)     NULL,
    [String0083]           NVARCHAR (256)     NULL,
    [String0084]           NVARCHAR (256)     NULL,
    [String0085]           NVARCHAR (256)     NULL,
    [String0086]           NVARCHAR (256)     NULL,
    [String0087]           NVARCHAR (256)     NULL,
    [String0088]           NVARCHAR (256)     NULL,
    [String0089]           NVARCHAR (256)     NULL,
    [String0090]           NVARCHAR (256)     NULL,
    [String0091]           NVARCHAR (256)     NULL,
    [String0092]           NVARCHAR (256)     NULL,
    [String0093]           NVARCHAR (256)     NULL,
    [String0094]           NVARCHAR (256)     NULL,
    [String0095]           NVARCHAR (256)     NULL,
    [String0096]           NVARCHAR (256)     NULL,
    [String0097]           NVARCHAR (256)     NULL,
    [String0098]           NVARCHAR (256)     NULL,
    [String0099]           NVARCHAR (256)     NULL,
    [String0100]           NVARCHAR (256)     NULL,
    [String0101]           NVARCHAR (256)     NULL,
    [String0102]           NVARCHAR (256)     NULL,
    [String0103]           NVARCHAR (256)     NULL,
    [String0104]           NVARCHAR (256)     NULL,
    [String0105]           NVARCHAR (256)     NULL,
    [String0106]           NVARCHAR (256)     NULL,
    [String0107]           NVARCHAR (256)     NULL,
    [String0108]           NVARCHAR (256)     NULL,
    [String0109]           NVARCHAR (256)     NULL,
    [String0110]           NVARCHAR (256)     NULL,
    [String0111]           NVARCHAR (256)     NULL,
    [String0112]           NVARCHAR (256)     NULL,
    [String0113]           NVARCHAR (256)     NULL,
    [String0114]           NVARCHAR (256)     NULL,
    [String0115]           NVARCHAR (256)     NULL,
    [String0116]           NVARCHAR (256)     NULL,
    [String0117]           NVARCHAR (256)     NULL,
    [String0118]           NVARCHAR (256)     NULL,
    [String0119]           NVARCHAR (256)     NULL,
    [String0120]           NVARCHAR (256)     NULL,
    [String0121]           NVARCHAR (256)     NULL,
    [String0122]           NVARCHAR (256)     NULL,
    [String0123]           NVARCHAR (256)     NULL,
    [String0124]           NVARCHAR (256)     NULL,
    [String0125]           NVARCHAR (256)     NULL,
    [String0126]           NVARCHAR (256)     NULL,
    [String0127]           NVARCHAR (256)     NULL,
    [String0128]           NVARCHAR (256)     NULL,
    [String0129]           NVARCHAR (256)     NULL,
    [String0130]           NVARCHAR (256)     NULL,
    [String0131]           NVARCHAR (256)     NULL,
    [String0132]           NVARCHAR (256)     NULL,
    [String0133]           NVARCHAR (256)     NULL,
    [String0134]           NVARCHAR (256)     NULL,
    [String0135]           NVARCHAR (256)     NULL,
    [String0136]           NVARCHAR (256)     NULL,
    [String0137]           NVARCHAR (256)     NULL,
    [String0138]           NVARCHAR (256)     NULL,
    [String0139]           NVARCHAR (256)     NULL,
    [String0140]           NVARCHAR (256)     NULL,
    [String0141]           NVARCHAR (256)     NULL,
    [String0142]           NVARCHAR (256)     NULL,
    [String0143]           NVARCHAR (256)     NULL,
    [String0144]           NVARCHAR (256)     NULL,
    [String0145]           NVARCHAR (256)     NULL,
    [String0146]           NVARCHAR (256)     NULL,
    [String0147]           NVARCHAR (256)     NULL,
    [String0148]           NVARCHAR (256)     NULL,
    [String0149]           NVARCHAR (256)     NULL,
    [String0150]           NVARCHAR (256)     NULL,
    [String0151]           NVARCHAR (256)     NULL,
    [String0152]           NVARCHAR (256)     NULL,
    [String0153]           NVARCHAR (256)     NULL,
    [String0154]           NVARCHAR (256)     NULL,
    [String0155]           NVARCHAR (256)     NULL,
    [String0156]           NVARCHAR (256)     NULL,
    [String0157]           NVARCHAR (256)     NULL,
    [String0158]           NVARCHAR (256)     NULL,
    [String0159]           NVARCHAR (256)     NULL,
    [String0160]           NVARCHAR (256)     NULL,
    [String0161]           NVARCHAR (256)     NULL,
    [String0162]           NVARCHAR (256)     NULL,
    [String0163]           NVARCHAR (256)     NULL,
    [String0164]           NVARCHAR (256)     NULL,
    [String0165]           NVARCHAR (256)     NULL,
    [String0166]           NVARCHAR (256)     NULL,
    [String0167]           NVARCHAR (256)     NULL,
    [String0168]           NVARCHAR (256)     NULL,
    [String0169]           NVARCHAR (256)     NULL,
    [String0170]           NVARCHAR (256)     NULL,
    [String0171]           NVARCHAR (256)     NULL,
    [String0172]           NVARCHAR (256)     NULL,
    [String0173]           NVARCHAR (256)     NULL,
    [String0174]           NVARCHAR (256)     NULL,
    [String0175]           NVARCHAR (256)     NULL,
    [String0176]           NVARCHAR (256)     NULL,
    [String0177]           NVARCHAR (256)     NULL,
    [String0178]           NVARCHAR (256)     NULL,
    [String0179]           NVARCHAR (256)     NULL,
    [String0180]           NVARCHAR (256)     NULL,
    [String0181]           NVARCHAR (256)     NULL,
    [String0182]           NVARCHAR (256)     NULL,
    [String0183]           NVARCHAR (256)     NULL,
    [String0184]           NVARCHAR (256)     NULL,
    [String0185]           NVARCHAR (256)     NULL,
    [String0186]           NVARCHAR (256)     NULL,
    [String0187]           NVARCHAR (256)     NULL,
    [String0188]           NVARCHAR (256)     NULL,
    [String0189]           NVARCHAR (256)     NULL,
    [String0190]           NVARCHAR (256)     NULL,
    [String0191]           NVARCHAR (256)     NULL,
    [String0192]           NVARCHAR (256)     NULL,
    [String0193]           NVARCHAR (256)     NULL,
    [String0194]           NVARCHAR (256)     NULL,
    [String0195]           NVARCHAR (256)     NULL,
    [String0196]           NVARCHAR (256)     NULL,
    [String0197]           NVARCHAR (256)     NULL,
    [String0198]           NVARCHAR (256)     NULL,
    [String0199]           NVARCHAR (256)     NULL,
    [String0200]           NVARCHAR (256)     NULL,
    [Integer0001]          BIGINT             NULL,
    [Integer0002]          BIGINT             NULL,
    [Integer0003]          BIGINT             NULL,
    [Integer0004]          BIGINT             NULL,
    [Integer0005]          BIGINT             NULL,
    [Integer0006]          BIGINT             NULL,
    [Integer0007]          BIGINT             NULL,
    [Integer0008]          BIGINT             NULL,
    [Integer0009]          BIGINT             NULL,
    [Integer0010]          BIGINT             NULL,
    [Integer0011]          BIGINT             NULL,
    [Integer0012]          BIGINT             NULL,
    [Integer0013]          BIGINT             NULL,
    [Integer0014]          BIGINT             NULL,
    [Integer0015]          BIGINT             NULL,
    [Integer0016]          BIGINT             NULL,
    [Integer0017]          BIGINT             NULL,
    [Integer0018]          BIGINT             NULL,
    [Integer0019]          BIGINT             NULL,
    [Integer0020]          BIGINT             NULL,
    [Integer0021]          BIGINT             NULL,
    [Integer0022]          BIGINT             NULL,
    [Integer0023]          BIGINT             NULL,
    [Integer0024]          BIGINT             NULL,
    [Integer0025]          BIGINT             NULL,
    [Integer0026]          BIGINT             NULL,
    [Integer0027]          BIGINT             NULL,
    [Integer0028]          BIGINT             NULL,
    [Integer0029]          BIGINT             NULL,
    [Integer0030]          BIGINT             NULL,
    [Integer0031]          BIGINT             NULL,
    [Integer0032]          BIGINT             NULL,
    [Integer0033]          BIGINT             NULL,
    [Integer0034]          BIGINT             NULL,
    [Integer0035]          BIGINT             NULL,
    [Integer0036]          BIGINT             NULL,
    [Integer0037]          BIGINT             NULL,
    [Integer0038]          BIGINT             NULL,
    [Integer0039]          BIGINT             NULL,
    [Integer0040]          BIGINT             NULL,
    [Integer0041]          BIGINT             NULL,
    [Integer0042]          BIGINT             NULL,
    [Integer0043]          BIGINT             NULL,
    [Integer0044]          BIGINT             NULL,
    [Integer0045]          BIGINT             NULL,
    [Integer0046]          BIGINT             NULL,
    [Integer0047]          BIGINT             NULL,
    [Integer0048]          BIGINT             NULL,
    [Integer0049]          BIGINT             NULL,
    [Integer0050]          BIGINT             NULL,
    [Double0001]           FLOAT (53)         NULL,
    [Double0002]           FLOAT (53)         NULL,
    [Double0003]           FLOAT (53)         NULL,
    [Double0004]           FLOAT (53)         NULL,
    [Double0005]           FLOAT (53)         NULL,
    [Double0006]           FLOAT (53)         NULL,
    [Double0007]           FLOAT (53)         NULL,
    [Double0008]           FLOAT (53)         NULL,
    [Double0009]           FLOAT (53)         NULL,
    [Double0010]           FLOAT (53)         NULL,
    [Double0011]           FLOAT (53)         NULL,
    [Double0012]           FLOAT (53)         NULL,
    [Double0013]           FLOAT (53)         NULL,
    [Double0014]           FLOAT (53)         NULL,
    [Double0015]           FLOAT (53)         NULL,
    [Double0016]           FLOAT (53)         NULL,
    [Double0017]           FLOAT (53)         NULL,
    [Double0018]           FLOAT (53)         NULL,
    [Double0019]           FLOAT (53)         NULL,
    [Double0020]           FLOAT (53)         NULL,
    [Double0021]           FLOAT (53)         NULL,
    [Double0022]           FLOAT (53)         NULL,
    [Double0023]           FLOAT (53)         NULL,
    [Double0024]           FLOAT (53)         NULL,
    [Double0025]           FLOAT (53)         NULL,
    [Double0026]           FLOAT (53)         NULL,
    [Double0027]           FLOAT (53)         NULL,
    [Double0028]           FLOAT (53)         NULL,
    [Double0029]           FLOAT (53)         NULL,
    [Double0030]           FLOAT (53)         NULL,
    [Double0031]           FLOAT (53)         NULL,
    [Double0032]           FLOAT (53)         NULL,
    [Double0033]           FLOAT (53)         NULL,
    [Double0034]           FLOAT (53)         NULL,
    [Double0035]           FLOAT (53)         NULL,
    [Double0036]           FLOAT (53)         NULL,
    [Double0037]           FLOAT (53)         NULL,
    [Double0038]           FLOAT (53)         NULL,
    [Double0039]           FLOAT (53)         NULL,
    [Double0040]           FLOAT (53)         NULL,
    [Double0041]           FLOAT (53)         NULL,
    [Double0042]           FLOAT (53)         NULL,
    [Double0043]           FLOAT (53)         NULL,
    [Double0044]           FLOAT (53)         NULL,
    [Double0045]           FLOAT (53)         NULL,
    [Double0046]           FLOAT (53)         NULL,
    [Double0047]           FLOAT (53)         NULL,
    [Double0048]           FLOAT (53)         NULL,
    [Double0049]           FLOAT (53)         NULL,
    [Double0050]           FLOAT (53)         NULL,
    [DateTime0001]         DATETIMEOFFSET (7) NULL,
    [DateTime0002]         DATETIMEOFFSET (7) NULL,
    [DateTime0003]         DATETIMEOFFSET (7) NULL,
    [DateTime0004]         DATETIMEOFFSET (7) NULL,
    [DateTime0005]         DATETIMEOFFSET (7) NULL,
    [DateTime0006]         DATETIMEOFFSET (7) NULL,
    [DateTime0007]         DATETIMEOFFSET (7) NULL,
    [DateTime0008]         DATETIMEOFFSET (7) NULL,
    [DateTime0009]         DATETIMEOFFSET (7) NULL,
    [DateTime0010]         DATETIMEOFFSET (7) NULL,
    [DateTime0011]         DATETIMEOFFSET (7) NULL,
    [DateTime0012]         DATETIMEOFFSET (7) NULL,
    [DateTime0013]         DATETIMEOFFSET (7) NULL,
    [DateTime0014]         DATETIMEOFFSET (7) NULL,
    [DateTime0015]         DATETIMEOFFSET (7) NULL,
    [DateTime0016]         DATETIMEOFFSET (7) NULL,
    [DateTime0017]         DATETIMEOFFSET (7) NULL,
    [DateTime0018]         DATETIMEOFFSET (7) NULL,
    [DateTime0019]         DATETIMEOFFSET (7) NULL,
    [DateTime0020]         DATETIMEOFFSET (7) NULL,
    [DateTime0021]         DATETIMEOFFSET (7) NULL,
    [DateTime0022]         DATETIMEOFFSET (7) NULL,
    [DateTime0023]         DATETIMEOFFSET (7) NULL,
    [DateTime0024]         DATETIMEOFFSET (7) NULL,
    [DateTime0025]         DATETIMEOFFSET (7) NULL,
    [DateTime0026]         DATETIMEOFFSET (7) NULL,
    [DateTime0027]         DATETIMEOFFSET (7) NULL,
    [DateTime0028]         DATETIMEOFFSET (7) NULL,
    [DateTime0029]         DATETIMEOFFSET (7) NULL,
    [DateTime0030]         DATETIMEOFFSET (7) NULL,
    [DateTime0031]         DATETIMEOFFSET (7) NULL,
    [DateTime0032]         DATETIMEOFFSET (7) NULL,
    [DateTime0033]         DATETIMEOFFSET (7) NULL,
    [DateTime0034]         DATETIMEOFFSET (7) NULL,
    [DateTime0035]         DATETIMEOFFSET (7) NULL,
    [DateTime0036]         DATETIMEOFFSET (7) NULL,
    [DateTime0037]         DATETIMEOFFSET (7) NULL,
    [DateTime0038]         DATETIMEOFFSET (7) NULL,
    [DateTime0039]         DATETIMEOFFSET (7) NULL,
    [DateTime0040]         DATETIMEOFFSET (7) NULL,
    [DateTime0041]         DATETIMEOFFSET (7) NULL,
    [DateTime0042]         DATETIMEOFFSET (7) NULL,
    [DateTime0043]         DATETIMEOFFSET (7) NULL,
    [DateTime0044]         DATETIMEOFFSET (7) NULL,
    [DateTime0045]         DATETIMEOFFSET (7) NULL,
    [DateTime0046]         DATETIMEOFFSET (7) NULL,
    [DateTime0047]         DATETIMEOFFSET (7) NULL,
    [DateTime0048]         DATETIMEOFFSET (7) NULL,
    [DateTime0049]         DATETIMEOFFSET (7) NULL,
    [DateTime0050]         DATETIMEOFFSET (7) NULL,
    [Boolean0001]          BIT                NULL,
    [Boolean0002]          BIT                NULL,
    [Boolean0003]          BIT                NULL,
    [Boolean0004]          BIT                NULL,
    [Boolean0005]          BIT                NULL,
    [Boolean0006]          BIT                NULL,
    [Boolean0007]          BIT                NULL,
    [Boolean0008]          BIT                NULL,
    [Boolean0009]          BIT                NULL,
    [Boolean0010]          BIT                NULL,
    [Boolean0011]          BIT                NULL,
    [Boolean0012]          BIT                NULL,
    [Boolean0013]          BIT                NULL,
    [Boolean0014]          BIT                NULL,
    [Boolean0015]          BIT                NULL,
    [Boolean0016]          BIT                NULL,
    [Boolean0017]          BIT                NULL,
    [Boolean0018]          BIT                NULL,
    [Boolean0019]          BIT                NULL,
    [Boolean0020]          BIT                NULL,
    [Boolean0021]          BIT                NULL,
    [Boolean0022]          BIT                NULL,
    [Boolean0023]          BIT                NULL,
    [Boolean0024]          BIT                NULL,
    [Boolean0025]          BIT                NULL,
    [Boolean0026]          BIT                NULL,
    [Boolean0027]          BIT                NULL,
    [Boolean0028]          BIT                NULL,
    [Boolean0029]          BIT                NULL,
    [Boolean0030]          BIT                NULL,
    [Boolean0031]          BIT                NULL,
    [Boolean0032]          BIT                NULL,
    [Boolean0033]          BIT                NULL,
    [Boolean0034]          BIT                NULL,
    [Boolean0035]          BIT                NULL,
    [Boolean0036]          BIT                NULL,
    [Boolean0037]          BIT                NULL,
    [Boolean0038]          BIT                NULL,
    [Boolean0039]          BIT                NULL,
    [Boolean0040]          BIT                NULL,
    [Boolean0041]          BIT                NULL,
    [Boolean0042]          BIT                NULL,
    [Boolean0043]          BIT                NULL,
    [Boolean0044]          BIT                NULL,
    [Boolean0045]          BIT                NULL,
    [Boolean0046]          BIT                NULL,
    [Boolean0047]          BIT                NULL,
    [Boolean0048]          BIT                NULL,
    [Boolean0049]          BIT                NULL,
    [Boolean0050]          BIT                NULL,
    [Identity0001]         UNIQUEIDENTIFIER   NULL,
    [Identity0002]         UNIQUEIDENTIFIER   NULL,
    [Identity0003]         UNIQUEIDENTIFIER   NULL,
    [Identity0004]         UNIQUEIDENTIFIER   NULL,
    [Identity0005]         UNIQUEIDENTIFIER   NULL,
    [Identity0006]         UNIQUEIDENTIFIER   NULL,
    [Identity0007]         UNIQUEIDENTIFIER   NULL,
    [Identity0008]         UNIQUEIDENTIFIER   NULL,
    [Identity0009]         UNIQUEIDENTIFIER   NULL,
    [Identity0010]         UNIQUEIDENTIFIER   NULL,
    [Identity0011]         UNIQUEIDENTIFIER   NULL,
    [Identity0012]         UNIQUEIDENTIFIER   NULL,
    [Identity0013]         UNIQUEIDENTIFIER   NULL,
    [Identity0014]         UNIQUEIDENTIFIER   NULL,
    [Identity0015]         UNIQUEIDENTIFIER   NULL,
    [Identity0016]         UNIQUEIDENTIFIER   NULL,
    [Identity0017]         UNIQUEIDENTIFIER   NULL,
    [Identity0018]         UNIQUEIDENTIFIER   NULL,
    [Identity0019]         UNIQUEIDENTIFIER   NULL,
    [Identity0020]         UNIQUEIDENTIFIER   NULL,
    [Identity0021]         UNIQUEIDENTIFIER   NULL,
    [Identity0022]         UNIQUEIDENTIFIER   NULL,
    [Identity0023]         UNIQUEIDENTIFIER   NULL,
    [Identity0024]         UNIQUEIDENTIFIER   NULL,
    [Identity0025]         UNIQUEIDENTIFIER   NULL,
    [Identity0026]         UNIQUEIDENTIFIER   NULL,
    [Identity0027]         UNIQUEIDENTIFIER   NULL,
    [Identity0028]         UNIQUEIDENTIFIER   NULL,
    [Identity0029]         UNIQUEIDENTIFIER   NULL,
    [Identity0030]         UNIQUEIDENTIFIER   NULL,
    [Identity0031]         UNIQUEIDENTIFIER   NULL,
    [Identity0032]         UNIQUEIDENTIFIER   NULL,
    [Identity0033]         UNIQUEIDENTIFIER   NULL,
    [Identity0034]         UNIQUEIDENTIFIER   NULL,
    [Identity0035]         UNIQUEIDENTIFIER   NULL,
    [Identity0036]         UNIQUEIDENTIFIER   NULL,
    [Identity0037]         UNIQUEIDENTIFIER   NULL,
    [Identity0038]         UNIQUEIDENTIFIER   NULL,
    [Identity0039]         UNIQUEIDENTIFIER   NULL,
    [Identity0040]         UNIQUEIDENTIFIER   NULL,
    [Identity0041]         UNIQUEIDENTIFIER   NULL,
    [Identity0042]         UNIQUEIDENTIFIER   NULL,
    [Identity0043]         UNIQUEIDENTIFIER   NULL,
    [Identity0044]         UNIQUEIDENTIFIER   NULL,
    [Identity0045]         UNIQUEIDENTIFIER   NULL,
    [Identity0046]         UNIQUEIDENTIFIER   NULL,
    [Identity0047]         UNIQUEIDENTIFIER   NULL,
    [Identity0048]         UNIQUEIDENTIFIER   NULL,
    [Identity0049]         UNIQUEIDENTIFIER   NULL,
    [Identity0050]         UNIQUEIDENTIFIER   NULL
) ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_AnalyticsModel_tbl_WorkItemRevisionCustom01_WorkItemRevisionSK]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom01]([PartitionId] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_WorkItemRevisionCustom01]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom01]
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

