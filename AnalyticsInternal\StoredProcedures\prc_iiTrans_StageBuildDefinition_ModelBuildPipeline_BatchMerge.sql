/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: BDA5AAEB5EE539D46D8850AD525AE53C7E6E5901
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageBuildDefinition_ModelBuildPipeline_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    ;WITH Src AS
    (
        SELECT  PartitionId,
                ProjectGuid AS ProjectSK,
                DefinitionId AS BuildPipelineId,
                DefinitionName AS BuildPipelineName,
                DefinitionVersion AS BuildPipelineVersion,
                ProcessType AS BuildPipelineProcessType,
                Deleted AS IsDeleted
        FROM    AnalyticsStage.tbl_BuildDefinition
        WHERE   PartitionId = @PartitionId
                AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    ),
    Tgt AS
    (
        SELECT  PartitionId,
                AnalyticsCreatedDate,
                AnalyticsUpdatedDate,
                AnalyticsBatchId,
                ProjectSK,
                BuildPipelineId,
                BuildPipelineName,
                BuildPipelineVersion,
                BuildPipelineProcessType,
                IsDeleted
        FROM   AnalyticsModel.tbl_BuildPipeline
        WHERE  PartitionId = @partitionId
    )
    MERGE TOP (@batchSizeMax) Tgt AS t
    USING Src AS s
    ON (t.PartitionId = s.PartitionId
        AND t.BuildPipelineId = s.BuildPipelineId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.PartitionId
        , s.ProjectSK
        , s.BuildPipelineId
        , s.BuildPipelineName
        , s.BuildPipelineVersion
        , s.BuildPipelineProcessType
        , s.IsDeleted
        INTERSECT
        SELECT
        t.PartitionId
        , t.ProjectSK
        , t.BuildPipelineId
        , t.BuildPipelineName
        , t.BuildPipelineVersion
        , t.BuildPipelineProcessType
        , t.IsDeleted
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt
        , AnalyticsBatchId = @batchId
        , ProjectSK = s.ProjectSK
        , BuildPipelineName = s.BuildPipelineName
        , BuildPipelineVersion = s.BuildPipelineVersion
        , BuildPipelineProcessType = s.BuildPipelineProcessType
        , IsDeleted = s.IsDeleted
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId
        , AnalyticsBatchId
        , AnalyticsCreatedDate
        , AnalyticsUpdatedDate
        , ProjectSK
        , BuildPipelineId
        , BuildPipelineName
        , BuildPipelineVersion
        , BuildPipelineProcessType
        , IsDeleted
        )
    VALUES (
        s.PartitionId
        , @batchId
        , @batchDt
        , @batchDt
        , s.ProjectSK
        , s.BuildPipelineId
        , s.BuildPipelineName
        , s.BuildPipelineVersion
        , s.BuildPipelineProcessType
        , s.IsDeleted
        )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    RETURN 0
END

GO

