/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: E8AC4FFD96F15BC56C346F0182E0FD0270F1ABB5
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTeamSetting_ModelTeam_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    ;WITH Src AS
    (
        SELECT
            t.PartitionId,
            ProjectGuid AS ProjectSK,
            TeamGuid AS TeamId,
            TeamName
        FROM AnalyticsStage.tbl_TeamSetting t
        WHERE t.PartitionId = @partitionId
            AND t.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd -- -- table uses change detection - ignore BatchId
    )
    MERGE TOP (@batchSizeMax) AnalyticsModel.tbl_Team AS t
    USING Src AS s
    ON (t.PartitionId = s.PartitionId AND t.TeamId = s.TeamId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.TeamName,
        s.ProjectSK
        INTERSECT
        SELECT
        t.TeamName,
        t.ProjectSK
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt
        , AnalyticsBatchId = @batchId
        , TeamName = s.TeamName
        , ProjectSK = s.ProjectSK
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId
        , AnalyticsBatchId
        , AnalyticsCreatedDate
        , AnalyticsUpdatedDate
        , TeamId
        , TeamName
        , ProjectSK
    )
    VALUES (
        s.PartitionId
        , @batchId
        , @batchDt
        , @batchDt
        , s.TeamId
        , s.TeamName
        , s.ProjectSK
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = 0

    RETURN 0
END

GO

