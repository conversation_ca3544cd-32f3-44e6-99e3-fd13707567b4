/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 419E7F7E523CA8DE094393FFF064BFEF941B002C
--------------------------------------------------------------------
-- Replace content of tbl_TransformDefintion table with provided transform definitions
-- and updates transform predicate table
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_InstallTransformDefinitions
    @definitions AnalyticsInternal.typ_TransformDefinition4 READONLY
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @installDate DATETIME = GETUTCDATE()

    BEGIN TRANSACTION

    MERGE   AnalyticsInternal.tbl_TransformDefinition t
    USING   @definitions AS s
    ON      t.TriggerTableName = s.TriggerTableName
            AND t.TriggerOperation = s.TriggerOperation
            AND t.TargetTableName = s.TargetTableName
            AND t.TargetOperation = s.TargetOperation
            AND t.SProcName = s.SProcName
    WHEN MATCHED AND NOT EXISTS (
            SELECT  s.OperationScope,
                    s.TransformOrder,
                    s.TransformPriority,
                    s.IntervalMinutes,
                    s.SProcVersion,
                    s.FeatureFlagged
            INTERSECT
            SELECT  t.OperationScope,
                    t.TransformOrder,
                    t.TransformPriority,
                    t.IntervalMinutes,
                    t.SProcVersion,
                    t.FeatureFlagged
        )
    THEN
    UPDATE
    SET     t.OperationScope = s.OperationScope,
            t.TransformOrder = s.TransformOrder,
            t.TransformPriority = s.TransformPriority,
            t.IntervalMinutes = s.IntervalMinutes,
            t.SProcVersion = s.SProcVersion,
            t.FeatureFlagged = s.FeatureFlagged,
            t.InstallDate = @installDate
    WHEN NOT MATCHED BY TARGET THEN
    INSERT  (
            TriggerTableName,
            TriggerOperation,
            TargetTableName,
            TargetOperation,
            SProcName,
            OperationScope,
            TransformOrder,
            TransformPriority,
            IntervalMinutes,
            SProcVersion,
            Hold,
            FeatureFlagged,
            InstallDate
            )
    VALUES  (
            s.TriggerTableName,
            s.TriggerOperation,
            s.TargetTableName,
            s.TargetOperation,
            s.SProcName,
            s.OperationScope,
            s.TransformOrder,
            s.TransformPriority,
            s.IntervalMinutes,
            s.SProcVersion,
            0, -- hold
            s.FeatureFlagged,
            @installDate
            )
    WHEN NOT MATCHED BY SOURCE THEN
    DELETE;

    -- validate TransformPriority does not conflict with TransformOrder
    DECLARE @opMap TABLE (Op VARCHAR(10) PRIMARY KEY, Mask SMALLINT)
    INSERT  @opMap (Op, Mask)
    VALUES  ('insert',  0x01),
            ('update',  0x02),
            ('delete',  0x04),
            ('merge',   0x03),
            ('replace', 0x07)

    IF EXISTS
    (
        SELECT  *
        FROM    AnalyticsInternal.tbl_TransformDefinition d
        JOIN    AnalyticsInternal.tbl_TransformDefinition p
        ON      p.OperationScope = 'batch'
                AND p.TriggerTableName = d.TriggerTableName
                AND p.TransformOrder < d.TransformOrder
        LEFT JOIN @opMap dm
        ON      dm.Op = d.TriggerOperation
        LEFT JOIN @opMap pm
        ON      pm.Op = p.TriggerOperation
        WHERE   d.OperationScope = 'batch'
                AND ISNULL(dm.Mask, 0) & ISNULL(pm.Mask, 0) > 0
                AND p.TransformPriority < d.TransformPriority
    )
    BEGIN
        ROLLBACK TRANSACTION
        SET @status = 1670001
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, 'Failed to install TransformDefintions: TransformPriority conflicts with TransformOrder for at least one definition.')
        RETURN @status
    END

    -- recompute predicates
    DELETE  AnalyticsInternal.tbl_TransformDefinitionPredicate

    ;WITH Predicate AS
    (
        -- predicates by peer transforms to trigger tables
        SELECT  d.TargetTableName,
                d.TriggerTableName,
                d.SProcName,
                ppeer.TriggerTableName AS PredTriggerTableName,
                ppeer.SProcName AS PredSprocName
        FROM    AnalyticsInternal.tbl_TransformDefinition d
        JOIN    AnalyticsInternal.tbl_TransformDefinition p
        ON      p.OperationScope = 'batch'
                AND p.TargetTableName = d.TriggerTableName
        JOIN    AnalyticsInternal.tbl_TransformDefinition ppeer
        ON      ppeer.OperationScope = 'batch'
                AND ppeer.TriggerTableName = p.TriggerTableName
                AND ppeer.TransformPriority >= d.TransformPriority
        WHERE   d.OperationScope = 'batch'
    )
    , RollupPredicate AS
    (
        -- flattened
        SELECT  TargetTableName,
                TriggerTableName,
                SProcName,
                PredTriggerTableName,
                PredSprocName
        FROM    Predicate
        UNION ALL
        SELECT  p.TargetTableName,
                p.TriggerTableName,
                p.SProcName,
                rp.PredTriggerTableName,
                rp.PredSprocName
        FROM    Predicate p
        JOIN    RollupPredicate rp
        ON      p.PredTriggerTableName = rp.TriggerTableName
                AND p.PredSprocName = rp.SprocName
    )
    INSERT  AnalyticsInternal.tbl_TransformDefinitionPredicate
            (
            TriggerTableName,
            SprocName,
            PredicateTriggerTableName,
            PredicateSprocName
            )
    SELECT  TriggerTableName,
            SprocName,
            PredTriggerTableName,
            PredSprocName
    FROM    RollupPredicate

    -- predicates by TransformOrder
    INSERT  AnalyticsInternal.tbl_TransformDefinitionPredicate
            (
            TriggerTableName,
            SprocName,
            PredicateTriggerTableName,
            PredicateSprocName
            )
    SELECT  d.TriggerTableName,
            d.SProcName,
            p.TriggerTableName AS PredTriggerTableName,
            p.SProcName AS PredSprocName
    FROM    AnalyticsInternal.tbl_TransformDefinition d
    JOIN    AnalyticsInternal.tbl_TransformDefinition p
    ON      p.OperationScope = 'batch'
            AND p.TriggerTableName = d.TriggerTableName
            AND p.TransformOrder < d.TransformOrder
    LEFT JOIN @opMap dm
    ON      dm.Op = d.TriggerOperation
    LEFT JOIN @opMap pm
    ON      pm.Op = p.TriggerOperation
    WHERE   d.OperationScope = 'batch'
            AND ISNULL(dm.Mask, 0) & ISNULL(pm.Mask, 0) > 0
            AND p.TransformPriority >= d.TransformPriority

    -- DeDupe
    ;WITH AllPredicates AS
    (
        SELECT  *, ROW_NUMBER() OVER (PARTITION BY TriggerTableName, SprocName, PredicateTriggerTableName, PredicateSprocName ORDER BY TriggerTableName) AS RowNum
        FROM    AnalyticsInternal.tbl_TransformDefinitionPredicate
    )
    DELETE  AllPredicates
    WHERE   RowNum > 1

    COMMIT TRANSACTION

    RETURN 0
END

GO

