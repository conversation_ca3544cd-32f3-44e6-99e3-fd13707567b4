/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FE1FA0BC56247EEEADC6B341FADE473C0D3994C3
CREATE PROCEDURE AnalyticsInternal.prc_GetProcessFields
    @partitionId    INT,
    @processId UNIQUEIDENTIFIER,
    @includeDeleted BIT = 0
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    SELECT
        Name
        , ReferenceName
        , SourceFieldName
        , Description
        , FieldType
        , IsSystem
        , IsDeleted
        , ModelTableName
        , ModelColumnName
        , IsHistoryEnabled
        , IsPerson
        , SourceKeyFieldName
    FROM
    (
        SELECT
        *
        -- In case of collection level we assume that for each ReferenceName we have only one FieldType
        , ROW_NUMBER() OVER(PARTITION BY ReferenceName, FieldType ORDER BY ProcessId) AS N
        FROM AnalyticsInternal.tbl_ProcessField
        WHERE PartitionId = @partitionId
            AND (ProcessId = @processId OR @processId IS NULL)
            AND (IsDeleted = 0 OR @includeDeleted = 1)
    ) T
    WHERE N = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
END

GO

