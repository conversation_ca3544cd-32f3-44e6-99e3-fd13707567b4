/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 3EC272BEC995869CE13F354EC596B7EC324A224C
CREATE PROCEDURE AnalyticsInternal.prc_iMergeWorkItemCore
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerOperation NVARCHAR(10),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 5000)
    DECLARE @batchSizeLarge INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeLarge'), 150)
    DECLARE @batchSizeAllMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeAllMax'), 500000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)
    SET @batchSizeAllMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeAllMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @triggers TABLE (System_Id INT NOT NULL PRIMARY KEY, [Count] INT NOT NULL)
    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)
    DECLARE @prelimEndStateData BIGINT

    IF (@triggerOperation = 'insert')
    BEGIN
        IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
        BEGIN
            INSERT  @triggers
            SELECT  System_Id, COUNT(*)
            FROM
            (
                SELECT  TOP (@batchSizeMax) System_Id
                FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
                WHERE   wi.PartitionId = @partitionId
                        AND wi.AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND wi.System_Id >= @workItemIdStart
                        AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
                ORDER BY System_Id
            ) T
            GROUP BY System_Id
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @prelimEndStateData = ISNULL((SELECT MAX(System_Id) FROM @triggers), @stateData)
            SET @complete = 0
            SET @endState = IIF((SELECT SUM([Count]) FROM @triggers) >= @batchSizeMax, 'dense', '')
        END
        ELSE
        BEGIN
            INSERT  @triggers
            SELECT  System_Id, COUNT(*)
            FROM
            (
                SELECT  TOP (@batchSizeMax) System_Id
                FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdCreated), FORCESEEK)
                WHERE   wi.PartitionId = @partitionId
                        AND wi.AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND wi.System_Id >= @workItemIdStart
                ORDER BY System_Id
            ) T
            GROUP BY System_Id
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

            SET @prelimEndStateData = ISNULL((SELECT MAX(System_Id) FROM @triggers), @stateData)
            SET @complete = IIF((SELECT SUM([Count]) FROM @triggers) >= @batchSizeMax, 0, 1)
            SET @endState = IIF(@complete = 0 AND @prelimEndStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
        END
    END
    ELSE -- TableOperation_Update
    BEGIN
        IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
        BEGIN
            INSERT  @triggers
            SELECT  System_Id, COUNT(*)
            FROM
            (
                SELECT  TOP (@batchSizeMax) System_Id
                FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
                WHERE   wi.PartitionId = @partitionId
                        AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND wi.AnalyticsBatchIdChanged <> ISNULL(wi.AnalyticsBatchIdCreated, 0)
                        AND wi.System_Id >= @workItemIdStart
                        AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
                ORDER BY System_Id
            ) T
            GROUP BY System_Id
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @prelimEndStateData = ISNULL((SELECT MAX(System_Id) FROM @triggers), @stateData)
            SET @complete = 0
            SET @endState = IIF((SELECT SUM([Count]) FROM @triggers) >= @batchSizeMax, 'dense', '')
        END
        ELSE
        BEGIN
            INSERT  @triggers
            SELECT  System_Id, COUNT(*)
            FROM
            (
                SELECT  TOP (@batchSizeMax) System_Id
                FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
                WHERE   wi.PartitionId = @partitionId
                        AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                        AND wi.AnalyticsBatchIdChanged <> ISNULL(wi.AnalyticsBatchIdCreated, 0)
                        AND wi.System_Id >= @workItemIdStart
                ORDER BY System_Id
            ) T
            GROUP BY System_Id
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

            SET @prelimEndStateData = ISNULL((SELECT MAX(System_Id) FROM @triggers), @stateData)
            SET @complete = IIF((SELECT SUM([Count]) FROM @triggers) >= @batchSizeMax, 0, 1)
            SET @endState = IIF(@complete = 0 AND @prelimEndStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
        END
    END

    -- ensure we don't attempt to handle too many rows in #WorkItemRevsAll
    DECLARE @triggerWorkItemId AnalyticsInternal.typ_WorkItemId
    INSERT  @triggerWorkItemId
    SELECT  System_Id
    FROM
    (
        SELECT  TOP (@batchSizeAllMax) t.System_Id
        FROM    @triggers t
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision r
        ON      r.PartitionId = @partitionId
                AND r.System_Id = t.System_Id
        ORDER BY t.System_Id
    ) T
    GROUP BY System_Id
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @endStateData = (SELECT MAX(System_Id) FROM @triggerWorkItemId)
    IF (@endStateData < @prelimEndStateData)
    BEGIN
        SET @complete = 0
    END

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    CREATE TABLE #WorkItemStateCategoryChange
    (
        System_Id INT NOT NULL,
        System_Rev INT NOT NULL,
        System_ChangedDate DATETIMEOFFSET NULL,
        StateCategory NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        PRIMARY KEY CLUSTERED (System_Id, System_Rev)
    )

    INSERT #WorkItemStateCategoryChange
    SELECT System_Id, System_Rev, System_ChangedDate, StateCategory
    FROM
        (
        SELECT  wi.System_Id, System_Rev, System_ChangedDate, StateCategory, LAG(StateCategory) OVER (PARTITION BY wi.PartitionId, wi.System_Id ORDER BY wi.System_Rev) AS PreviousStateCategory
        FROM    @triggerWorkItemId tid
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision wi ON wi.System_Id = tid.System_Id
        LEFT JOIN AnalyticsInternal.tbl_WorkItemTypeState scm
        ON      scm.PartitionId = wi.PartitionId
                AND scm.ProjectSK = wi.System_ProjectGuid
                AND scm.WorkItemType = wi.System_WorkItemType
                AND scm.[State] = wi.System_State
        WHERE   wi.PartitionId = @partitionId
        ) core
    WHERE   ISNULL(StateCategory, '') <> ISNULL(PreviousStateCategory, '') OR System_Rev = 1
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

    CREATE TABLE #WorkItemRevsAll
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        WorkItemRevisionSK INT NULL,
        ExistingHistory BIT NOT NULL,
        ExistingCurrent BIT NOT NULL,
        Updating BIT NOT NULL,
        PrevIsDeleted BIT NULL,
        PrevChangedDate DATETIMEOFFSET NULL,
        PrevRevisedDate DATETIMEOFFSET NULL,
        PrevInProgressDate DATETIMEOFFSET NULL,
        PrevCompletedDate DATETIMEOFFSET NULL,
        IsCurrent BIT NOT NULL,
        IsDeleted BIT NOT NULL,
        ChangedDateRaw DATETIMEOFFSET NULL,
        RevisedDateRaw DATETIMEOFFSET NULL,
        InProgressDateRaw DATETIMEOFFSET NULL, -- don't translate TZ here - do it only on updates/compare to targets to avoid unnessary work
        CompletedDateRaw DATETIMEOFFSET NULL, -- don't translate TZ here - do it only on updates/compare to targets to avoid unnessary work
        IsLastRevisionOfDay BIT NULL,
        IsLastRevisionOfPeriod INT NULL,
        PrevCurrentProjectSK UNIQUEIDENTIFIER NULL,
        CurrentProjectSK UNIQUEIDENTIFIER NULL,
        PRIMARY KEY CLUSTERED (WorkItemId, Revision)
    )
    WITH (DATA_COMPRESSION=ROW)

    INSERT #WorkItemRevsAll (WorkItemId, Revision, WorkItemRevisionSK, ExistingHistory, ExistingCurrent, Updating, PrevIsDeleted, PrevChangedDate, PrevRevisedDate, PrevInProgressDate, PrevCompletedDate, IsCurrent, IsDeleted, ChangedDateRaw, RevisedDateRaw, InProgressDateRaw, CompletedDateRaw, PrevCurrentProjectSK, CurrentProjectSK)
    SELECT
        t.WorkItemId,
        t.Revision,
        t.WorkItemRevisionSK,
        1 AS ExistingHistory,
        0 AS ExistingCurrent,
        0 AS Updating,
        t.IsDeleted,
        t.ChangedDate,
        t.RevisedDate,
        t.InProgressDate,
        t.CompletedDate,
        0 AS IsCurrent,
        t.IsDeleted,
        t.ChangedDate,
        t.RevisedDate,
        t.InProgressDate,
        t.CompletedDate,
        t.CurrentProjectSK,
        t.CurrentProjectSK
    FROM AnalyticsModel.tbl_WorkItemHistory t
    JOIN @triggerWorkItemId tid ON tid.System_Id = t.WorkItemId
    WHERE t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT #WorkItemRevsAll (WorkItemId, Revision, WorkItemRevisionSK, ExistingHistory, ExistingCurrent, Updating, PrevIsDeleted, PrevChangedDate, PrevRevisedDate, PrevInProgressDate, PrevCompletedDate, IsCurrent, IsDeleted, ChangedDateRaw, RevisedDateRaw, InProgressDateRaw, CompletedDateRaw, PrevCurrentProjectSK, CurrentProjectSK)
    SELECT
        t.WorkItemId,
        t.Revision,
        t.WorkItemRevisionSK,
        0 AS ExistingHistory,
        1 AS ExistingCurrent,
        0 AS Updating,
        t.IsDeleted,
        t.ChangedDate,
        '9999-01-01' AS RevisedDate,
        t.InProgressDate,
        t.CompletedDate,
        1 AS IsCurrent,
        t.IsDeleted,
        t.ChangedDate,
        NULL AS RevisedDate,
        t.InProgressDate,
        t.CompletedDate,
        t.ProjectSK,
        t.ProjectSK
    FROM AnalyticsModel.tbl_WorkItem t
    JOIN @triggerWorkItemId tid ON tid.System_Id = t.WorkItemId
    WHERE t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- merge new revisions from stage, and mark Updating on existing revisions
    MERGE #WorkItemRevsAll t
    USING
        (
        SELECT  s.*
        FROM    @triggerWorkItemId tid
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision s
        ON      tid.System_Id = s.System_Id
        WHERE   s.PartitionId = @partitionId
                AND s.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        ) s
    ON (t.WorkItemId = s.System_Id AND t.Revision = s.System_Rev)
    WHEN MATCHED THEN
    UPDATE SET
        t.Updating = 1,
        t.IsDeleted = ISNULL(s.System_IsDeleted, 0),
        t.ChangedDateRaw = s.System_ChangedDate,
        t.CurrentProjectSK = s.System_ProjectGuid
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (WorkItemId, Revision, ExistingHistory, ExistingCurrent, Updating, IsCurrent, IsDeleted, ChangedDateRaw, CurrentProjectSK)
    VALUES (
        s.System_Id,
        s.System_Rev,
        0, -- ExistingHistory
        0, -- ExistingCurrent,
        1, -- Updating
        1, -- IsCurrent, to be updated in subsequent step
        ISNULL(s.System_IsDeleted, 0),
        s.System_ChangedDate,
        s.System_ProjectGuid
        )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), LOOP JOIN);

    CREATE TABLE #WorkItemRevs
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        WorkItemRevisionSK INT NULL,
        Watermark INT NULL,
        -- Fundamental attributes
        Title NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        WorkItemType NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        -- Temporal attributes
        ChangedDate DATETIMEOFFSET NULL,
        CreatedDate DATETIMEOFFSET NULL,
        -- Major filtering attrbutes
        [State] NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        -- Rest of System attributes
        Reason NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        -- Build attributes
        FoundIn NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        IntegrationBuild NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        -- VSTS Common attributes
        ActivatedDate DATETIMEOFFSET NULL,
        Activity NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        BacklogPriority FLOAT NULL,
        BusinessValue INT NULL,
        ClosedDate DATETIMEOFFSET NULL,
        Discipline NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        Issue NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL, --WH
        Priority INT NULL,
        Rating NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL, --WH
        ResolvedDate DATETIMEOFFSET NULL,
        ResolvedReason NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        Risk NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        Severity NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        StackRank FLOAT NULL,
        TimeCriticality FLOAT NULL,
        Triage NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        ValueArea NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        -- Scheduling
        DueDate DATETIMEOFFSET NULL,
        FinishDate DATETIMEOFFSET NULL,
        StartDate DATETIMEOFFSET NULL,
        TargetDate DATETIMEOFFSET NULL,
        -- CMMI attributes
        Blocked NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        Committed NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        Escalate NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        FoundInEnvironment NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        HowFound NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        Probability INT NULL,
        RequirementType NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        RequiresReview NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        RequiresTest NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        RootCause NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        SubjectMatterExpert1 NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        SubjectMatterExpert2 NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        SubjectMatterExpert3 NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        TargetResolveDate DATETIMEOFFSET NULL,
        TaskType NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        UserAcceptanceTest NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        ProjectSK UNIQUEIDENTIFIER NULL,
        IsDeleted BIT NOT NULL,
        RevisedDate DATETIMEOFFSET NULL,
        IsCurrent BIT NOT NULL,
        AutomatedTestId NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        AutomatedTestName NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        AutomatedTestStorage NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        AutomatedTestType NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        AutomationStatus NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        DateSK INT,
        AreaSK UNIQUEIDENTIFIER,
        IterationSK UNIQUEIDENTIFIER,
        CompletedWork FLOAT,
        Effort FLOAT,
        OriginalEstimate FLOAT,
        RemainingWork FLOAT,
        Size FLOAT,
        StoryPoints FLOAT,
        CreatedDateSK INT,
        RevisedDateSK INT,
        ActivatedDateSK INT,
        ClosedDateSK INT,
        ResolvedDateSK INT,
        AssignedToUserSK UNIQUEIDENTIFIER,
        ChangedByUserSK UNIQUEIDENTIFIER,
        CreatedByUserSK UNIQUEIDENTIFIER,
        ActivatedByUserSK UNIQUEIDENTIFIER,
        ClosedByUserSK UNIQUEIDENTIFIER,
        ResolvedByUserSK UNIQUEIDENTIFIER,
        ParentWorkItemId INT NULL,
        IsLastRevisionOfDay BIT,
        IsLastRevisionOfPeriod INT,
        TeamFieldSK INT NULL,
        TagNames NVARCHAR(1024) COLLATE DATABASE_DEFAULT NULL,
        StateCategory NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        InProgressDate              DATETIMEOFFSET      NULL,
        InProgressDateSK            INT                 NULL,
        CompletedDate               DATETIMEOFFSET      NULL,
        CompletedDateSK             INT                 NULL,
        LeadTimeDays                FLOAT               NULL,
        CycleTimeDays               FLOAT               NULL,
        AuthorizedDate              DATETIMEOFFSET      NULL,
        StateChangeDate             DATETIMEOFFSET      NULL,
        StateChangeDateSK           INT                 NULL,
        CurrentProjectSK            UNIQUEIDENTIFIER    NULL,
        CommentCount                INT                 NULL,
    )
    WITH (DATA_COMPRESSION=ROW)

    DECLARE @triggerWorkItemIdMin INT = (SELECT MIN(System_Id) FROM @triggerWorkItemId)
    DECLARE @triggerWorkItemIdMax INT = @endStateData

    CREATE TABLE #RevsWithTag
    (
        WorkItemId      INT                                         NOT NULL,
        Revision        INT                                         NOT NULL,
        TagName         NVARCHAR(400)   COLLATE DATABASE_DEFAULT    NULL,
        INDEX CL_RevsWithTag CLUSTERED (WorkItemId, Revision)
    )

    CREATE TABLE #RevsWithConcatTags
    (
        WorkItemId      INT                                         NOT NULL,
        Revision        INT                                         NOT NULL,
        TagNames        NVARCHAR(1024)  COLLATE DATABASE_DEFAULT    NULL,
        PRIMARY KEY CLUSTERED (WorkItemId, Revision)
    )

    ;WITH RevTags AS
    (
        SELECT      r.PartitionId,
                    r.System_ProjectGuid,
                    r.System_Id,
                    r.System_Rev,
                    TagIds.x.value('(TagId/text())[1]','nvarchar(256)') AS TagId
        FROM        #WorkItemRevsAll wia
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision r
        ON          wia.WorkItemId = r.System_Id
                    AND wia.Revision = r.System_Rev
                    AND (wia.Updating = 1 OR wia.ExistingCurrent = 1) -- inserts/updates can be from inserted/changed stage rows or from previous current revision moving to WorkItemHistory
        CROSS APPLY Tags.nodes('//Item') AS TagIds(x)
        WHERE       r.PartitionId = @partitionId
                    AND r.System_Id BETWEEN @triggerWorkItemIdMin AND @triggerWorkItemIdMax
                    AND r.Tags IS NOT NULL
    )
    INSERT INTO #RevsWithTag(WorkItemId, Revision, TagName)
    SELECT      r.System_Id,
                r.System_Rev,
                t.TagName
    FROM        RevTags r
    JOIN        AnalyticsModel.tbl_Tag t
    ON          r.PartitionId = t.PartitionId
                AND r.TagId = t.TagId
                AND r.System_ProjectGuid = t.ProjectSK
                AND t.TagName IS NOT NULL
    OPTION      (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER);

    INSERT INTO #RevsWithConcatTags(WorkItemId, Revision, TagNames)
    SELECT      r.WorkItemId,
                r.Revision,
                STUFF((
                SELECT '; ' + CAST(TagName AS NVARCHAR(400))
                FROM #RevsWithTag
                WHERE (WorkItemId = r.WorkItemId AND Revision = r.Revision)
                ORDER BY TagName
                FOR XML PATH(''),TYPE).value('(./text())[1]','NVARCHAR(1026)')
                ,1,2,'')
    FROM #RevsWithTag r
    GROUP BY r.WorkItemId, r.Revision

    -- TODO - this statement likely needs optimization for retransform scenario
    -- as all revision for the work item will need to be inserted, not selected revisions
    INSERT #WorkItemRevs
    SELECT
        wi.System_Id                                       AS WorkItemId,
        wi.System_Rev                                      AS Revision,
        NULL                                               AS WorkItemRevisionSK, -- set in merge from All
        wi.System_Watermark                                AS Watermark,
        wi.System_Title                                    AS Title,
        wi.System_WorkItemType                             AS WorkItemType,
        wi.System_ChangedDate AT TIME ZONE @timeZone       AS ChangedDate,
        wi.System_CreatedDate AT TIME ZONE @timeZone       AS CreatedDate,
        wi.System_State                                    AS [State],
        wi.System_Reason                                   AS Reason,
        wi.Microsoft_VSTS_Build_FoundIn                    AS FoundIn,
        wi.Microsoft_VSTS_Build_IntegrationBuild           AS IntegrationBuild,
        wi.Microsoft_VSTS_Common_ActivatedDate AT TIME ZONE @timeZone AS ActivatedDate,
        wi.Microsoft_VSTS_Common_Activity                  AS Activity,
        wi.Microsoft_VSTS_Common_BacklogPriority           AS BacklogPriority,
        wi.Microsoft_VSTS_Common_BusinessValue             AS BusinessValue,
        wi.Microsoft_VSTS_Common_ClosedDate AT TIME ZONE @timeZone AS ClosedDate,
        wi.Microsoft_VSTS_Common_Discipline                AS Discipline,
        wi.Microsoft_VSTS_Common_Issue                     AS Issue,
        wi.Microsoft_VSTS_Common_Priority                  AS Priority,
        wi.Microsoft_VSTS_Common_Rating                    AS Rating,
        wi.Microsoft_VSTS_Common_ResolvedDate AT TIME ZONE @timeZone AS ResolvedDate,
        wi.Microsoft_VSTS_Common_ResolvedReason            AS ResolvedReason,
        wi.Microsoft_VSTS_Common_Risk                      AS Risk,
        wi.Microsoft_VSTS_Common_Severity                  AS Severity,
        wi.Microsoft_VSTS_Common_StackRank                 AS StackRank,
        wi.Microsoft_VSTS_Common_TimeCriticality           AS TimeCriticality,
        wi.Microsoft_VSTS_Common_Triage                    AS Triage,
        wi.Microsoft_VSTS_Common_ValueArea                 AS ValueArea,
        wi.Microsoft_VSTS_Scheduling_DueDate               AS DueDate,
        wi.Microsoft_VSTS_Scheduling_FinishDate            AS FinishDate,
        wi.Microsoft_VSTS_Scheduling_StartDate             AS StartDate,
        wi.Microsoft_VSTS_Scheduling_TargetDate            AS TargetDate,
        wi.Microsoft_VSTS_CMMI_Blocked                     AS Blocked,
        wi.Microsoft_VSTS_CMMI_Committed                   AS Committed,
        wi.Microsoft_VSTS_CMMI_Escalate                    AS Escalate,
        wi.Microsoft_VSTS_CMMI_FoundInEnvironment          AS FoundInEnvironment,
        wi.Microsoft_VSTS_CMMI_HowFound                    AS HowFound,
        wi.Microsoft_VSTS_CMMI_Probability                 AS Probability,
        wi.Microsoft_VSTS_CMMI_RequirementType             AS RequirementType,
        wi.Microsoft_VSTS_CMMI_RequiresReview              AS RequiresReview,
        wi.Microsoft_VSTS_CMMI_RequiresTest                AS RequiresTest,
        wi.Microsoft_VSTS_CMMI_RootCause                   AS RootCause,
        wi.Microsoft_VSTS_CMMI_SubjectMatterExpert1        AS SubjectMatterExpert1,
        wi.Microsoft_VSTS_CMMI_SubjectMatterExpert2        AS SubjectMatterExpert2,
        wi.Microsoft_VSTS_CMMI_SubjectMatterExpert3        AS SubjectMatterExpert3,
        wi.Microsoft_VSTS_CMMI_TargetResolveDate           AS TargetResolveDate,
        wi.Microsoft_VSTS_CMMI_TaskType                    AS TaskType,
        wi.Microsoft_VSTS_CMMI_UserAcceptanceTest          AS UserAcceptanceTest,
        wi.System_ProjectGuid                              AS ProjectSK,
        ISNULL(wi.System_IsDeleted, 0)                     AS IsDeleted,
        '9999-01-01'                                AS RevisedDate, -- set in subsequent update
        1                                                  AS IsCurrent, -- set in subsequent update
        wi.Microsoft_VSTS_TCM_AutomatedTestId              AS AutomatedTestId,
        wi.Microsoft_VSTS_TCM_AutomatedTestName            AS AutomatedTestName,
        wi.Microsoft_VSTS_TCM_AutomatedTestStorage         AS AutomatedTestStorage,
        wi.Microsoft_VSTS_TCM_AutomatedTestType            AS AutomatedTestType,
        wi.Microsoft_VSTS_TCM_AutomationStatus             AS AutomationStatus,
        AnalyticsInternal.func_GenDateSK(wi.System_ChangedDate AT TIME ZONE @timeZone) AS DateSK,
        wi.System_AreaGuid AS AreaSK,
        wi.System_IterationGuid AS IterationSK,
        wi.Microsoft_VSTS_Scheduling_CompletedWork,
        wi.Microsoft_VSTS_Scheduling_Effort,
        wi.Microsoft_VSTS_Scheduling_OriginalEstimate,
        wi.Microsoft_VSTS_Scheduling_RemainingWork,
        wi.Microsoft_VSTS_Scheduling_Size,
        wi.Microsoft_VSTS_Scheduling_StoryPoints,
        AnalyticsInternal.func_GenDateSK(wi.System_CreatedDate AT TIME ZONE @timeZone) AS CreatedDateSK,
        NULL AS RevisedDateSK,
        AnalyticsInternal.func_GenDateSK(wi.Microsoft_VSTS_Common_ActivatedDate AT TIME ZONE @timeZone) AS ActivatedDateSK,
        AnalyticsInternal.func_GenDateSK(wi.Microsoft_VSTS_Common_ClosedDate AT TIME ZONE @timeZone) AS ClosedDateSK,
        AnalyticsInternal.func_GenDateSK(wi.Microsoft_VSTS_Common_ResolvedDate AT TIME ZONE @timeZone) AS ResolvedDateSK,
        AnalyticsInternal.func_GetUserSKFromWITPerson(wi.System_AssignedTo) AS AssignedToUserSK,
        AnalyticsInternal.func_GetUserSKFromWITPerson(wi.System_ChangedBy) AS ChangedByUserSK,
        AnalyticsInternal.func_GetUserSKFromWITPerson(wi.System_CreatedBy) AS CreatedByUserSK,
        AnalyticsInternal.func_GetUserSKFromWITPerson(wi.Microsoft_VSTS_Common_ActivatedBy) AS ActivatedByUserSK,
        AnalyticsInternal.func_GetUserSKFromWITPerson(wi.Microsoft_VSTS_Common_ClosedBy) AS ClosedByUserSK,
        AnalyticsInternal.func_GetUserSKFromWITPerson(wi.Microsoft_VSTS_Common_ResolvedBy) AS ResolvedByUserSK,
        NULL AS ParentWorkItemId, -- set in subsequent update
        1 AS IsLastRevisionOfDay, -- set in subsequent update
        0 AS IsLastRevisionOfPeriod, -- set in subsequent update
        ISNULL(tf1.TeamFieldSK, tf2.TeamFieldSK) AS TeamFieldSK,
        tags.TagNames AS TagNames,
        scm.StateCategory,
        NULL AS InProgressDate,
        NULL AS InProgressDateSK,
        NULL AS CompletedDate,
        NULL AS CompletedDateSK,
        NULL AS LeadTimeDays,
        NULL AS CycleTimeDays,
        wi.System_AuthorizedDate AT TIME ZONE @timeZone AS AuthorizedDate,
        wi.Microsoft_VSTS_Common_StateChangeDate AT TIME ZONE @timeZone AS StateChangeDate,
        AnalyticsInternal.func_GenDateSK(wi.Microsoft_VSTS_Common_StateChangeDate AT TIME ZONE @timeZone) AS StateChangeDateSK,
        wi.System_ProjectGuid AS CurrentProjectSK, --will be updated later
        wi.System_CommentCount AS CommentCount
    FROM    #WorkItemRevsAll wia
    JOIN    AnalyticsStage.tbl_WorkItemRevision wi
    ON      wia.WorkItemId = wi.System_Id
            AND wia.Revision = wi.System_Rev
            AND (wia.Updating = 1 OR wia.ExistingCurrent = 1) -- inserts/updates can be from inserted/changed stage rows or from previous current revision moving to WorkItemHistory
    LEFT LOOP JOIN AnalyticsInternal.tbl_WorkItemTypeState scm
    ON      scm.PartitionId = wi.PartitionId
            AND scm.ProjectSK = wi.System_ProjectGuid
            AND scm.WorkItemType = wi.System_WorkItemType
            AND scm.[State] = wi.System_State
    LEFT LOOP JOIN #RevsWithConcatTags tags
    ON      tags.WorkItemId = wi.System_Id
            AND tags.Revision = wi.System_Rev
    LEFT LOOP JOIN AnalyticsModel.tbl_Project p
    ON      p.PartitionId = wi.PartitionId
            AND p.ProjectId = wi.System_ProjectGuid
    LEFT LOOP JOIN AnalyticsModel.tbl_TeamField tf1 -- team field from area
    ON      tf1.PartitionId = p.PartitionId
            AND tf1.ProjectId = p.ProjectId
            AND tf1.TeamFieldReferenceName = ISNULL(p.TeamFieldReferenceName, 'System.AreaPath')
            AND tf1.TeamFieldReferenceName = 'System.AreaPath'
            AND tf1.AreaId = wi.System_AreaGuid
    LEFT LOOP JOIN AnalyticsInternal.tbl_Fields f
    ON      f.PartitionId = p.PartitionId
            AND f.TableName = 'WorkItemRevision'
            AND f.FieldName = p.TeamFieldSourceFieldName
            AND p.TeamFieldReferenceName <> 'System.AreaPath'
    LEFT LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x
    ON      x.PartitionId = f.PartitionId
            AND x.FieldSK = f.FieldSK
            AND x.System_Id = wi.System_Id
            AND x.System_Rev = wi.System_Rev
    LEFT LOOP JOIN AnalyticsModel.tbl_TeamField tf2 -- team field from custom fields
    ON      tf2.PartitionId = p.PartitionId
            AND tf2.ProjectId = p.ProjectId
            AND tf2.TeamFieldReferenceName = ISNULL(p.TeamFieldReferenceName, 'System.AreaPath')
            AND tf2.TeamFieldReferenceName <> 'System.AreaPath'
            AND tf2.TeamFieldValue = x.ValueString
    WHERE   wi.PartitionId = @partitionId
            AND wi.System_Id BETWEEN @triggerWorkItemIdMin AND @triggerWorkItemIdMax
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- set inprogress and completed on revs downstream from changed revs
    UPDATE  r
    SET     InProgressDateRaw = stateDate.InProgressDate,
            CompletedDateRaw = IIF(stateDate.CompletedDate < stateDate.ReworkDate, NULL, stateDate.CompletedDate)
    FROM    (SELECT WorkItemId, MIN(Revision) Revision FROM #WorkItemRevs GROUP BY WorkItemId) earliestChanges
    INNER LOOP JOIN #WorkItemRevsAll r
    ON      r.WorkItemId = earliestChanges.WorkItemId
            AND r.Revision >= earliestChanges.Revision
    OUTER APPLY
    (
        SELECT  MIN(IIF(ISNULL(StateCategory, 'Proposed') <> 'Proposed', System_ChangedDate, NULL)) AS InProgressDate, -- treat NULL as Proposed
                MAX(IIF(StateCategory = 'Complete' OR StateCategory = 'Completed', System_ChangedDate, NULL)) AS CompletedDate,
                MAX(IIF(StateCategory <> 'Complete' AND StateCategory <> 'Completed', System_ChangedDate, NULL)) AS ReworkDate -- ignoring NULLs
        FROM    #WorkItemStateCategoryChange
        WHERE   System_Id = r.WorkItemId
                AND System_Rev <= r.Revision
    ) stateDate
    OPTION (FORCE ORDER)

    -- correct ChangedDates if needed
    WHILE (1=1)
    BEGIN
        ;WITH AllRevs AS
        (
            SELECT  *,
                    LAG(ChangedDateRaw) OVER (PARTITION BY WorkItemId ORDER BY Revision) AS PreviousChangedDateRaw
            FROM    #WorkItemRevsAll
        )
        UPDATE  t
        SET     ChangedDateRaw = PreviousChangedDateRaw
        FROM    AllRevs t
        WHERE   ChangedDateRaw < PreviousChangedDateRaw

        IF (@@ROWCOUNT = 0)
            BREAK
    END

    -- set RevisedDateRaw and ChangedDateRaw to local values
    ;WITH AllRevs AS
    (
        SELECT  *,
                LEAD(ChangedDateRaw) OVER (PARTITION BY WorkItemId ORDER BY Revision) AS NewRevisedDateRaw
        FROM    #WorkItemRevsAll
    )
    UPDATE  t
    SET     ChangedDateRaw = ChangedDateRaw AT TIME ZONE @timeZone,
            RevisedDateRaw = NewRevisedDateRaw AT TIME ZONE @timeZone
    FROM    AllRevs t

    -- set IsLastRevisionOfDay, IsLastRevisionOfPeriod, IsDeleted, CurrentProjectSK and IsCurrent on all revs
    ;WITH AllRevs AS
    (
        SELECT  *,
                CAST(ChangedDateRaw AS DATE) ChangedDay,
                CAST(RevisedDateRaw AS DATE) RevisedDay
        FROM    #WorkItemRevsAll
    )
    UPDATE  t
    SET     IsLastRevisionOfDay = IIF(RevisedDay = ChangedDay, 0, 1),
            --flattened AnalyticsInternal.func_GetIsLastRevisionOfPeriod
            IsLastRevisionOfPeriod = IIF(RevisedDay IS NULL, 2047,
                  IIF(DATEDIFF(DAY, ChangedDay, RevisedDay) > 0, 1, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -0, ChangedDay), DATEADD(DAY, -0, RevisedDay)) > 0, 128, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -1, ChangedDay), DATEADD(DAY, -1, RevisedDay)) > 0, 2, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -2, ChangedDay), DATEADD(DAY, -2, RevisedDay)) > 0, 4, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -3, ChangedDay), DATEADD(DAY, -3, RevisedDay)) > 0, 8, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -4, ChangedDay), DATEADD(DAY, -4, RevisedDay)) > 0, 16, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -5, ChangedDay), DATEADD(DAY, -5, RevisedDay)) > 0, 32, 0)
                | IIF(DATEDIFF(WEEK, DATEADD(DAY, -6, ChangedDay), DATEADD(DAY, -6, RevisedDay)) > 0, 64, 0)
                | IIF(DATEDIFF(MONTH,   ChangedDay, RevisedDay) > 0, 256, 0)
                | IIF(DATEDIFF(QUARTER, ChangedDay, RevisedDay) > 0, 512, 0)
                | IIF(DATEDIFF(YEAR,    ChangedDay, RevisedDay) > 0, 1024, 0)
                ),
            IsDeleted = wilast.IsDeleted,
            CurrentProjectSK = wilast.CurrentProjectSK,
            IsCurrent = IIF(t.Revision = wilast.Revision, 1, 0)
    FROM    AllRevs t
    CROSS APPLY
    (
        SELECT  TOP 1 wiall.Revision, wiall.IsDeleted, wiall.CurrentProjectSK
        FROM    #WorkItemRevsAll wiall
        WHERE   wiall.WorkItemId = t.WorkItemId
        ORDER BY wiall.Revision DESC
    ) wilast

    -- refresh #WorkItemRevs table with latest from #WorkItemRevsAll
    UPDATE  t
    SET     t.WorkItemRevisionSK = s.WorkItemRevisionSK,
            t.IsDeleted = s.IsDeleted,
            t.IsCurrent = s.IsCurrent,
            t.ChangedDate = s.ChangedDateRaw, -- ChangedDateRaw has correct TZ
            t.DateSK = AnalyticsInternal.func_GenDateSK(s.ChangedDateRaw), -- ChangedDateRaw has correct TZ
            t.RevisedDate = ISNULL(s.RevisedDateRaw, '9999-01-01'), -- RevisedDateRaw has correct TZ
            t.RevisedDateSK = AnalyticsInternal.func_GenDateSK(ISNULL(s.RevisedDateRaw, '9999-01-01')), -- RevisedDateRaw has correct TZ
            t.InProgressDate = s.InProgressDateRaw  AT TIME ZONE @timeZone,
            t.InProgressDateSK = AnalyticsInternal.func_GenDateSK(s.InProgressDateRaw AT TIME ZONE @timeZone),
            t.CompletedDate = s.CompletedDateRaw  AT TIME ZONE @timeZone,
            t.CompletedDateSK = AnalyticsInternal.func_GenDateSK(s.CompletedDateRaw AT TIME ZONE @timeZone),
            t.LeadTimeDays = IIF(t.CreatedDate > s.CompletedDateRaw, 0.0, DATEDIFF_BIG(second, t.CreatedDate, s.CompletedDateRaw) / 86400.0),
            t.CycleTimeDays = IIF(s.InProgressDateRaw > s.CompletedDateRaw, 0.0, DATEDIFF_BIG(second, s.InProgressDateRaw, s.CompletedDateRaw) / 86400.0),
            t.IsLastRevisionOfDay = s.IsLastRevisionOfDay,
            t.IsLastRevisionOfPeriod = s.IsLastRevisionOfPeriod,
            t.CurrentProjectSK = s.CurrentProjectSK
    FROM    #WorkItemRevs t
    JOIN    #WorkItemRevsAll s
    ON      s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision

    -- next assign SK to new rows
    UPDATE  wi
    SET     WorkItemRevisionSK = wisk.WorkItemRevisionSK
    FROM    #WorkItemRevs wi
    JOIN    AnalyticsInternal.tbl_WorkItemRevisionReserved wisk
    ON      wisk.PartitionId = @partitionId
            AND wisk.WorkItemId = wi.WorkItemId
            AND wisk.Revision = wi.Revision
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE @toInsertCount INT
    SELECT @toInsertCount = COUNT(*)
    FROM #WorkItemRevs
    WHERE WorkItemRevisionSK IS NULL

    IF (@toInsertCount > 0)
    BEGIN
        -- should not be here unless InternalWorkItemRevisionReserved transform failed
        -- keeping this logic here for robustness
        DECLARE @newWorkItemRevisionSK INT
        EXEC @status = prc_iCounterGetNext @partitionId = @partitionId, @counterName = 'AnalyticsWorkItemRevisionSKCounter', @countToReserve = @toInsertCount, @firstIdToUse = @newWorkItemRevisionSK OUTPUT
        IF (@status <> 0)
        BEGIN
            SET @tfError = dbo.func_GetMessage(1670005); RAISERROR(@tfError, 16, -1, @procedureName, 'AnalyticsWorkItemRevisionSKCounter', @status)
            RETURN 1670005
        END

        ;WITH ToInsert AS
        (
            SELECT  *, @newWorkItemRevisionSK + (ROW_NUMBER() OVER (ORDER BY ChangedDate) - 1) AS NewWorkItemRevisionSK
            FROM    #WorkItemRevs
            WHERE   WorkItemRevisionSK IS NULL
        )
        UPDATE  t
        SET     WorkItemRevisionSK = NewWorkItemRevisionSK
        FROM    ToInsert t
    END

    -- set parent
    UPDATE  wi
    SET     ParentWorkItemId = l.SourceWorkItemId
    FROM    #WorkItemRevs wi
    JOIN    AnalyticsModel.tbl_WorkItemLinkHistory AS l
    ON      l.PartitionId = @partitionId
            AND l.TargetWorkItemId = wi.WorkItemId
            -- This predicate assumes that there exists at most one hierarchy link at any point in time.
            AND ISNULL(wi.RevisedDate, '9999-01-01') > l.CreatedDate
            AND ISNULL(wi.RevisedDate, '9999-01-01') <= l.DeletedDate
            AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE @updatingCount INT = (SELECT COUNT(*) FROM #WorkItemRevsAll WHERE Updating = 1)

    SET @updatedCount = 0
    SET @insertedCount = 0

    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    -- diff to WorkItemHistory
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    CREATE TABLE #DiffRevsHistory
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        Watermark INT NULL,
        SrcRowHash VARBINARY(16) NULL,
        TgtRowHash VARBINARY(16) NULL
    )

    CREATE TABLE #WorkItemRevsAllChangedHistory
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        IsDeleted BIT NOT NULL,
        ChangedDate DATETIMEOFFSET NULL,
        RevisedDate DATETIMEOFFSET NULL,
        InProgressDateRaw DATETIMEOFFSET NULL, -- don't translate TZ here - do it only on updates/compare to targets to avoid unnessary work
        CompletedDateRaw DATETIMEOFFSET NULL, -- don't translate TZ here - do it only on updates/compare to targets to avoid unnessary work
        IsLastRevisionOfDay BIT NULL,
        IsLastRevisionOfPeriod INT NULL,
        CurrentProjectSK UNIQUEIDENTIFIER NULL,
        PRIMARY KEY CLUSTERED (WorkItemId, Revision)
    )

    -- CCI optimization - gather existing values from target into DiffRevs temp table for comparison with source
    -- Comparing values on a join to CCI is slower that pulling them an comparing in temp tables
    IF (@updatingCount > @batchSizeLarge) -- larger update - force hash join as we expect many rows
    BEGIN
        -- first, just grab the watermarks - the most likely column to change
        -- this optimization is because CCI is much faster for narrower queries,
        -- and for the fact that updates during incremental processing are likely to change the watermark
        INSERT #DiffRevsHistory
        (
            WorkItemId,
            Revision,
            Watermark
        )
        SELECT
            t.WorkItemId,
            t.Revision,
            t.Watermark
        FROM #WorkItemRevs AS s
        JOIN #WorkItemRevsAll sx  -- attempte to reduce rows joined to large model table
        ON sx.WorkItemId = s.WorkItemId
            AND sx.Revision = s.Revision
            AND sx.ExistingHistory = 1
        INNER HASH JOIN AnalyticsModel.tbl_WorkItemHistory AS t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory)) -- force hash join for reprocess
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        WHERE s.IsCurrent = 0
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        UPDATE  d
        SET     TgtRowHash = HASHBYTES('MD5', (
                    SELECT  t.Watermark,
                            t.Title,
                            t.WorkItemType,
                            t.ChangedDate,
                            t.CreatedDate,
                            t.[State],
                            t.Reason,
                            t.FoundIn,
                            t.IntegrationBuild,
                            t.ActivatedDate,
                            t.Activity,
                            t.BacklogPriority,
                            t.BusinessValue,
                            t.ClosedDate,
                            t.Discipline,
                            t.Issue,
                            t.Priority,
                            t.Rating,
                            t.ResolvedDate,
                            t.ResolvedReason,
                            t.Risk,
                            t.Severity,
                            t.StackRank,
                            t.TimeCriticality,
                            t.Triage,
                            t.ValueArea,
                            t.DueDate,
                            t.FinishDate,
                            t.StartDate,
                            t.TargetDate,
                            t.Blocked,
                            t.Committed,
                            t.Escalate,
                            t.FoundInEnvironment,
                            t.HowFound,
                            t.Probability,
                            t.RequirementType,
                            t.RequiresReview,
                            t.RequiresTest,
                            t.RootCause,
                            t.SubjectMatterExpert1,
                            t.SubjectMatterExpert2,
                            t.SubjectMatterExpert3,
                            t.TargetResolveDate,
                            t.TaskType,
                            t.UserAcceptanceTest,
                            t.IsDeleted,
                            t.RevisedDate,
                            t.AutomatedTestId,
                            t.AutomatedTestName,
                            t.AutomatedTestStorage,
                            t.AutomatedTestType,
                            t.AutomationStatus,
                            t.ProjectSK,
                            t.DateSK,
                            t.AreaSK,
                            t.IterationSK,
                            t.CompletedWork,
                            t.Effort,
                            t.OriginalEstimate,
                            t.RemainingWork,
                            t.Size,
                            t.StoryPoints,
                            t.RevisedDateSK,
                            t.CreatedDateSK,
                            t.ActivatedDateSK,
                            t.ClosedDateSK,
                            t.ResolvedDateSK,
                            t.AssignedToUserSK,
                            t.ChangedByUserSK,
                            t.CreatedByUserSK,
                            t.ActivatedByUserSK,
                            t.ClosedByUserSK,
                            t.ResolvedByUserSK,
                            t.ParentWorkItemId,
                            CAST(t.ChangedDate AS DATETIME2) ChangedDateLocal, -- check for offset changes
                            CAST(t.RevisedDate AS DATETIME2) RevisedDateLocal, -- check for offset changes
                            CAST(t.CreatedDate AS DATETIME2) CreatedDateLocal, -- check for offset changes
                            CAST(t.ActivatedDate AS DATETIME2) ActivatedDateLocal, -- check for offset changes
                            CAST(t.ClosedDate AS DATETIME2) ClosedDateLocal, -- check for offset changes
                            CAST(t.ResolvedDate AS DATETIME2) ResolvedDateLocal, -- check for offset changes
                            t.IsLastRevisionOfDay,
                            t.IsLastRevisionOfPeriod,
                            t.TeamFieldSK,
                            t.TagNames,
                            t.StateCategory,
                            t.InProgressDate,
                            CAST(t.InProgressDate AS DATETIME2) InProgressDateLocal, -- check for offset changes
                            t.InProgressDateSK,
                            t.CompletedDate,
                            CAST(t.CompletedDate AS DATETIME2) CompletedDateLocal, -- check for offset changes
                            t.CompletedDateSK,
                            t.LeadTimeDays,
                            t.CycleTimeDays,
                            t.AuthorizedDate,
                            t.StateChangeDate,
                            t.StateChangeDateSK,
                            t.CurrentProjectSK,
                            t.CommentCount
                            FOR XML RAW))
        FROM #DiffRevsHistory d
        JOIN #WorkItemRevs AS s
            ON d.WorkItemId = s.WorkItemId
            AND d.Revision = s.Revision
        INNER HASH JOIN AnalyticsModel.tbl_WorkItemHistory AS t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory)) -- force hash join for reprocess
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        WHERE d.Watermark = s.Watermark
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE -- incremental
    BEGIN
        -- first, just grab the watermarks - the most likely column to change
        -- this optimization is because CCI is much faster for narrower queries,
        -- and for the fact that updates during incremental processing are likely to change the watermark
        -- using force order to ensure that CCI will not be used as outer part of the join
        INSERT #DiffRevsHistory
        (
            WorkItemId,
            Revision,
            Watermark
        )
        SELECT
            t.WorkItemId,
            t.Revision,
            t.Watermark
        FROM #WorkItemRevs AS s
        JOIN #WorkItemRevsAll sx  -- attempte to reduce rows joined to large model table
        ON sx.WorkItemId = s.WorkItemId
            AND sx.Revision = s.Revision
            AND sx.ExistingHistory = 1
        INNER JOIN AnalyticsModel.tbl_WorkItemHistory AS t
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        WHERE s.IsCurrent = 0
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

        -- now grab other columns, for those revs where the watermark matches
        UPDATE  d
        SET     TgtRowHash = HASHBYTES('MD5', (
                    SELECT  t.Watermark,
                            t.Title,
                            t.WorkItemType,
                            t.ChangedDate,
                            t.CreatedDate,
                            t.[State],
                            t.Reason,
                            t.FoundIn,
                            t.IntegrationBuild,
                            t.ActivatedDate,
                            t.Activity,
                            t.BacklogPriority,
                            t.BusinessValue,
                            t.ClosedDate,
                            t.Discipline,
                            t.Issue,
                            t.Priority,
                            t.Rating,
                            t.ResolvedDate,
                            t.ResolvedReason,
                            t.Risk,
                            t.Severity,
                            t.StackRank,
                            t.TimeCriticality,
                            t.Triage,
                            t.ValueArea,
                            t.DueDate,
                            t.FinishDate,
                            t.StartDate,
                            t.TargetDate,
                            t.Blocked,
                            t.Committed,
                            t.Escalate,
                            t.FoundInEnvironment,
                            t.HowFound,
                            t.Probability,
                            t.RequirementType,
                            t.RequiresReview,
                            t.RequiresTest,
                            t.RootCause,
                            t.SubjectMatterExpert1,
                            t.SubjectMatterExpert2,
                            t.SubjectMatterExpert3,
                            t.TargetResolveDate,
                            t.TaskType,
                            t.UserAcceptanceTest,
                            t.IsDeleted,
                            t.RevisedDate,
                            t.AutomatedTestId,
                            t.AutomatedTestName,
                            t.AutomatedTestStorage,
                            t.AutomatedTestType,
                            t.AutomationStatus,
                            t.ProjectSK,
                            t.DateSK,
                            t.AreaSK,
                            t.IterationSK,
                            t.CompletedWork,
                            t.Effort,
                            t.OriginalEstimate,
                            t.RemainingWork,
                            t.Size,
                            t.StoryPoints,
                            t.RevisedDateSK,
                            t.CreatedDateSK,
                            t.ActivatedDateSK,
                            t.ClosedDateSK,
                            t.ResolvedDateSK,
                            t.AssignedToUserSK,
                            t.ChangedByUserSK,
                            t.CreatedByUserSK,
                            t.ActivatedByUserSK,
                            t.ClosedByUserSK,
                            t.ResolvedByUserSK,
                            t.ParentWorkItemId,
                            CAST(t.ChangedDate AS DATETIME2) ChangedDateLocal, -- check for offset changes
                            CAST(t.RevisedDate AS DATETIME2) RevisedDateLocal, -- check for offset changes
                            CAST(t.CreatedDate AS DATETIME2) CreatedDateLocal, -- check for offset changes
                            CAST(t.ActivatedDate AS DATETIME2) ActivatedDateLocal, -- check for offset changes
                            CAST(t.ClosedDate AS DATETIME2) ClosedDateLocal, -- check for offset changes
                            CAST(t.ResolvedDate AS DATETIME2) ResolvedDateLocal, -- check for offset changes
                            t.IsLastRevisionOfDay,
                            t.IsLastRevisionOfPeriod,
                            t.TeamFieldSK,
                            t.TagNames,
                            t.StateCategory,
                            t.InProgressDate,
                            CAST(t.InProgressDate AS DATETIME2) InProgressDateLocal, -- check for offset changes
                            t.InProgressDateSK,
                            t.CompletedDate,
                            CAST(t.CompletedDate AS DATETIME2) CompletedDateLocal, -- check for offset changes
                            t.CompletedDateSK,
                            t.LeadTimeDays,
                            t.CycleTimeDays,
                            t.AuthorizedDate,
                            t.StateChangeDate,
                            t.StateChangeDateSK,
                            t.CurrentProjectSK,
                            t.CommentCount
                            FOR XML RAW))
        FROM #DiffRevsHistory d
        JOIN #WorkItemRevs AS s
            ON d.WorkItemId = s.WorkItemId
            AND d.Revision = s.Revision
        INNER JOIN AnalyticsModel.tbl_WorkItemHistory AS t
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        WHERE d.Watermark = s.Watermark
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    UPDATE  d
    SET     SrcRowHash = HASHBYTES('MD5', (
                SELECT  s.Watermark,
                        s.Title,
                        s.WorkItemType,
                        s.ChangedDate,
                        s.CreatedDate,
                        s.[State],
                        s.Reason,
                        s.FoundIn,
                        s.IntegrationBuild,
                        s.ActivatedDate,
                        s.Activity,
                        s.BacklogPriority,
                        s.BusinessValue,
                        s.ClosedDate,
                        s.Discipline,
                        s.Issue,
                        s.Priority,
                        s.Rating,
                        s.ResolvedDate,
                        s.ResolvedReason,
                        s.Risk,
                        s.Severity,
                        s.StackRank,
                        s.TimeCriticality,
                        s.Triage,
                        s.ValueArea,
                        s.DueDate,
                        s.FinishDate,
                        s.StartDate,
                        s.TargetDate,
                        s.Blocked,
                        s.Committed,
                        s.Escalate,
                        s.FoundInEnvironment,
                        s.HowFound,
                        s.Probability,
                        s.RequirementType,
                        s.RequiresReview,
                        s.RequiresTest,
                        s.RootCause,
                        s.SubjectMatterExpert1,
                        s.SubjectMatterExpert2,
                        s.SubjectMatterExpert3,
                        s.TargetResolveDate,
                        s.TaskType,
                        s.UserAcceptanceTest,
                        s.IsDeleted,
                        s.RevisedDate,
                        s.AutomatedTestId,
                        s.AutomatedTestName,
                        s.AutomatedTestStorage,
                        s.AutomatedTestType,
                        s.AutomationStatus,
                        s.ProjectSK,
                        s.DateSK,
                        s.AreaSK,
                        s.IterationSK,
                        s.CompletedWork,
                        s.Effort,
                        s.OriginalEstimate,
                        s.RemainingWork,
                        s.Size,
                        s.StoryPoints,
                        s.RevisedDateSK,
                        s.CreatedDateSK,
                        s.ActivatedDateSK,
                        s.ClosedDateSK,
                        s.ResolvedDateSK,
                        s.AssignedToUserSK,
                        s.ChangedByUserSK,
                        s.CreatedByUserSK,
                        s.ActivatedByUserSK,
                        s.ClosedByUserSK,
                        s.ResolvedByUserSK,
                        s.ParentWorkItemId,
                        CAST(s.ChangedDate AS DATETIME2) ChangedDateLocal, -- check for offset changes
                        CAST(s.RevisedDate AS DATETIME2) RevisedDateLocal, -- check for offset changes
                        CAST(s.CreatedDate AS DATETIME2) CreatedDateLocal, -- check for offset changes
                        CAST(s.ActivatedDate AS DATETIME2) ActivatedDateLocal, -- check for offset changes
                        CAST(s.ClosedDate AS DATETIME2) ClosedDateLocal, -- check for offset changes
                        CAST(s.ResolvedDate AS DATETIME2) ResolvedDateLocal, -- check for offset changes
                        s.IsLastRevisionOfDay,
                        s.IsLastRevisionOfPeriod,
                        s.TeamFieldSK,
                        s.TagNames,
                        s.StateCategory,
                        s.InProgressDate,
                        CAST(s.InProgressDate AS DATETIME2) InProgressDateLocal, -- check for offset changes
                        s.InProgressDateSK,
                        s.CompletedDate,
                        CAST(s.CompletedDate AS DATETIME2) CompletedDateLocal, -- check for offset changes
                        s.CompletedDateSK,
                        s.LeadTimeDays,
                        s.CycleTimeDays,
                        s.AuthorizedDate,
                        s.StateChangeDate,
                        s.StateChangeDateSK,
                        s.CurrentProjectSK,
                        s.CommentCount
                        FOR XML RAW))
    FROM #DiffRevsHistory d
    JOIN #WorkItemRevs AS s
    ON s.WorkItemId = d.WorkItemId
        AND s.Revision = d.Revision

    DELETE t
    FROM #DiffRevsHistory t
    WHERE SrcRowHash = TgtRowHash

    -- double check and update IsCurrent flags for all revisions
    -- update work items IsDelete with the latest IsDeleted
    INSERT #WorkItemRevsAllChangedHistory
    SELECT  WorkItemId,
            Revision,
            IsDeleted,
            ChangedDateRaw,
            ISNULL(RevisedDateRaw, '9999-01-01'),
            InProgressDateRaw,
            CompletedDateRaw,
            IsLastRevisionOfDay,
            IsLastRevisionOfPeriod,
            CurrentProjectSK
    FROM #WorkItemRevsAll AS s
    WHERE s.ExistingHistory = 1 -- important - prev fields are not set for newly inserted rows - these rows for this handled via DiffRevs update
        AND NOT
        (
            IsDeleted = PrevIsDeleted
            AND ((CurrentProjectSK IS NULL AND PrevCurrentProjectSK IS NULL) OR CurrentProjectSK = PrevCurrentProjectSK)
            AND (PrevChangedDate IS NULL OR ChangedDateRaw = PrevChangedDate)
            AND ((RevisedDateRaw IS NULL AND PrevRevisedDate IS NULL) OR ISNULL(RevisedDateRaw, '9999-01-01') = PrevRevisedDate)
            AND ((InProgressDateRaw IS NULL AND PrevInProgressDate IS NULL) OR InProgressDateRaw = PrevInProgressDate)
            AND ((CompletedDateRaw IS NULL AND PrevCompletedDate IS NULL) OR CompletedDateRaw = PrevCompletedDate)
        )

    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    -- diff to WorkItem
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    CREATE TABLE #DiffRevsCurrent
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        Watermark INT NULL,
        SrcRowHash VARBINARY(16) NULL,
        TgtRowHash VARBINARY(16) NULL
    )

    CREATE TABLE #WorkItemRevsAllChangedCurrent
    (
        WorkItemId INT NOT NULL,
        Revision INT NOT NULL,
        IsDeleted BIT NOT NULL,
        ChangedDate DATETIMEOFFSET NULL,
        RevisedDate DATETIMEOFFSET NULL,
        InProgressDateRaw DATETIMEOFFSET NULL, -- don't translate TZ here - do it only on updates/compare to targets to avoid unnessary work
        CompletedDateRaw DATETIMEOFFSET NULL, -- don't translate TZ here - do it only on updates/compare to targets to avoid unnessary work
        IsLastRevisionOfDay BIT NULL,
        IsLastRevisionOfPeriod INT NULL,
        CurrentProjectSK UNIQUEIDENTIFIER NULL,
        PRIMARY KEY CLUSTERED (WorkItemId, Revision)
    )

    -- CCI optimization - gather existing values from target into DiffRevs temp table for comparison with source
    -- Comparing values on a join to CCI is slower that pulling them an comparing in temp tables
    IF (@updatingCount > @batchSizeLarge) -- larger update - force hash join as we expect many rows
    BEGIN
        -- first, just grab the watermarks - the most likely column to change
        -- this optimization is because CCI is much faster for narrower queries,
        -- and for the fact that updates during incremental processing are likely to change the watermark
        INSERT #DiffRevsCurrent
        (
            WorkItemId,
            Revision,
            Watermark
        )
        SELECT
            t.WorkItemId,
            t.Revision,
            t.Watermark
        FROM #WorkItemRevs AS s
        JOIN #WorkItemRevsAll sx  -- attempte to reduce rows joined to large model table
        ON sx.WorkItemId = s.WorkItemId
            AND sx.Revision = s.Revision
            AND sx.ExistingCurrent = 1
        INNER HASH JOIN AnalyticsModel.tbl_WorkItem AS t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem)) -- force hash join for reprocess
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        WHERE s.IsCurrent = 1
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        UPDATE  d
        SET     TgtRowHash = HASHBYTES('MD5', (
                    SELECT  t.Watermark,
                            t.Title,
                            t.WorkItemType,
                            t.ChangedDate,
                            t.CreatedDate,
                            t.[State],
                            t.Reason,
                            t.FoundIn,
                            t.IntegrationBuild,
                            t.ActivatedDate,
                            t.Activity,
                            t.BacklogPriority,
                            t.BusinessValue,
                            t.ClosedDate,
                            t.Discipline,
                            t.Issue,
                            t.Priority,
                            t.Rating,
                            t.ResolvedDate,
                            t.ResolvedReason,
                            t.Risk,
                            t.Severity,
                            t.StackRank,
                            t.TimeCriticality,
                            t.Triage,
                            t.ValueArea,
                            t.DueDate,
                            t.FinishDate,
                            t.StartDate,
                            t.TargetDate,
                            t.Blocked,
                            t.Committed,
                            t.Escalate,
                            t.FoundInEnvironment,
                            t.HowFound,
                            t.Probability,
                            t.RequirementType,
                            t.RequiresReview,
                            t.RequiresTest,
                            t.RootCause,
                            t.SubjectMatterExpert1,
                            t.SubjectMatterExpert2,
                            t.SubjectMatterExpert3,
                            t.TargetResolveDate,
                            t.TaskType,
                            t.UserAcceptanceTest,
                            t.IsDeleted,
                            t.AutomatedTestId,
                            t.AutomatedTestName,
                            t.AutomatedTestStorage,
                            t.AutomatedTestType,
                            t.AutomationStatus,
                            t.ProjectSK,
                            t.DateSK,
                            t.AreaSK,
                            t.IterationSK,
                            t.CompletedWork,
                            t.Effort,
                            t.OriginalEstimate,
                            t.RemainingWork,
                            t.Size,
                            t.StoryPoints,
                            t.CreatedDateSK,
                            t.ActivatedDateSK,
                            t.ClosedDateSK,
                            t.ResolvedDateSK,
                            t.AssignedToUserSK,
                            t.ChangedByUserSK,
                            t.CreatedByUserSK,
                            t.ActivatedByUserSK,
                            t.ClosedByUserSK,
                            t.ResolvedByUserSK,
                            t.ParentWorkItemId,
                            CAST(t.ChangedDate AS DATETIME2) ChangedDateLocal, -- check for offset changes
                            CAST(t.CreatedDate AS DATETIME2) CreatedDateLocal, -- check for offset changes
                            CAST(t.ActivatedDate AS DATETIME2) ActivatedDateLocal, -- check for offset changes
                            CAST(t.ClosedDate AS DATETIME2) ClosedDateLocal, -- check for offset changes
                            CAST(t.ResolvedDate AS DATETIME2) ResolvedDateLocal, -- check for offset changes
                            t.TeamFieldSK,
                            t.TagNames,
                            t.StateCategory,
                            t.InProgressDate,
                            CAST(t.InProgressDate AS DATETIME2) InProgressDateLocal, -- check for offset changes
                            t.InProgressDateSK,
                            t.CompletedDate,
                            CAST(t.CompletedDate AS DATETIME2) CompletedDateLocal, -- check for offset changes
                            t.CompletedDateSK,
                            t.LeadTimeDays,
                            t.CycleTimeDays,
                            t.AuthorizedDate,
                            t.StateChangeDate,
                            t.StateChangeDateSK,
                            t.CommentCount
                            FOR XML RAW))
        FROM #DiffRevsCurrent d
        JOIN #WorkItemRevs AS s
            ON d.WorkItemId = s.WorkItemId
            AND d.Revision = s.Revision
        INNER HASH JOIN AnalyticsModel.tbl_WorkItem AS t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem)) -- force hash join for reprocess
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
        WHERE d.Watermark = s.Watermark
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE -- incremental
    BEGIN
        -- first, just grab the watermarks - the most likely column to change
        -- this optimization is because CCI is much faster for narrower queries,
        -- and for the fact that updates during incremental processing are likely to change the watermark
        -- using force order to ensure that CCI will not be used as outer part of the join
        INSERT #DiffRevsCurrent
        (
            WorkItemId,
            Revision,
            Watermark
        )
        SELECT
            t.WorkItemId,
            t.Revision,
            t.Watermark
        FROM #WorkItemRevs AS s
        JOIN #WorkItemRevsAll sx  -- attempte to reduce rows joined to large model table
        ON sx.WorkItemId = s.WorkItemId
            AND sx.Revision = s.Revision
            AND sx.ExistingCurrent = 1
        INNER JOIN AnalyticsModel.tbl_WorkItem AS t
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        WHERE s.IsCurrent = 1
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

        -- now grab other columns, for those revs where the watermark matches
        UPDATE d
        SET     TgtRowHash = HASHBYTES('MD5', (
                    SELECT  t.Watermark,
                            t.Title,
                            t.WorkItemType,
                            t.ChangedDate,
                            t.CreatedDate,
                            t.[State],
                            t.Reason,
                            t.FoundIn,
                            t.IntegrationBuild,
                            t.ActivatedDate,
                            t.Activity,
                            t.BacklogPriority,
                            t.BusinessValue,
                            t.ClosedDate,
                            t.Discipline,
                            t.Issue,
                            t.Priority,
                            t.Rating,
                            t.ResolvedDate,
                            t.ResolvedReason,
                            t.Risk,
                            t.Severity,
                            t.StackRank,
                            t.TimeCriticality,
                            t.Triage,
                            t.ValueArea,
                            t.DueDate,
                            t.FinishDate,
                            t.StartDate,
                            t.TargetDate,
                            t.Blocked,
                            t.Committed,
                            t.Escalate,
                            t.FoundInEnvironment,
                            t.HowFound,
                            t.Probability,
                            t.RequirementType,
                            t.RequiresReview,
                            t.RequiresTest,
                            t.RootCause,
                            t.SubjectMatterExpert1,
                            t.SubjectMatterExpert2,
                            t.SubjectMatterExpert3,
                            t.TargetResolveDate,
                            t.TaskType,
                            t.UserAcceptanceTest,
                            t.IsDeleted,
                            t.AutomatedTestId,
                            t.AutomatedTestName,
                            t.AutomatedTestStorage,
                            t.AutomatedTestType,
                            t.AutomationStatus,
                            t.ProjectSK,
                            t.DateSK,
                            t.AreaSK,
                            t.IterationSK,
                            t.CompletedWork,
                            t.Effort,
                            t.OriginalEstimate,
                            t.RemainingWork,
                            t.Size,
                            t.StoryPoints,
                            t.CreatedDateSK,
                            t.ActivatedDateSK,
                            t.ClosedDateSK,
                            t.ResolvedDateSK,
                            t.AssignedToUserSK,
                            t.ChangedByUserSK,
                            t.CreatedByUserSK,
                            t.ActivatedByUserSK,
                            t.ClosedByUserSK,
                            t.ResolvedByUserSK,
                            t.ParentWorkItemId,
                            CAST(t.ChangedDate AS DATETIME2) ChangedDateLocal, -- check for offset changes
                            CAST(t.CreatedDate AS DATETIME2) CreatedDateLocal, -- check for offset changes
                            CAST(t.ActivatedDate AS DATETIME2) ActivatedDateLocal, -- check for offset changes
                            CAST(t.ClosedDate AS DATETIME2) ClosedDateLocal, -- check for offset changes
                            CAST(t.ResolvedDate AS DATETIME2) ResolvedDateLocal, -- check for offset changes
                            t.TeamFieldSK,
                            t.TagNames,
                            t.StateCategory,
                            t.InProgressDate,
                            CAST(t.InProgressDate AS DATETIME2) InProgressDateLocal, -- check for offset changes
                            t.InProgressDateSK,
                            t.CompletedDate,
                            CAST(t.CompletedDate AS DATETIME2) CompletedDateLocal, -- check for offset changes
                            t.CompletedDateSK,
                            t.LeadTimeDays,
                            t.CycleTimeDays,
                            t.AuthorizedDate,
                            t.StateChangeDate,
                            t.StateChangeDateSK,
                            t.CommentCount
                            FOR XML RAW))
        FROM #DiffRevsCurrent d
        JOIN #WorkItemRevs AS s
            ON d.WorkItemId = s.WorkItemId
            AND d.Revision = s.Revision
        INNER JOIN AnalyticsModel.tbl_WorkItem AS t
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
        WHERE d.Watermark = s.Watermark
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    UPDATE  d
    SET     SrcRowHash = HASHBYTES('MD5', (
                SELECT  s.Watermark,
                        s.Title,
                        s.WorkItemType,
                        s.ChangedDate,
                        s.CreatedDate,
                        s.[State],
                        s.Reason,
                        s.FoundIn,
                        s.IntegrationBuild,
                        s.ActivatedDate,
                        s.Activity,
                        s.BacklogPriority,
                        s.BusinessValue,
                        s.ClosedDate,
                        s.Discipline,
                        s.Issue,
                        s.Priority,
                        s.Rating,
                        s.ResolvedDate,
                        s.ResolvedReason,
                        s.Risk,
                        s.Severity,
                        s.StackRank,
                        s.TimeCriticality,
                        s.Triage,
                        s.ValueArea,
                        s.DueDate,
                        s.FinishDate,
                        s.StartDate,
                        s.TargetDate,
                        s.Blocked,
                        s.Committed,
                        s.Escalate,
                        s.FoundInEnvironment,
                        s.HowFound,
                        s.Probability,
                        s.RequirementType,
                        s.RequiresReview,
                        s.RequiresTest,
                        s.RootCause,
                        s.SubjectMatterExpert1,
                        s.SubjectMatterExpert2,
                        s.SubjectMatterExpert3,
                        s.TargetResolveDate,
                        s.TaskType,
                        s.UserAcceptanceTest,
                        s.IsDeleted,
                        s.AutomatedTestId,
                        s.AutomatedTestName,
                        s.AutomatedTestStorage,
                        s.AutomatedTestType,
                        s.AutomationStatus,
                        s.ProjectSK,
                        s.DateSK,
                        s.AreaSK,
                        s.IterationSK,
                        s.CompletedWork,
                        s.Effort,
                        s.OriginalEstimate,
                        s.RemainingWork,
                        s.Size,
                        s.StoryPoints,
                        s.CreatedDateSK,
                        s.ActivatedDateSK,
                        s.ClosedDateSK,
                        s.ResolvedDateSK,
                        s.AssignedToUserSK,
                        s.ChangedByUserSK,
                        s.CreatedByUserSK,
                        s.ActivatedByUserSK,
                        s.ClosedByUserSK,
                        s.ResolvedByUserSK,
                        s.ParentWorkItemId,
                        CAST(s.ChangedDate AS DATETIME2) ChangedDateLocal, -- check for offset changes
                        CAST(s.CreatedDate AS DATETIME2) CreatedDateLocal, -- check for offset changes
                        CAST(s.ActivatedDate AS DATETIME2) ActivatedDateLocal, -- check for offset changes
                        CAST(s.ClosedDate AS DATETIME2) ClosedDateLocal, -- check for offset changes
                        CAST(s.ResolvedDate AS DATETIME2) ResolvedDateLocal, -- check for offset changes
                        s.TeamFieldSK,
                        s.TagNames,
                        s.StateCategory,
                        s.InProgressDate,
                        CAST(s.InProgressDate AS DATETIME2) InProgressDateLocal, -- check for offset changes
                        s.InProgressDateSK,
                        s.CompletedDate,
                        CAST(s.CompletedDate AS DATETIME2) CompletedDateLocal, -- check for offset changes
                        s.CompletedDateSK,
                        s.LeadTimeDays,
                        s.CycleTimeDays,
                        s.AuthorizedDate,
                        s.StateChangeDate,
                        s.StateChangeDateSK,
                        s.CommentCount
                        FOR XML RAW))
    FROM #DiffRevsCurrent d
    JOIN #WorkItemRevs AS s
    ON s.WorkItemId = d.WorkItemId
        AND s.Revision = d.Revision

    DELETE t
    FROM #DiffRevsCurrent t
    WHERE SrcRowHash = TgtRowHash

    -- double check and update IsCurrent flags for all revisions
    -- update work items IsDelete with the latest IsDeleted
    INSERT #WorkItemRevsAllChangedCurrent (WorkItemId, Revision, IsDeleted, ChangedDate, InProgressDateRaw, CompletedDateRaw)
    SELECT  WorkItemId,
            Revision,
            IsDeleted,
            ChangedDateRaw,
            InProgressDateRaw,
            CompletedDateRaw
    FROM #WorkItemRevsAll AS s
    WHERE s.ExistingCurrent = 1 -- important - prev fields are not set for newly inserted rows - these rows for this handled via DiffRevs update
        AND NOT
        (
            IsDeleted = PrevIsDeleted
            AND (PrevChangedDate IS NULL OR ChangedDateRaw = PrevChangedDate)
            AND ((InProgressDateRaw IS NULL AND PrevInProgressDate IS NULL) OR InProgressDateRaw = PrevInProgressDate)
            AND ((CompletedDateRaw IS NULL AND PrevCompletedDate IS NULL) OR CompletedDateRaw = PrevCompletedDate)
        )

    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    -- update WorkItemHistory and WorkItem
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    BEGIN TRAN

    IF ((SELECT COUNT(*) FROM #DiffRevsHistory) > @batchSizeLarge) -- force hash join on larger updates
    BEGIN
        UPDATE AnalyticsModel.tbl_WorkItemHistory
        SET AnalyticsUpdatedDate      = @batchDt,
            AnalyticsBatchId          = @batchId,
            Watermark                 = s.Watermark,
            Title                     = s.Title,
            WorkItemType              = s.WorkItemType,
            ChangedDate               = s.ChangedDate,
            CreatedDate               = s.CreatedDate,
            [State]                     = s.[State],
            Reason                    = s.Reason,
            FoundIn                   = s.FoundIn,
            IntegrationBuild          = s.IntegrationBuild,
            ActivatedDate             = s.ActivatedDate,
            Activity                  = s.Activity,
            BacklogPriority           = s.BacklogPriority,
            BusinessValue             = s.BusinessValue,
            ClosedDate                = s.ClosedDate,
            Discipline                = s.Discipline,
            Issue                     = s.Issue,
            Priority                  = s.Priority,
            Rating                    = s.Rating,
            ResolvedDate              = s.ResolvedDate,
            ResolvedReason            = s.ResolvedReason,
            Risk                      = s.Risk,
            Severity                  = s.Severity,
            StackRank                 = s.StackRank,
            TimeCriticality           = s.TimeCriticality,
            Triage                    = s.Triage,
            ValueArea                 = s.ValueArea,
            DueDate                   = s.DueDate,
            FinishDate                = s.FinishDate,
            StartDate                 = s.StartDate,
            TargetDate                = s.TargetDate,
            Blocked                   = s.Blocked,
            Committed                 = s.Committed,
            Escalate                  = s.Escalate,
            FoundInEnvironment        = s.FoundInEnvironment,
            HowFound                  = s.HowFound,
            Probability               = s.Probability,
            RequirementType           = s.RequirementType,
            RequiresReview            = s.RequiresReview,
            RequiresTest              = s.RequiresTest,
            RootCause                 = s.RootCause,
            SubjectMatterExpert1      = s.SubjectMatterExpert1,
            SubjectMatterExpert2      = s.SubjectMatterExpert2,
            SubjectMatterExpert3      = s.SubjectMatterExpert3,
            TargetResolveDate         = s.TargetResolveDate,
            TaskType                  = s.TaskType,
            UserAcceptanceTest        = s.UserAcceptanceTest,
            IsDeleted                 = s.IsDeleted,
            RevisedDate               = s.RevisedDate,
            AutomatedTestId           = s.AutomatedTestId,
            AutomatedTestName         = s.AutomatedTestName,
            AutomatedTestStorage      = s.AutomatedTestStorage,
            AutomatedTestType         = s.AutomatedTestType,
            AutomationStatus          = s.AutomationStatus,
            ProjectSK                 = s.ProjectSK,
            DateSK                    = s.DateSK,
            AreaSK                    = s.AreaSK,
            IterationSK               = s.IterationSK,
            CompletedWork             = s.CompletedWork,
            Effort                    = s.Effort,
            OriginalEstimate          = s.OriginalEstimate,
            RemainingWork             = s.RemainingWork,
            Size                      = s.Size,
            StoryPoints               = s.StoryPoints,
            RevisedDateSK             = s.RevisedDateSK,
            CreatedDateSK             = s.CreatedDateSK,
            ActivatedDateSK           = s.ActivatedDateSK,
            ClosedDateSK              = s.ClosedDateSK,
            ResolvedDateSK            = s.ResolvedDateSK,
            AssignedToUserSK          = s.AssignedToUserSK,
            ChangedByUserSK           = s.ChangedByUserSK,
            CreatedByUserSK           = s.CreatedByUserSK,
            ActivatedByUserSK         = s.ActivatedByUserSK,
            ClosedByUserSK            = s.ClosedByUserSK,
            ResolvedByUserSK          = s.ResolvedByUserSK,
            ParentWorkItemId          = s.ParentWorkItemId,
            IsLastRevisionOfDay       = s.IsLastRevisionOfDay,
            IsLastRevisionOfPeriod    = s.IsLastRevisionOfPeriod,
            TeamFieldSK               = s.TeamFieldSK,
            TagNames                  = s.TagNames,
            StateCategory             = s.StateCategory,
            InProgressDate            = s.InProgressDate,
            InProgressDateSK          = s.InProgressDateSK,
            CompletedDate             = s.CompletedDate,
            CompletedDateSK           = s.CompletedDateSK,
            LeadTimeDays              = s.LeadTimeDays,
            CycleTimeDays             = s.CycleTimeDays,
            AuthorizedDate            = s.AuthorizedDate,
            StateChangeDate           = s.StateChangeDate,
            StateChangeDateSK         = s.StateChangeDateSK,
            CurrentProjectSK          = s.CurrentProjectSK,
            CommentCount              = s.CommentCount
        FROM #DiffRevsHistory AS d
        JOIN #WorkItemRevs AS s
        ON s.WorkItemId = d.WorkItemId
            AND s.Revision = d.Revision
        INNER HASH JOIN AnalyticsModel.tbl_WorkItemHistory AS t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory)) -- force has join with large number of updates
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        UPDATE AnalyticsModel.tbl_WorkItemHistory
        SET AnalyticsUpdatedDate              = @batchDt,
            AnalyticsBatchId                 = @batchId,
            Watermark                 = s.Watermark,
            Title                     = s.Title,
            WorkItemType              = s.WorkItemType,
            ChangedDate               = s.ChangedDate,
            CreatedDate               = s.CreatedDate,
            [State]                     = s.[State],
            Reason                    = s.Reason,
            FoundIn                   = s.FoundIn,
            IntegrationBuild          = s.IntegrationBuild,
            ActivatedDate             = s.ActivatedDate,
            Activity                  = s.Activity,
            BacklogPriority           = s.BacklogPriority,
            BusinessValue             = s.BusinessValue,
            ClosedDate                = s.ClosedDate,
            Discipline                = s.Discipline,
            Issue                     = s.Issue,
            Priority                  = s.Priority,
            Rating                    = s.Rating,
            ResolvedDate              = s.ResolvedDate,
            ResolvedReason            = s.ResolvedReason,
            Risk                      = s.Risk,
            Severity                  = s.Severity,
            StackRank                 = s.StackRank,
            TimeCriticality           = s.TimeCriticality,
            Triage                    = s.Triage,
            ValueArea                 = s.ValueArea,
            DueDate                   = s.DueDate,
            FinishDate                = s.FinishDate,
            StartDate                 = s.StartDate,
            TargetDate                = s.TargetDate,
            Blocked                   = s.Blocked,
            Committed                 = s.Committed,
            Escalate                  = s.Escalate,
            FoundInEnvironment        = s.FoundInEnvironment,
            HowFound                  = s.HowFound,
            Probability               = s.Probability,
            RequirementType           = s.RequirementType,
            RequiresReview            = s.RequiresReview,
            RequiresTest              = s.RequiresTest,
            RootCause                 = s.RootCause,
            SubjectMatterExpert1      = s.SubjectMatterExpert1,
            SubjectMatterExpert2      = s.SubjectMatterExpert2,
            SubjectMatterExpert3      = s.SubjectMatterExpert3,
            TargetResolveDate         = s.TargetResolveDate,
            TaskType                  = s.TaskType,
            UserAcceptanceTest        = s.UserAcceptanceTest,
            IsDeleted                 = s.IsDeleted,
            RevisedDate               = s.RevisedDate,
            AutomatedTestId           = s.AutomatedTestId,
            AutomatedTestName         = s.AutomatedTestName,
            AutomatedTestStorage      = s.AutomatedTestStorage,
            AutomatedTestType         = s.AutomatedTestType,
            AutomationStatus          = s.AutomationStatus,
            ProjectSK                 = s.ProjectSK,
            DateSK                    = s.DateSK,
            AreaSK                    = s.AreaSK,
            IterationSK               = s.IterationSK,
            CompletedWork             = s.CompletedWork,
            Effort                    = s.Effort,
            OriginalEstimate          = s.OriginalEstimate,
            RemainingWork             = s.RemainingWork,
            Size                      = s.Size,
            StoryPoints               = s.StoryPoints,
            RevisedDateSK             = s.RevisedDateSK,
            CreatedDateSK             = s.CreatedDateSK,
            ActivatedDateSK           = s.ActivatedDateSK,
            ClosedDateSK              = s.ClosedDateSK,
            ResolvedDateSK            = s.ResolvedDateSK,
            AssignedToUserSK          = s.AssignedToUserSK,
            ChangedByUserSK           = s.ChangedByUserSK,
            CreatedByUserSK           = s.CreatedByUserSK,
            ActivatedByUserSK         = s.ActivatedByUserSK,
            ClosedByUserSK            = s.ClosedByUserSK,
            ResolvedByUserSK          = s.ResolvedByUserSK,
            ParentWorkItemId          = s.ParentWorkItemId,
            IsLastRevisionOfDay       = s.IsLastRevisionOfDay,
            IsLastRevisionOfPeriod    = s.IsLastRevisionOfPeriod,
            TeamFieldSK               = s.TeamFieldSK,
            TagNames                  = s.TagNames,
            StateCategory             = s.StateCategory,
            InProgressDate            = s.InProgressDate,
            InProgressDateSK          = s.InProgressDateSK,
            CompletedDate             = s.CompletedDate,
            CompletedDateSK           = s.CompletedDateSK,
            LeadTimeDays              = s.LeadTimeDays,
            CycleTimeDays             = s.CycleTimeDays,
            AuthorizedDate            = s.AuthorizedDate,
            StateChangeDate           = s.StateChangeDate,
            StateChangeDateSK         = s.StateChangeDateSK,
            CurrentProjectSK          = s.CurrentProjectSK,
            CommentCount              = s.CommentCount
        FROM #DiffRevsHistory AS d
        JOIN #WorkItemRevs AS s
        ON s.WorkItemId = d.WorkItemId
            AND s.Revision = d.Revision
        JOIN AnalyticsModel.tbl_WorkItemHistory AS t WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemIdRev)) -- allow use of row lock on NCI vs CCI segments
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    SET @updatedCount += @@ROWCOUNT

    INSERT AnalyticsModel.tbl_WorkItemHistory
    (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        WorkItemRevisionSK,
        WorkItemId,
        Revision,
        Watermark,
        Title,
        WorkItemType,
        ChangedDate,
        CreatedDate,
        [State],
        Reason,
        FoundIn,
        IntegrationBuild,
        ActivatedDate,
        Activity,
        BacklogPriority,
        BusinessValue,
        ClosedDate,
        Discipline,
        Issue,
        Priority,
        Rating,
        ResolvedDate,
        ResolvedReason,
        Risk,
        Severity,
        StackRank,
        TimeCriticality,
        Triage,
        ValueArea,
        DueDate,
        FinishDate,
        StartDate,
        TargetDate,
        Blocked,
        Committed,
        Escalate,
        FoundInEnvironment,
        HowFound,
        Probability,
        RequirementType,
        RequiresReview,
        RequiresTest,
        RootCause,
        SubjectMatterExpert1,
        SubjectMatterExpert2,
        SubjectMatterExpert3,
        TargetResolveDate,
        TaskType,
        UserAcceptanceTest,
        IsDeleted,
        RevisedDate,
        AutomatedTestId,
        AutomatedTestName,
        AutomatedTestStorage,
        AutomatedTestType,
        AutomationStatus,
        ProjectSK,
        DateSK,
        AreaSK,
        IterationSK,
        CompletedWork,
        Effort,
        OriginalEstimate,
        RemainingWork,
        Size,
        StoryPoints,
        RevisedDateSK,
        CreatedDateSK,
        ActivatedDateSK,
        ClosedDateSK,
        ResolvedDateSK,
        AssignedToUserSK,
        ChangedByUserSK,
        CreatedByUserSK,
        ActivatedByUserSK,
        ClosedByUserSK,
        ResolvedByUserSK,
        ParentWorkItemId,
        IsLastRevisionOfDay,
        IsLastRevisionOfPeriod,
        TeamFieldSK,
        TagNames,
        StateCategory,
        InProgressDate,
        InProgressDateSK,
        CompletedDate,
        CompletedDateSK,
        LeadTimeDays,
        CycleTimeDays,
        InternalForSnapshotHashJoin,
        AuthorizedDate,
        StateChangeDate,
        StateChangeDateSK,
        CurrentProjectSK,
        CommentCount
    )
    SELECT
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.WorkItemRevisionSK,
        s.WorkItemId,
        s.Revision,
        s.Watermark,
        s.Title,
        s.WorkItemType,
        s.ChangedDate,
        s.CreatedDate,
        s.[State],
        s.Reason,
        s.FoundIn,
        s.IntegrationBuild,
        s.ActivatedDate,
        s.Activity,
        s.BacklogPriority,
        s.BusinessValue,
        s.ClosedDate,
        s.Discipline,
        s.Issue,
        s.Priority,
        s.Rating,
        s.ResolvedDate,
        s.ResolvedReason,
        s.Risk,
        s.Severity,
        s.StackRank,
        s.TimeCriticality,
        s.Triage,
        s.ValueArea,
        s.DueDate,
        s.FinishDate,
        s.StartDate,
        s.TargetDate,
        s.Blocked,
        s.Committed,
        s.Escalate,
        s.FoundInEnvironment,
        s.HowFound,
        s.Probability,
        s.RequirementType,
        s.RequiresReview,
        s.RequiresTest,
        s.RootCause,
        s.SubjectMatterExpert1,
        s.SubjectMatterExpert2,
        s.SubjectMatterExpert3,
        s.TargetResolveDate,
        s.TaskType,
        s.UserAcceptanceTest,
        s.IsDeleted,
        s.RevisedDate,
        s.AutomatedTestId,
        s.AutomatedTestName,
        s.AutomatedTestStorage,
        s.AutomatedTestType,
        s.AutomationStatus,
        s.ProjectSK,
        s.DateSK,
        s.AreaSK,
        s.IterationSK,
        s.CompletedWork,
        s.Effort,
        s.OriginalEstimate,
        s.RemainingWork,
        s.Size,
        s.StoryPoints,
        s.RevisedDateSK,
        s.CreatedDateSK,
        s.ActivatedDateSK,
        s.ClosedDateSK,
        s.ResolvedDateSK,
        s.AssignedToUserSK,
        s.ChangedByUserSK,
        s.CreatedByUserSK,
        s.ActivatedByUserSK,
        s.ClosedByUserSK,
        s.ResolvedByUserSK,
        s.ParentWorkItemId,
        s.IsLastRevisionOfDay,
        s.IsLastRevisionOfPeriod,
        s.TeamFieldSK,
        s.TagNames,
        s.StateCategory,
        s.InProgressDate,
        s.InProgressDateSK,
        s.CompletedDate,
        s.CompletedDateSK,
        s.LeadTimeDays,
        s.CycleTimeDays,
        1 AS InternalForSnapshotHashJoin,
        s.AuthorizedDate,
        s.StateChangeDate,
        s.StateChangeDateSK,
        s.CurrentProjectSK,
        s.CommentCount
    FROM #WorkItemRevs AS s
    JOIN #WorkItemRevsAll sx
    ON sx.WorkItemId = s.WorkItemId
        AND sx.Revision = s.Revision
        AND sx.ExistingHistory = 0
    WHERE s.IsCurrent = 0
    ORDER BY s.WorkItemRevisionSK
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT

    IF ((SELECT COUNT(*) FROM #WorkItemRevsAllChangedHistory) > @batchSizeLarge) -- force hash join on larger updates
    BEGIN
        UPDATE              t
        SET                 AnalyticsUpdatedDate = @batchDt,
                            AnalyticsBatchId = @batchId,
                            IsDeleted = s.IsDeleted,
                            CurrentProjectSK    = s.CurrentProjectSK,
                            ChangedDate         = s.ChangedDate,
                            DateSK              = AnalyticsInternal.func_GenDateSK(s.ChangedDate),
                            RevisedDate         = s.RevisedDate,
                            RevisedDateSK       = AnalyticsInternal.func_GenDateSK(s.RevisedDate),
                            InProgressDate      = s.InProgressDateRaw  AT TIME ZONE @timeZone,
                            InProgressDateSK    = AnalyticsInternal.func_GenDateSK(s.InProgressDateRaw  AT TIME ZONE @timeZone),
                            CompletedDate       = s.CompletedDateRaw AT TIME ZONE @timeZone,
                            CompletedDateSK     = AnalyticsInternal.func_GenDateSK(s.CompletedDateRaw  AT TIME ZONE @timeZone),
                            LeadTimeDays        = IIF(t.CreatedDate > s.CompletedDateRaw, 0.0, DATEDIFF_BIG(second, t.CreatedDate, s.CompletedDateRaw) / 86400.0),
                            CycleTimeDays       = IIF(s.InProgressDateRaw > s.CompletedDateRaw, 0.0, DATEDIFF_BIG(second, s.InProgressDateRaw, s.CompletedDateRaw) / 86400.0),
                            IsLastRevisionOfDay = IIF(s.IsLastRevisionOfDay IS NOT NULL, s.IsLastRevisionOfDay, t.IsLastRevisionOfDay), -- a NULL indicates we did not re-evaluate this field
                            IsLastRevisionOfPeriod = IIF(s.IsLastRevisionOfPeriod IS NOT NULL, s.IsLastRevisionOfPeriod, t.IsLastRevisionOfPeriod) -- a NULL indicates we did not re-evaluate this field
        FROM                #WorkItemRevsAllChangedHistory s
        INNER HASH JOIN     AnalyticsModel.tbl_WorkItemHistory t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItemHistory)) -- force hash join for reprocess
        ON                  @partitionId = t.PartitionId
                            AND s.WorkItemId = t.WorkItemId
                            AND s.Revision = t.Revision
        OPTION              (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        UPDATE  t
        SET     AnalyticsUpdatedDate = @batchDt,
                AnalyticsBatchId = @batchId,
                IsDeleted = s.IsDeleted,
                CurrentProjectSK    = s.CurrentProjectSK,
                ChangedDate         = s.ChangedDate,
                DateSK              = AnalyticsInternal.func_GenDateSK(s.ChangedDate),
                RevisedDate         = s.RevisedDate,
                RevisedDateSK       = AnalyticsInternal.func_GenDateSK(s.RevisedDate),
                InProgressDate      = s.InProgressDateRaw  AT TIME ZONE @timeZone,
                InProgressDateSK    = AnalyticsInternal.func_GenDateSK(s.InProgressDateRaw  AT TIME ZONE @timeZone),
                CompletedDate       = s.CompletedDateRaw AT TIME ZONE @timeZone,
                CompletedDateSK     = AnalyticsInternal.func_GenDateSK(s.CompletedDateRaw  AT TIME ZONE @timeZone),
                LeadTimeDays        = IIF(t.CreatedDate > s.CompletedDateRaw, 0.0, DATEDIFF_BIG(second, t.CreatedDate, s.CompletedDateRaw) / 86400.0),
                CycleTimeDays       = IIF(s.InProgressDateRaw > s.CompletedDateRaw, 0.0, DATEDIFF_BIG(second, s.InProgressDateRaw, s.CompletedDateRaw) / 86400.0),
                IsLastRevisionOfDay = IIF(s.IsLastRevisionOfDay IS NOT NULL, s.IsLastRevisionOfDay, t.IsLastRevisionOfDay), -- a NULL indicates we did not re-evaluate this field
                IsLastRevisionOfPeriod = IIF(s.IsLastRevisionOfPeriod IS NOT NULL, s.IsLastRevisionOfPeriod, t.IsLastRevisionOfPeriod) -- a NULL indicates we did not re-evaluate this field
        FROM    AnalyticsModel.tbl_WorkItemHistory t WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemIdRev)) -- allow use of row lock on NCI vs CCI segments
        JOIN    #WorkItemRevsAllChangedHistory s
        ON      @partitionId = t.PartitionId
                AND s.WorkItemId = t.WorkItemId
                AND s.Revision = t.Revision
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    SET @updatedCount += @@ROWCOUNT

    IF ((SELECT COUNT(*) FROM #DiffRevsCurrent) > @batchSizeLarge) -- force hash join on larger updates
    BEGIN
        UPDATE AnalyticsModel.tbl_WorkItem
        SET AnalyticsUpdatedDate              = @batchDt,
            AnalyticsBatchId                 = @batchId,
            Watermark                 = s.Watermark,
            Title                     = s.Title,
            WorkItemType              = s.WorkItemType,
            ChangedDate               = s.ChangedDate,
            CreatedDate               = s.CreatedDate,
            [State]                     = s.[State],
            Reason                    = s.Reason,
            FoundIn                   = s.FoundIn,
            IntegrationBuild          = s.IntegrationBuild,
            ActivatedDate             = s.ActivatedDate,
            Activity                  = s.Activity,
            BacklogPriority           = s.BacklogPriority,
            BusinessValue             = s.BusinessValue,
            ClosedDate                = s.ClosedDate,
            Discipline                = s.Discipline,
            Issue                     = s.Issue,
            Priority                  = s.Priority,
            Rating                    = s.Rating,
            ResolvedDate              = s.ResolvedDate,
            ResolvedReason            = s.ResolvedReason,
            Risk                      = s.Risk,
            Severity                  = s.Severity,
            StackRank                 = s.StackRank,
            TimeCriticality           = s.TimeCriticality,
            Triage                    = s.Triage,
            ValueArea                 = s.ValueArea,
            DueDate                   = s.DueDate,
            FinishDate                = s.FinishDate,
            StartDate                 = s.StartDate,
            TargetDate                = s.TargetDate,
            Blocked                   = s.Blocked,
            Committed                 = s.Committed,
            Escalate                  = s.Escalate,
            FoundInEnvironment        = s.FoundInEnvironment,
            HowFound                  = s.HowFound,
            Probability               = s.Probability,
            RequirementType           = s.RequirementType,
            RequiresReview            = s.RequiresReview,
            RequiresTest              = s.RequiresTest,
            RootCause                 = s.RootCause,
            SubjectMatterExpert1      = s.SubjectMatterExpert1,
            SubjectMatterExpert2      = s.SubjectMatterExpert2,
            SubjectMatterExpert3      = s.SubjectMatterExpert3,
            TargetResolveDate         = s.TargetResolveDate,
            TaskType                  = s.TaskType,
            UserAcceptanceTest        = s.UserAcceptanceTest,
            IsDeleted                 = s.IsDeleted,
            AutomatedTestId           = s.AutomatedTestId,
            AutomatedTestName         = s.AutomatedTestName,
            AutomatedTestStorage      = s.AutomatedTestStorage,
            AutomatedTestType         = s.AutomatedTestType,
            AutomationStatus          = s.AutomationStatus,
            ProjectSK                 = s.ProjectSK,
            DateSK                    = s.DateSK,
            AreaSK                    = s.AreaSK,
            IterationSK               = s.IterationSK,
            CompletedWork             = s.CompletedWork,
            Effort                    = s.Effort,
            OriginalEstimate          = s.OriginalEstimate,
            RemainingWork             = s.RemainingWork,
            Size                      = s.Size,
            StoryPoints               = s.StoryPoints,
            CreatedDateSK             = s.CreatedDateSK,
            ActivatedDateSK           = s.ActivatedDateSK,
            ClosedDateSK              = s.ClosedDateSK,
            ResolvedDateSK            = s.ResolvedDateSK,
            AssignedToUserSK          = s.AssignedToUserSK,
            ChangedByUserSK           = s.ChangedByUserSK,
            CreatedByUserSK           = s.CreatedByUserSK,
            ActivatedByUserSK         = s.ActivatedByUserSK,
            ClosedByUserSK            = s.ClosedByUserSK,
            ResolvedByUserSK          = s.ResolvedByUserSK,
            ParentWorkItemId          = s.ParentWorkItemId,
            TeamFieldSK               = s.TeamFieldSK,
            TagNames                  = s.TagNames,
            StateCategory             = s.StateCategory,
            InProgressDate            = s.InProgressDate,
            InProgressDateSK          = s.InProgressDateSK,
            CompletedDate             = s.CompletedDate,
            CompletedDateSK           = s.CompletedDateSK,
            LeadTimeDays              = s.LeadTimeDays,
            CycleTimeDays             = s.CycleTimeDays,
            AuthorizedDate            = s.AuthorizedDate,
            StateChangeDate           = s.StateChangeDate,
            StateChangeDateSK         = s.StateChangeDateSK,
            CommentCount              = s.CommentCount
        FROM #DiffRevsCurrent AS d
        JOIN #WorkItemRevs AS s
        ON s.WorkItemId = d.WorkItemId
            AND s.Revision = d.Revision
        INNER HASH JOIN AnalyticsModel.tbl_WorkItem AS t WITH (INDEX (CL_AnalyticsModel_tbl_WorkItem)) -- force has join with large number of updates
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        UPDATE AnalyticsModel.tbl_WorkItem
        SET AnalyticsUpdatedDate              = @batchDt,
            AnalyticsBatchId                 = @batchId,
            Watermark                 = s.Watermark,
            Title                     = s.Title,
            WorkItemType              = s.WorkItemType,
            ChangedDate               = s.ChangedDate,
            CreatedDate               = s.CreatedDate,
            [State]                     = s.[State],
            Reason                    = s.Reason,
            FoundIn                   = s.FoundIn,
            IntegrationBuild          = s.IntegrationBuild,
            ActivatedDate             = s.ActivatedDate,
            Activity                  = s.Activity,
            BacklogPriority           = s.BacklogPriority,
            BusinessValue             = s.BusinessValue,
            ClosedDate                = s.ClosedDate,
            Discipline                = s.Discipline,
            Issue                     = s.Issue,
            Priority                  = s.Priority,
            Rating                    = s.Rating,
            ResolvedDate              = s.ResolvedDate,
            ResolvedReason            = s.ResolvedReason,
            Risk                      = s.Risk,
            Severity                  = s.Severity,
            StackRank                 = s.StackRank,
            TimeCriticality           = s.TimeCriticality,
            Triage                    = s.Triage,
            ValueArea                 = s.ValueArea,
            DueDate                   = s.DueDate,
            FinishDate                = s.FinishDate,
            StartDate                 = s.StartDate,
            TargetDate                = s.TargetDate,
            Blocked                   = s.Blocked,
            Committed                 = s.Committed,
            Escalate                  = s.Escalate,
            FoundInEnvironment        = s.FoundInEnvironment,
            HowFound                  = s.HowFound,
            Probability               = s.Probability,
            RequirementType           = s.RequirementType,
            RequiresReview            = s.RequiresReview,
            RequiresTest              = s.RequiresTest,
            RootCause                 = s.RootCause,
            SubjectMatterExpert1      = s.SubjectMatterExpert1,
            SubjectMatterExpert2      = s.SubjectMatterExpert2,
            SubjectMatterExpert3      = s.SubjectMatterExpert3,
            TargetResolveDate         = s.TargetResolveDate,
            TaskType                  = s.TaskType,
            UserAcceptanceTest        = s.UserAcceptanceTest,
            IsDeleted                 = s.IsDeleted,
            AutomatedTestId           = s.AutomatedTestId,
            AutomatedTestName         = s.AutomatedTestName,
            AutomatedTestStorage      = s.AutomatedTestStorage,
            AutomatedTestType         = s.AutomatedTestType,
            AutomationStatus          = s.AutomationStatus,
            ProjectSK                 = s.ProjectSK,
            DateSK                    = s.DateSK,
            AreaSK                    = s.AreaSK,
            IterationSK               = s.IterationSK,
            CompletedWork             = s.CompletedWork,
            Effort                    = s.Effort,
            OriginalEstimate          = s.OriginalEstimate,
            RemainingWork             = s.RemainingWork,
            Size                      = s.Size,
            StoryPoints               = s.StoryPoints,
            CreatedDateSK             = s.CreatedDateSK,
            ActivatedDateSK           = s.ActivatedDateSK,
            ClosedDateSK              = s.ClosedDateSK,
            ResolvedDateSK            = s.ResolvedDateSK,
            AssignedToUserSK          = s.AssignedToUserSK,
            ChangedByUserSK           = s.ChangedByUserSK,
            CreatedByUserSK           = s.CreatedByUserSK,
            ActivatedByUserSK         = s.ActivatedByUserSK,
            ClosedByUserSK            = s.ClosedByUserSK,
            ResolvedByUserSK          = s.ResolvedByUserSK,
            ParentWorkItemId          = s.ParentWorkItemId,
            TeamFieldSK               = s.TeamFieldSK,
            TagNames                  = s.TagNames,
            StateCategory             = s.StateCategory,
            InProgressDate            = s.InProgressDate,
            InProgressDateSK          = s.InProgressDateSK,
            CompletedDate             = s.CompletedDate,
            CompletedDateSK           = s.CompletedDateSK,
            LeadTimeDays              = s.LeadTimeDays,
            CycleTimeDays             = s.CycleTimeDays,
            AuthorizedDate            = s.AuthorizedDate,
            StateChangeDate           = s.StateChangeDate,
            StateChangeDateSK         = s.StateChangeDateSK,
            CommentCount              = s.CommentCount
        FROM #DiffRevsCurrent AS d
        JOIN #WorkItemRevs AS s
        ON s.WorkItemId = d.WorkItemId
            AND s.Revision = d.Revision
        JOIN AnalyticsModel.tbl_WorkItem AS t WITH (INDEX (IX_tbl_WorkItem_WorkItemIdRev)) -- allow use of row lock on NCI vs CCI segments
        ON @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    SET @updatedCount += @@ROWCOUNT

    DELETE t
    FROM AnalyticsModel.tbl_WorkItem t
    JOIN #WorkItemRevsAll sx
    ON sx.WorkItemId = t.WorkItemId
        AND sx.Revision = t.Revision
        AND sx.ExistingCurrent = 1
        AND sx.IsCurrent = 0
    WHERE t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- decrement deletions from insertions, otherwise insertions would double count revisions moved from WorkItem to WorkItemHistory
    SET @insertedCount -= @@ROWCOUNT

    INSERT AnalyticsModel.tbl_WorkItem
    (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        WorkItemRevisionSK,
        WorkItemId,
        Revision,
        Watermark,
        Title,
        WorkItemType,
        ChangedDate,
        CreatedDate,
        [State],
        Reason,
        FoundIn,
        IntegrationBuild,
        ActivatedDate,
        Activity,
        BacklogPriority,
        BusinessValue,
        ClosedDate,
        Discipline,
        Issue,
        Priority,
        Rating,
        ResolvedDate,
        ResolvedReason,
        Risk,
        Severity,
        StackRank,
        TimeCriticality,
        Triage,
        ValueArea,
        DueDate,
        FinishDate,
        StartDate,
        TargetDate,
        Blocked,
        Committed,
        Escalate,
        FoundInEnvironment,
        HowFound,
        Probability,
        RequirementType,
        RequiresReview,
        RequiresTest,
        RootCause,
        SubjectMatterExpert1,
        SubjectMatterExpert2,
        SubjectMatterExpert3,
        TargetResolveDate,
        TaskType,
        UserAcceptanceTest,
        IsDeleted,
        AutomatedTestId,
        AutomatedTestName,
        AutomatedTestStorage,
        AutomatedTestType,
        AutomationStatus,
        ProjectSK,
        DateSK,
        AreaSK,
        IterationSK,
        CompletedWork,
        Effort,
        OriginalEstimate,
        RemainingWork,
        Size,
        StoryPoints,
        CreatedDateSK,
        ActivatedDateSK,
        ClosedDateSK,
        ResolvedDateSK,
        AssignedToUserSK,
        ChangedByUserSK,
        CreatedByUserSK,
        ActivatedByUserSK,
        ClosedByUserSK,
        ResolvedByUserSK,
        ParentWorkItemId,
        TeamFieldSK,
        TagNames,
        StateCategory,
        InProgressDate,
        InProgressDateSK,
        CompletedDate,
        CompletedDateSK,
        LeadTimeDays,
        CycleTimeDays,
        InternalForSnapshotHashJoin,
        AuthorizedDate,
        StateChangeDate,
        StateChangeDateSK,
        CommentCount
    )
    SELECT
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.WorkItemRevisionSK,
        s.WorkItemId,
        s.Revision,
        s.Watermark,
        s.Title,
        s.WorkItemType,
        s.ChangedDate,
        s.CreatedDate,
        s.[State],
        s.Reason,
        s.FoundIn,
        s.IntegrationBuild,
        s.ActivatedDate,
        s.Activity,
        s.BacklogPriority,
        s.BusinessValue,
        s.ClosedDate,
        s.Discipline,
        s.Issue,
        s.Priority,
        s.Rating,
        s.ResolvedDate,
        s.ResolvedReason,
        s.Risk,
        s.Severity,
        s.StackRank,
        s.TimeCriticality,
        s.Triage,
        s.ValueArea,
        s.DueDate,
        s.FinishDate,
        s.StartDate,
        s.TargetDate,
        s.Blocked,
        s.Committed,
        s.Escalate,
        s.FoundInEnvironment,
        s.HowFound,
        s.Probability,
        s.RequirementType,
        s.RequiresReview,
        s.RequiresTest,
        s.RootCause,
        s.SubjectMatterExpert1,
        s.SubjectMatterExpert2,
        s.SubjectMatterExpert3,
        s.TargetResolveDate,
        s.TaskType,
        s.UserAcceptanceTest,
        s.IsDeleted,
        s.AutomatedTestId,
        s.AutomatedTestName,
        s.AutomatedTestStorage,
        s.AutomatedTestType,
        s.AutomationStatus,
        s.ProjectSK,
        s.DateSK,
        s.AreaSK,
        s.IterationSK,
        s.CompletedWork,
        s.Effort,
        s.OriginalEstimate,
        s.RemainingWork,
        s.Size,
        s.StoryPoints,
        s.CreatedDateSK,
        s.ActivatedDateSK,
        s.ClosedDateSK,
        s.ResolvedDateSK,
        s.AssignedToUserSK,
        s.ChangedByUserSK,
        s.CreatedByUserSK,
        s.ActivatedByUserSK,
        s.ClosedByUserSK,
        s.ResolvedByUserSK,
        s.ParentWorkItemId,
        s.TeamFieldSK,
        s.TagNames,
        s.StateCategory,
        s.InProgressDate,
        s.InProgressDateSK,
        s.CompletedDate,
        s.CompletedDateSK,
        s.LeadTimeDays,
        s.CycleTimeDays,
        1 AS InternalForSnapshotHashJoin,
        s.AuthorizedDate,
        s.StateChangeDate,
        s.StateChangeDateSK,
        s.CommentCount
    FROM #WorkItemRevs AS s
    JOIN #WorkItemRevsAll sx
    ON sx.WorkItemId = s.WorkItemId
        AND sx.Revision = s.Revision
        AND sx.ExistingCurrent = 0
    WHERE s.IsCurrent = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount += @@ROWCOUNT

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            IsDeleted = s.IsDeleted,
            ChangedDate         = s.ChangedDate,
            DateSK              = AnalyticsInternal.func_GenDateSK(s.ChangedDate),
            InProgressDate      = s.InProgressDateRaw AT TIME ZONE @timeZone,
            InProgressDateSK    = AnalyticsInternal.func_GenDateSK(s.InProgressDateRaw AT TIME ZONE @timeZone),
            CompletedDate       = s.CompletedDateRaw AT TIME ZONE @timeZone,
            CompletedDateSK     = AnalyticsInternal.func_GenDateSK(s.CompletedDateRaw AT TIME ZONE @timeZone),
            LeadTimeDays        = IIF(t.CreatedDate > s.CompletedDateRaw, 0.0, DATEDIFF_BIG(second, t.CreatedDate, s.CompletedDateRaw) / 86400.0),
            CycleTimeDays       = IIF(s.InProgressDateRaw > s.CompletedDateRaw, 0.0, DATEDIFF_BIG(second, s.InProgressDateRaw, s.CompletedDateRaw) / 86400.0)
    FROM    AnalyticsModel.tbl_WorkItem t WITH (INDEX (IX_tbl_WorkItem_WorkItemIdRev)) -- allow use of row lock on NCI vs CCI segments
    JOIN    #WorkItemRevsAllChangedCurrent s
    ON      @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount += @@ROWCOUNT

    --------------------------------------------------------------------------------
    -- Update Custom tables
    --------------------------------------------------------------------------------
    CREATE TABLE #ChangedWorkItems
    (
        WorkItemId  INT NOT NULL,
        Revision    INT NOT NULL,
        PRIMARY KEY (WorkItemId, Revision)
    )

    INSERT #ChangedWorkItems (WorkItemId, Revision)
    EXEC AnalyticsInternal.prc_iiMergeWorkItemCustom
            @partitionId,
            @batchId,
            @batchDt,
            @triggerBatchIdStart,
            @triggerBatchIdEnd,
            @triggerWorkItemId

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId
    FROM    AnalyticsModel.tbl_WorkItem t WITH (INDEX (IX_tbl_WorkItem_WorkItemIdRev)) -- allow use of row lock on NCI vs CCI segments
    JOIN    #ChangedWorkItems s
    ON      @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
            AND t.AnalyticsBatchId <> @batchId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount += @@ROWCOUNT

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId
    FROM    AnalyticsModel.tbl_WorkItemHistory t WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemIdRev)) -- allow use of row lock on NCI vs CCI segments
    JOIN    #ChangedWorkItems s
    ON      @partitionId = t.PartitionId
            AND s.WorkItemId = t.WorkItemId
            AND s.Revision = t.Revision
            AND t.AnalyticsBatchId <> @batchId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount += @@ROWCOUNT

    -- finally cleanup reserved SKs - later to avoid long locks on table
    DELETE  wisk
    FROM    #WorkItemRevs wi
    JOIN    AnalyticsInternal.tbl_WorkItemRevisionReserved wisk WITH (ROWLOCK)
    ON      wisk.PartitionId = @partitionId
            AND wisk.WorkItemId = wi.WorkItemId
            AND wisk.Revision = wi.Revision
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    COMMIT TRAN

    DROP TABLE #WorkItemRevs
    DROP TABLE #RevsWithTag
    DROP TABLE #RevsWithConcatTags
    DROP TABLE #WorkItemRevsAll
    DROP TABLE #WorkItemRevsAllChangedHistory
    DROP TABLE #DiffRevsHistory
    DROP TABLE #WorkItemRevsAllChangedCurrent
    DROP TABLE #DiffRevsCurrent
    DROP TABLE #WorkItemStateCategoryChange

    RETURN 0
END

GO

