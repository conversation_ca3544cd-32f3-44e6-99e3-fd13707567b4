/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 082996B2A3706B99EE379F993CF1820B144BC42A
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageCollection_InternalCollectionTimeZone_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    ;WITH Src AS
    (
        SELECT  @partitionId AS PartitionId, MAX(ISNULL(tz.name, 'UTC')) AS TimeZone
        FROM    AnalyticsStage.tbl_Collection c
        LEFT JOIN sys.time_zone_info tz
        ON      c.TimeZone = tz.Name
        WHERE   c.PartitionId = @partitionId
    )
    MERGE AnalyticsInternal.tbl_CollectionTimeZone AS t
    USING Src AS s
    ON  (t.PartitionId = s.PartitionId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT s.TimeZone
        INTERSECT
        SELECT t.TimeZone
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt,
        AnalyticsBatchId = @batchId,
        TimeZone = s.TimeZone
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        TimeZone
        )
    VALUES (
        s.PartitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.TimeZone
        )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = 1
    SET @deletedCount = 0
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    RETURN 0
END

GO

