/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F474640FDE525A162EC53E4703DCB578673326A5
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_ModelTeamField_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)
    DECLARE @triggerWorkItemId TABLE
    (
        System_Id INT NOT NULL PRIMARY KEY,
        [Count] INT NOT NULL
    )

    IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
                    AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = 0
        SET @endState = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 'dense', '')
    END
    ELSE -- use NCI
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 0, 1)
        SET @endState = IIF(@complete = 0 AND @endStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
    END

    -- Determine if workitems have team field values in the model table. If so, insert.
    CREATE TABLE #SrcA
    (
        ProjectId               UNIQUEIDENTIFIER    NOT NULL,
        TeamFieldReferenceName  NVARCHAR(256)       COLLATE DATABASE_DEFAULT NOT NULL,
        AreaId                  UNIQUEIDENTIFIER    NOT NULL
    )

    CREATE TABLE #SrcFV
    (
        ProjectId               UNIQUEIDENTIFIER    NOT NULL,
        TeamFieldReferenceName  NVARCHAR(256)       COLLATE DATABASE_DEFAULT NOT NULL,
        TeamFieldValue          NVARCHAR(4000)      COLLATE DATABASE_DEFAULT NOT NULL,
    )

    INSERT  #SrcA (ProjectId, TeamFieldReferenceName, AreaId)
    SELECT  DISTINCT r.System_ProjectGuid AS ProjectId,
            'System.AreaPath' AS TeamFieldReferenceName,
            r.System_AreaGuid AS AreaId
    FROM    @triggerWorkItemId t
    INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision r WITH (INDEX (CI_tbl_WorkItemRevision))
    ON      r.PartitionId = @partitionId
            AND r.System_Id = t.System_Id
    LEFT JOIN AnalyticsModel.tbl_Project p
    ON      p.PartitionId = r.PartitionId
            AND p.ProjectId = r.System_ProjectGuid
    WHERE   r.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND r.System_ProjectGuid IS NOT NULL
            AND r.System_AreaGuid IS NOT NULL
            AND ISNULL(p.TeamFieldReferenceName, 'System.AreaPath') = 'System.AreaPath'
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  #SrcFV (ProjectId, TeamFieldReferenceName, TeamFieldValue)
    SELECT  DISTINCT p.ProjectId,
            p.TeamFieldReferenceName,
            x.ValueString AS TeamFieldValue -- TODO - do I need to support other types?
    FROM    @triggerWorkItemId t
    INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision r WITH (INDEX (CI_tbl_WorkItemRevision))
    ON      r.PartitionId = @partitionId
            AND r.System_Id = t.System_Id
    JOIN    AnalyticsModel.tbl_Project p
    ON      p.PartitionId = r.PartitionId
            AND p.ProjectId = r.System_ProjectGuid
    LEFT JOIN AnalyticsInternal.tbl_Fields f
    ON      f.PartitionId = p.PartitionId
            AND f.TableName = 'WorkItemRevision'
            AND f.FieldName = p.TeamFieldSourceFieldName
    LEFT LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x
    ON      x.PartitionId = f.PartitionId
            AND x.FieldSK = f.FieldSK
            AND x.System_Id = r.System_Id
            AND x.System_Rev = r.System_Rev
    WHERE   r.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND p.TeamFieldReferenceName <> 'System.AreaPath'
            AND x.ValueString IS NOT NULL
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  s
    FROM    #SrcA s
    JOIN    AnalyticsModel.tbl_TeamField AS t
    ON      t.PartitionId = @partitionId
            AND t.ProjectId = s.ProjectId
            AND t.TeamFieldReferenceName = s.TeamFieldReferenceName
            AND t.AreaId = s.AreaId
            AND t.TeamFieldReferenceName = 'System.AreaPath' -- allowed use of filtered index
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  s
    FROM    #SrcFV s
    JOIN    AnalyticsModel.tbl_TeamField AS t
    ON      t.PartitionId = @partitionId
            AND t.ProjectId = s.ProjectId
            AND t.TeamFieldReferenceName = s.TeamFieldReferenceName
            AND t.TeamFieldValue = s.TeamFieldValue
            AND t.TeamFieldReferenceName <> 'System.AreaPath' -- allowed use of filtered index
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    BEGIN TRAN

    INSERT AnalyticsModel.tbl_TeamField
    (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        ProjectId,
        TeamFieldReferenceName,
        AreaId
    )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.ProjectId,
            s.TeamFieldReferenceName,
            s.AreaId
    FROM    #SrcA s
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT

    INSERT AnalyticsModel.tbl_TeamField
    (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        ProjectId,
        TeamFieldReferenceName,
        TeamFieldValue
    )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.ProjectId,
            s.TeamFieldReferenceName,
            s.TeamFieldValue
    FROM    #SrcFV s
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount += @@ROWCOUNT

    COMMIT TRAN

    DROP TABLE #SrcA
    DROP TABLE #SrcFV

    RETURN 0
END

GO

