CREATE TABLE [AnalyticsModel].[tbl_Tag] (
    [PartitionId]          INT              NOT NULL,
    [AnalyticsCreatedDate] DATETIME         NOT NULL,
    [AnalyticsUpdatedDate] DATETIME         NOT NULL,
    [AnalyticsBatchId]     BIGINT           NOT NULL,
    [TagSK]                INT              IDENTITY (1, 1) NOT NULL,
    [TagId]                UNIQUEIDENTIFIER NULL,
    [TagName]              NVARCHAR (400)   NULL,
    [IsDeleted]            BIT              NOT NULL,
    [ProjectSK]            UNIQUEIDENTIFIER NULL
);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_Tag]
    ON [AnalyticsModel].[tbl_Tag]([PartitionId] ASC, [TagSK] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_Tag_TagId]
    ON [AnalyticsModel].[tbl_Tag]([PartitionId] ASC, [TagId] ASC, [ProjectSK] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_Tag_AnalyticsBatchId]
    ON [AnalyticsModel].[tbl_Tag]([PartitionId] ASC, [AnalyticsBatchId] ASC);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_Tag_TagName]
    ON [AnalyticsModel].[tbl_Tag]([PartitionId] ASC, [ProjectSK] ASC, [TagName] ASC)
    INCLUDE([IsDeleted]);


GO

