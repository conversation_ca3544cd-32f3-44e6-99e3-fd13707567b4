/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 92FE1E6A5AAD2FE6140CECBC814616767B20ED16
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Any_InternalWorkItemTypeState_BatchReplace
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)

    DECLARE @changes TABLE
    (
        MergeAction     NVARCHAR(10)
    );

    CREATE TABLE #State
    (
        PartitionId INT NOT NULL,
        ProjectSK UNIQUEIDENTIFIER,
        ProcessId UNIQUEIDENTIFIER NOT NULL,
        WorkItemType NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        [State] NVARCHAR(128) COLLATE DATABASE_DEFAULT NULL,
        StateCategory NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        StateCategoryId INT
    )

    -- Shred all states from the entire process table
    ; WITH ProcessState AS
    (
        SELECT
            p.PartitionId,
            p.ProcessId,
            ps.Item.value('WorkItemTypeName[1]','nvarchar(256)') as WorkItemType,
            ps.Item.value('Name[1]','nvarchar(256)') as [State],
            ps.Item.value('CategoryName[1]','nvarchar(256)') as StateCategory,
            ps.Item.value('CategoryId[1]','int') as StateCategoryId
        FROM AnalyticsStage.tbl_Process p
        CROSS APPLY p.States.nodes('//Item') AS ps(Item)
        WHERE PartitionId = @partitionId
    )
    INSERT INTO #State
    (
        PartitionId,
        ProcessId,
        WorkItemType,
        [State],
        StateCategory,
        StateCategoryId,
        ProjectSK
    )
    -- Join from project
    SELECT
        p.PartitionId,
        p.ProcessId,
        p.WorkItemType,
        p.[State],
        p.StateCategory,
        p.StateCategoryId,
        pj.ProjectGuid AS ProjectSK
    FROM AnalyticsStage.tbl_Project pj
    JOIN ProcessState p
        ON p.PartitionId = pj.PartitionId
        AND p.ProcessId = pj.ProcessId
    WHERE p.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    ;WITH Tgt AS
    (
        -- MERGE has a bug where it tries accessing entities in all partitions (even offline ones) for the target table.
        -- To overcome this problem the target table has to be pre-filtered by correct partition id.
        SELECT * FROM AnalyticsInternal.tbl_WorkItemTypeState WHERE PartitionId = @partitionId
    )
    MERGE TOP (@batchSizeMax) Tgt AS t
    USING #State AS s
    ON (t.PartitionId = s.PartitionId
        AND t.ProjectSK = s.ProjectSK
        AND t.WorkItemType = s.WorkItemType
        AND t.[State] = s.[State]
        )
    WHEN MATCHED AND NOT EXISTS
    (
        SELECT
        s.ProcessId,
        s.StateCategory,
        s.StateCategoryId
        INTERSECT
        SELECT
        t.ProcessId,
        t.StateCategory,
        t.StateCategoryId
    )
    THEN
    UPDATE SET
        AnalyticsUpdatedDate        = @batchDt
        , AnalyticsBatchId          = @batchId
        , ProcessId                 = s.ProcessId
        , StateCategory             = s.StateCategory
        , StateCategoryId           = s.StateCategoryId
    WHEN NOT MATCHED BY TARGET THEN
    INSERT
    (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        ProcessId,
        WorkItemType,
        [State],
        StateCategory,
        StateCategoryId,
        ProjectSK
    )
    VALUES
    (
        s.PartitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.ProcessId,
        s.WorkItemType,
        s.[State],
        s.StateCategory,
        s.StateCategoryId,
        s.ProjectSK
    )
    WHEN NOT MATCHED BY SOURCE
        AND (t.ProjectSK IN (SELECT ProjectSK FROM #State)
            OR t.ProjectSK NOT IN (SELECT ProjectGuid FROM AnalyticsStage.tbl_Project WHERE PartitionId = @partitionId))
    THEN DELETE
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    DROP TABLE #State

    RETURN 0
END

GO

