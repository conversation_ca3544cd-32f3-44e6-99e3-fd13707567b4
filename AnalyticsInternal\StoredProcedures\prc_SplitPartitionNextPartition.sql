/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B8D903264C61ED04EC8EF09672EB4AF02D344748
CREATE PROCEDURE AnalyticsInternal.prc_SplitPartitionNextPartition
    @partitionId    INT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(2048)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @maxPartitionBoundry INT
    DECLARE @newBoundry INT
    DECLARE @partitionStep INT = 100

    DECLARE @partitionFunc NVARCHAR(256)
    DECLARE @partitionScheme NVARCHAR(256)

    DECLARE @partitionCursor CURSOR

    -- Clone custom tables
    SET @partitionCursor = CURSOR LOCAL FORWARD_ONLY STATIC FOR
    SELECT DISTINCT PartitionFunction, PartitionScheme FROM AnalyticsInternal.func_GetTablePartitioningMap()

    OPEN @partitionCursor
    FETCH NEXT FROM @partitionCursor INTO @partitionFunc, @partitionScheme;
    WHILE @@FETCH_STATUS = 0
    BEGIN
        SELECT @maxPartitionBoundry = CAST(MAX(value) AS INT) FROM sys.partition_functions pf
        JOIN sys.partition_range_values prv ON pf.function_id=prv.function_id
        WHERE name = @partitionFunc

        IF @maxPartitionBoundry IS NOT NULL AND @partitionId >= (@maxPartitionBoundry - @partitionStep)
        BEGIN
            -- When asked for far away partition => split on next planned step after selected partition
            -- When asked for partition close to the existing boundry => split on next step after boundry
            SET @newBoundry = IIF(@partitionId > @maxPartitionBoundry, (@partitionId / @partitionStep + 1) * @partitionStep, @maxPartitionBoundry + @partitionStep)
            BEGIN TRY
                DECLARE @cmd NVARCHAR(MAX) ='
                ALTER PARTITION FUNCTION '+@partitionFunc+'() SPLIT RANGE(' + CAST(@newBoundry AS VARCHAR(10)) +')

                ALTER PARTITION SCHEME '+@partitionScheme+'
                NEXT USED [PRIMARY]'

                EXEC(@cmd)
            END TRY
            BEGIN CATCH
                SET @status = 1670012
                SET @errorMessage = dbo.func_FormatErrorMessage(@status, ERROR_MESSAGE(), ERROR_LINE())
                SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 10, -1, @procedureName, @partitionScheme, @newBoundry, @errorMessage)
            END CATCH
        END

       FETCH NEXT FROM @partitionCursor INTO @partitionFunc, @partitionScheme;
    END

    CLOSE @partitionCursor
    DEALLOCATE @partitionCursor
END

GO

