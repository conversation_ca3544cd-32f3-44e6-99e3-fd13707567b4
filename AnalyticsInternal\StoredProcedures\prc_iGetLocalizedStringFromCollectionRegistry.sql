/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 741A5279B5EF771D76716829FE4B363509DD6F1F
--Changing the name to regenerate
CREATE PROCEDURE AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry
    @partitionId     INT,
    @key             NVARCHAR(256),
    @defaultValue    NVARCHAR(230),
    @localizedValue  NVARCHAR(230) OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @registryQueryResult    NVARCHAR(256)

    SET @localizedValue = @defaultValue
    DECLARE @registrykey                NVARCHAR(256) = REPLACE(N'#' + '/Service/Analytics/Localization/MessageKeys/' + @key + '/' , '/', '\')

    EXEC @status = prc_pQueryRegistry @partitionId = @partitionId,
                                    @key = @registrykey,
                                    @value = @registryQueryResult OUTPUT

    IF (@status = 0 AND @registryQueryResult IS NOT NULL)
    BEGIN
        SET @localizedValue = @registryQueryResult
    END

    RETURN 0
END

GO

