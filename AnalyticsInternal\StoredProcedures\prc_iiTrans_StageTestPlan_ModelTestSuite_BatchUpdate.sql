/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: ABC45FB19D02A6AD3E42534D38E47B231BB0C4C4
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestPlan_ModelTestSuite_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #ChangedTestPlans
    (
        TestPlanId    INT NOT NULL,
        ProjectGuid   UNIQUEIDENTIFIER NOT NULL,
        TestPlanState TINYINT

        INDEX IX_ChangedTestPlans_TestPlanId_ProjectGuid UNIQUE CLUSTERED (TestPlanId, ProjectGuid)
    )

    INSERT   #ChangedTestPlans (TestPlanId, ProjectGuid, TestPlanState)
    SELECT   TOP (@batchSizeMax) WITH TIES p.TestPlanId, p.ProjectGuid, p.State
    FROM     AnalyticsStage.tbl_TestPlan p WITH (INDEX (IX_tbl_TestPlan_AxBatchIdChanged), FORCESEEK)
    WHERE    p.PartitionId = @partitionId
             AND p.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
             AND p.TestPlanId > ISNULL(@stateData, 0)
    ORDER BY p.TestPlanId
    OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete     = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT TOP 1 TestPlanId FROM #ChangedTestPlans ORDER BY TestPlanId DESC)

    -- Update test plan state
    UPDATE t
    SET    AnalyticsUpdatedDate = @batchDt,
           AnalyticsBatchId     = @batchId,
           TestPlanState        = s.TestPlanState,
           IsDeleted            = (t.IsDeleted | IIF(s.TestPlanState = 255 , 1, 0))
    FROM   #ChangedTestPlans s
    JOIN   AnalyticsModel.tbl_TestSuite t
    ON     t.PartitionId = @partitionId
           AND t.ProjectSK = s.ProjectGuid
           AND t.TestPlanId = s.TestPlanId
    WHERE  NOT EXISTS
           (
                SELECT
                t.TestPlanState
                INTERSECT
                SELECT
                s.TestPlanState
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @updatedCount  = @@ROWCOUNT

    DROP TABLE #ChangedTestPlans

    RETURN 0
END

GO

