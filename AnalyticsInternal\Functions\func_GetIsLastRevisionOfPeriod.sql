/******************************************************************************************************
** Warning: the contents of this function are critical to its functioning.
** Modifying the contents of this function could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 1EBE45A107DBD46434802DA8E4FE10B08402F9C9
CREATE FUNCTION AnalyticsInternal.func_GetIsLastRevisionOfPeriod(@changedDate DATE, @revisedDate DATE)
RETURNS INT
AS
BEGIN
    RETURN IIF(@revisedDate IS NULL,
        2047,
          IIF(DATEDIFF(DAY, @changedDate, @revisedDate) > 0, 1, 0)
        | IIF(DATEDIFF(WEEK, DATEADD(DAY, -0, @changedDate), DATEADD(DAY, -0, @revisedDate)) > 0, 128, 0)
        | IIF(DATEDIFF(WEEK, DATEADD(DAY, -1, @changedDate), DATEADD(DAY, -1, @revisedDate)) > 0, 2, 0)
        | IIF(DATEDIFF(WEEK, DATEADD(DAY, -2, @changedDate), DATEADD(DAY, -2, @revisedDate)) > 0, 4, 0)
        | IIF(DATEDIFF(WEEK, DATEADD(DAY, -3, @changedDate), DATEADD(DAY, -3, @revisedDate)) > 0, 8, 0)
        | IIF(DATEDIFF(WEEK, DATEADD(DAY, -4, @changedDate), DATEADD(DAY, -4, @revisedDate)) > 0, 16, 0)
        | IIF(DATEDIFF(WEEK, DATEADD(DAY, -5, @changedDate), DATEADD(DAY, -5, @revisedDate)) > 0, 32, 0)
        | IIF(DATEDIFF(WEEK, DATEADD(DAY, -6, @changedDate), DATEADD(DAY, -6, @revisedDate)) > 0, 64, 0)
        | IIF(DATEDIFF(MONTH,   @changedDate, @revisedDate) > 0, 256, 0)
        | IIF(DATEDIFF(QUARTER, @changedDate, @revisedDate) > 0, 512, 0)
        | IIF(DATEDIFF(YEAR,    @changedDate, @revisedDate) > 0, 1024, 0)
    )
END

GO

GRANT EXECUTE
    ON OBJECT::[AnalyticsInternal].[func_GetIsLastRevisionOfPeriod] TO [VSODIAGROLE]
    AS [dbo];


GO

