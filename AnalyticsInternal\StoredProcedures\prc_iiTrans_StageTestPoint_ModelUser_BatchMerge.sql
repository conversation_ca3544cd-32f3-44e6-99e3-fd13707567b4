/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 500E60FADE65CA68D5B9C17C09672EBEAB13230F
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestPoint_ModelUser_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    -- Mentioning primary key to avoid sorting #ChangedTestPlans when joining with other tables
    -- This should be safe, as for a partitionId, point Ids are unique.
    CREATE TABLE #ChangedTestPoints
    (
        TestPointId        INT NOT NULL PRIMARY KEY
    )

    IF (@subBatchCount <= 1)
    BEGIN
        -- Initial batches in case of sub-batching, mostly @triggerBatchIdStart and @triggerBatchIdEnd
        -- don't differ much. Go with IX_tbl_TestPoint_AxBatchIdChanged
        INSERT     #ChangedTestPoints (TestPointId)
        SELECT TOP (@batchSizeMax) p.TestPointId
        FROM       AnalyticsStage.tbl_TestPoint p WITH (INDEX (IX_tbl_TestPoint_AxBatchIdChanged))
        WHERE      p.PartitionId = @partitionId
                   AND p.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                   AND p.TestPointId > ISNULL(@stateData, 0)
        ORDER BY   p.TestPointId

        SET @complete     = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPoints)
    END
    ELSE
    BEGIN
        -- Subsequent batches in case of sub-batching, mostly @triggerBatchIdStart and @triggerBatchIdEnd
        -- cover a lot of batches in between. Go with CI_tbl_TestPoint to avoid expensive sorts
        INSERT     #ChangedTestPoints (TestPointId)
        SELECT TOP (@batchSizeMax) p.TestPointId
        FROM       AnalyticsStage.tbl_TestPoint p WITH (INDEX (CI_tbl_TestPoint))
        WHERE      p.PartitionId = @partitionId
                   AND p.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                   AND p.TestPointId > ISNULL(@stateData, 0)
        ORDER BY   p.TestPointId

        SET @complete     = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endStateData = (SELECT MAX(TestPointId) FROM #ChangedTestPoints)
    END

    DECLARE @localizedUnknown   NVARCHAR(230)
    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_UNKNOWN',
                                                            @defaultValue = 'Unknown',
                                                            @localizedValue =  @localizedUnknown OUTPUT

    DECLARE @LatestUsersRaw TABLE
    (
        UserId                  UNIQUEIDENTIFIER    NOT NULL,
        UserDisplayName         NVARCHAR(256)       NULL,
        ChangedDate             DATETIME2           NULL
    )

    DECLARE @LatestUsers TABLE
    (
        UserId                  UNIQUEIDENTIFIER    NOT NULL,
        UserName                NVARCHAR(256)       NULL,
        UserEmail               NVARCHAR(256)       NULL,
        UserSK                  UNIQUEIDENTIFIER    NOT NULL,
        ChangedDate             DATETIME2           NULL
    )

    INSERT @LatestUsersRaw
    SELECT  CAST(JSON_VALUE(Tester, '$.Id') AS UNIQUEIDENTIFIER) AS UserId,
            JSON_VALUE(Tester, '$.DisplayName') AS UserDisplayName,
            p.ChangedDate As ChangedDate
    FROM    AnalyticsStage.tbl_TestPoint p WITH (INDEX (CI_tbl_TestPoint), FORCESEEK)
    JOIN    #ChangedTestPoints cp
    ON      cp.TestPointId = p.TestPointId
    WHERE   p.PartitionId = @partitionId
    AND     p.Tester IS NOT NULL

    ; WITH UserNameEmailSplit AS
    (
        SELECT  AnalyticsInternal.func_GetUserSK(UserId, UserDisplayName) AS UserId,
                UserDisplayName,
                ISNULL(LTRIM(RTRIM([0])), @localizedUnknown) AS UserName,
                ISNULL(LTRIM(RTRIM(substring([1], 0, len([1])))), @localizedUnknown) AS UserEmail,
                ChangedDate
        FROM
        (
            SELECT      *
            FROM        @LatestUsersRaw
            CROSS APPLY AnalyticsInternal.func_SplitString(UserDisplayName, '<')
        ) AS source
        PIVOT (MAX(value) FOR ordinal in ([0],[1])) AS pivotTable
    )
    INSERT   @LatestUsers (UserSK, UserId, UserName, UserEmail, ChangedDate)
    SELECT   UserId,
             UserId,
             UserName,
             CASE WHEN CHARINDEX('@', UserEmail) = 0 THEN @localizedUnknown ELSE UserEmail END AS UserEmail,
             ChangedDate AS ChangedDate
    FROM
    (
        SELECT *,
            ROW_NUMBER() OVER (PARTITION BY UserId ORDER BY ChangedDate DESC, UserDisplayName ASC) AS RN
        FROM UserNameEmailSplit
    ) core
    WHERE RN = 1

    MERGE AnalyticsModel.tbl_User t
    USING @LatestUsers s
    ON (t.PartitionId = @partitionId AND s.UserSK = t.UserSK)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
         t.UserName
        , t.UserEmail
        , t.UserId
        INTERSECT
        SELECT
        s.UserName
        , s.UserEmail
        , s.UserId
    ) AND ISNULL(t.ChangedDate, '1900-01-01') <= s.ChangedDate THEN
    UPDATE SET
        t.AnalyticsUpdatedDate    = @batchDt,
        t.AnalyticsBatchId        = @batchId,
        t.UserName                = s.UserEmail,
        t.UserEmail               = s.UserEmail,
        t.UserId                  = s.UserId,
        t.ChangedDate             = s.ChangedDate
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        UserSK,
        UserId,
        UserName,
        UserEmail,
        ChangedDate
    )
    VALUES(
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.UserSK,
        s.UserId,
        s.UserName,
        s.UserEmail,
        s.ChangedDate
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    DROP TABLE #ChangedTestPoints

    RETURN 0
END

GO

