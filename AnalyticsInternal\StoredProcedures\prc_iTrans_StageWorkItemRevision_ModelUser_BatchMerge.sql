/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 814DEFFD386CB08989291EE95AE60239D46BDF2A
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_ModelUser_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @localizedUnknown   NVARCHAR(230)

    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_UNKNOWN',
                                                            @defaultValue = 'Unknown',
                                                            @localizedValue =  @localizedUnknown OUTPUT

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)
    DECLARE @triggerWorkItemId TABLE
    (
        System_Id INT NOT NULL PRIMARY KEY,
        [Count] INT NOT NULL
    )

    IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
                    AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = 0
        SET @endState = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 'dense', '')
    END
    ELSE -- use NCI
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 0, 1)
        SET @endState = IIF(@complete = 0 AND @endStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
    END

    DECLARE @LatestUsersRaw TABLE
    (
        UserId                  UNIQUEIDENTIFIER    NOT NULL,
        UserDisplayName         NVARCHAR(256)       NULL,
        ChangedDate             DATETIME2           NULL
    )

    DECLARE @LatestUsers TABLE
    (
        UserId                  UNIQUEIDENTIFIER    NOT NULL,
        UserName                NVARCHAR(256)       NULL,
        UserEmail               NVARCHAR(256)       NULL,
        UserSK                  UNIQUEIDENTIFIER    NOT NULL,
        ChangedDate             DATETIME2           NULL
    )

    ;WITH UserIdsAndDisplayNames
    AS
    (
        SELECT  System_ChangedDate,
                CAST(JSON_VALUE(ValueObject, '$.Id') AS UNIQUEIDENTIFIER) AS UserId,
                JSON_VALUE(ValueObject, '$.DisplayName') AS UserDisplayName
        FROM    @triggerWorkItemId tid
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision r WITH (INDEX (CI_tbl_WorkItemRevision))
        ON      r.PartitionId = @partitionId
                AND r.System_Id = tid.System_Id
                AND r.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x WITH (INDEX (CI_tbl_WorkItemRevisionExtended))
        ON      x.PartitionId = @partitionId
                AND x.System_Id = r.System_Id
                AND x.System_Rev = r.System_Rev
        -- CONSIDER/TODO (User Story 1022427): use ProcessFields table to ensure that only known indenity fields will be written to WorkItemUser table
        WHERE   ValueObject IS NOT NULL
                AND ISJSON(ValueObject) > 0
    ),
    AllUsers AS
    (
        SELECT  AnalyticsInternal.func_GetUserSK(UserId, UserDisplayName) AS UserId,
                UserDisplayName,
                System_ChangedDate
        FROM    UserIdsAndDisplayNames
        WHERE   UserId IS NOT NULL

        UNION ALL

        SELECT  AnalyticsInternal.func_GetUserSK(UserId, UserDisplayName) AS UserId,
                UserDisplayName,
                System_ChangedDate
        FROM    @triggerWorkItemId tid
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision r WITH (INDEX (CI_tbl_WorkItemRevision))
        ON      r.PartitionId = @partitionId
                AND r.System_Id = tid.System_Id
                AND r.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        CROSS APPLY
        (
            VALUES  (CAST(JSON_VALUE(System_AssignedTo, '$.Id') AS UNIQUEIDENTIFIER), JSON_VALUE(System_AssignedTo, '$.DisplayName')),
                    (CAST(JSON_VALUE(System_ChangedBy, '$.Id') AS UNIQUEIDENTIFIER), JSON_VALUE(System_ChangedBy, '$.DisplayName')),
                    (CAST(JSON_VALUE(System_CreatedBy, '$.Id') AS UNIQUEIDENTIFIER), JSON_VALUE(System_CreatedBy, '$.DisplayName')),
                    (CAST(JSON_VALUE(Microsoft_VSTS_Common_ActivatedBy, '$.Id') AS UNIQUEIDENTIFIER), JSON_VALUE(Microsoft_VSTS_Common_ActivatedBy, '$.DisplayName')),
                    (CAST(JSON_VALUE(Microsoft_VSTS_Common_ClosedBy, '$.Id') AS UNIQUEIDENTIFIER), JSON_VALUE(Microsoft_VSTS_Common_ClosedBy, '$.DisplayName')),
                    (CAST(JSON_VALUE(Microsoft_VSTS_Common_ResolvedBy, '$.Id') AS UNIQUEIDENTIFIER), JSON_VALUE(Microsoft_VSTS_Common_ResolvedBy, '$.DisplayName'))
        ) C (UserId, UserDisplayName)
        WHERE   UserId IS NOT NULL
    )
    INSERT  @LatestUsersRaw (UserId, UserDisplayName, ChangedDate)
    SELECT  UserId,
            UserDisplayName,
            System_ChangedDate
    FROM
    (
        SELECT  *,
                ROW_NUMBER() OVER (PARTITION BY UserId ORDER BY System_ChangedDate DESC, UserDisplayName ASC) AS RN
        FROM    AllUsers
    ) core
    WHERE   RN = 1
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), NO_PERFORMANCE_SPOOL)

    ; WITH UserNameEmailSplit AS
    (
        SELECT  AnalyticsInternal.func_GetUserSK(UserId, UserDisplayName) AS UserId,
                ChangedDate,
                UserDisplayName,
                ISNULL(LTRIM(RTRIM([0])), @localizedUnknown) AS UserName,
                ISNULL(LTRIM(RTRIM(substring([1], 0, len([1])))), @localizedUnknown) AS UserEmail
        FROM
        (
            SELECT      *
            FROM        @LatestUsersRaw
            CROSS APPLY AnalyticsInternal.func_SplitString(UserDisplayName, '<')
        ) AS source
        PIVOT (MAX(value) FOR ordinal in ([0],[1])) AS pivotTable
    )
    INSERT  @LatestUsers (UserSK, UserId, UserName, UserEmail, ChangedDate)
    SELECT  UserId,
            UserId,
            UserName,
            CASE WHEN CHARINDEX('@', UserEmail) = 0 THEN @localizedUnknown ELSE UserEmail END AS UserEmail,
            ChangedDate
    FROM    UserNameEmailSplit

    BEGIN TRAN

    -- Replace only by newer user
    UPDATE  t
    SET     AnalyticsUpdatedDate    = @batchDt,
            AnalyticsBatchId        = @batchId,
            UserId                  = s.UserId,
            UserName                = s.UserName,
            UserEmail               = s.UserEmail,
            ChangedDate             = s.ChangedDate
    FROM    AnalyticsModel.tbl_User t
    JOIN    @LatestUsers s
    ON      t.UserSK = s.UserSK
            AND ISNULL(t.ChangedDate, '1900-01-01') <= s.ChangedDate
            AND NOT EXISTS (
                    SELECT s.UserName
                        , s.UserEmail
                        , s.UserId
                    INTERSECT
                    SELECT t.UserName
                        , t.UserEmail
                        , t.UserId
                    )
    WHERE   t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    DELETE  t
    FROM    @LatestUsers t
    JOIN    AnalyticsModel.tbl_User s
    ON      t.UserSK = s.UserSK
            AND s.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT  AnalyticsModel.tbl_User
            (
            PartitionId,
            AnalyticsBatchId,
            AnalyticsCreatedDate,
            AnalyticsUpdatedDate,
            UserSK,
            UserId,
            UserName,
            UserEmail,
            ChangedDate
            )
    SELECT  @partitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.UserSK,
            s.UserId,
            s.UserName,
            s.UserEmail,
            s.ChangedDate
    FROM    @LatestUsers AS s
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT

    COMMIT TRAN

    RETURN 0
END

GO

