/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 7C9E8F74903C184600DC308E52F524D132230A23
CREATE PROCEDURE AnalyticsInternal.prc_iGetWorkItemRevisionSubBatchIdsFromModel
    @partitionId INT,
    @workItemIdStart BIGINT,
    @batchSizeMax INT,
    @subBatchWorkItemIdStart BIGINT OUTPUT,
    @subBatchWorkItemIdEnd BIGINT OUTPUT,
    @complete BIT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    SET @subBatchWorkItemIdStart = ISNULL(@workItemIdStart, 0)

    -- Get Ids for up to @batchSizeMax => we will deal with ~@batchSizeMax revisions not @batchSizeMax*by arbitrary number
    DECLARE @endStateData BIGINT
    SELECT @endStateData = MAX(WorkItemId)
    FROM
    (
        SELECT  TOP (@batchSizeMax) WorkItemId
        FROM    AnalyticsInternal.vw_WorkItemRevisionSK
        WHERE   PartitionId = @partitionId
                AND WorkItemId >= @subBatchWorkItemIdStart
        ORDER   BY WorkItemId
    ) T
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN, @subBatchWorkItemIdStart = 0))

    SET @subBatchWorkItemIdEnd = ISNULL(@endStateData, 0)

    DECLARE @maxWorkItemId INT
    SELECT  @maxWorkItemId = MAX(WorkItemId)
    FROM    AnalyticsInternal.vw_WorkItemRevisionSK
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@subBatchWorkItemIdEnd >= ISNULL(@maxWorkItemId, 0), 1, 0)
END

GO

