CREATE TABLE [AnalyticsModel].[tbl_Test] (
    [PartitionId]            INT              NOT NULL,
    [AnalyticsCreatedDate]   DATETIME         NOT NULL,
    [AnalyticsUpdatedDate]   DATETIME         NOT NULL,
    [AnalyticsBatchId]       BIGINT           NOT NULL,
    [TestSK]                 INT              IDENTITY (1, 1) NOT NULL,
    [TestCaseReferenceId]    INT              NOT NULL,
    [FullyQualifiedTestName] NVARCHAR (512)   NULL,
    [TestOwner]              NVARCHAR (256)   NULL,
    [ContainerName]          NVARCHAR (512)   NULL,
    [Priority]               INT              NULL,
    [ProjectSK]              UNIQUEIDENTIFIER NULL,
    [TestName]               NVARCHAR (256)   NULL,
    [DataSourceId]           INT              CONSTRAINT [CK_AnalyticsModel_tbl_Test_DataSourceIdNotNullDefault] DEFAULT ((0)) NOT NULL
) ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_tbl_Test_TestCaseReferenceId]
    ON [AnalyticsModel].[tbl_Test]([PartitionId] ASC, [TestCaseReferenceId] ASC, [DataSourceId] ASC)
    INCLUDE([TestSK]) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_Test]
    ON [AnalyticsModel].[tbl_Test]
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

