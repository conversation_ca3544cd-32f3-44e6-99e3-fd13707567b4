CREATE TABLE [AnalyticsInternal].[tbl_Fields] (
    [PartitionId]        INT              NOT NULL,
    [FieldSK]            INT              IDENTITY (1, 1) NOT NULL,
    [TableName]          VARCHAR (64)     NOT NULL,
    [FieldName]          NVARCHAR (256)   NOT NULL,
    [ExtensionId]        UNIQUEIDENTIFIER NULL,
    [ExtensionFieldName] NVARCHAR (256)   NULL,
    [FieldDisplayName]   NVARCHAR (64)    NULL,
    [FieldDescription]   NVARCHAR (255)   NULL,
    [FieldType]          VARCHAR (64)     NULL,
    [FieldNullable]      BIT              NULL,
    [CreateDate]         DATETIME         NOT NULL,
    [UpdateDate]         DATETIME         NOT NULL
);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_Fields_ExtendedFieldName]
    ON [AnalyticsInternal].[tbl_Fields]([PartitionId] ASC, [TableName] ASC, [ExtensionFieldName] ASC);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_Fields]
    ON [AnalyticsInternal].[tbl_Fields]([PartitionId] ASC, [FieldSK] ASC);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_Fields_FieldName]
    ON [AnalyticsInternal].[tbl_Fields]([PartitionId] ASC, [TableName] ASC, [FieldName] ASC);


GO

