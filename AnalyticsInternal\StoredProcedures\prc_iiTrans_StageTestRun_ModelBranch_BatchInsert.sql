/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: DE43AB4754B04933245A9229D1770D2782727FBF
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestRun_ModelBranch_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    CREATE TABLE #Branch
    (
        RepositoryId                    NVARCHAR(400)    COLLATE DATABASE_DEFAULT NOT NULL,
        BranchName                      NVARCHAR(400)    COLLATE Latin1_General_BIN2 NOT NULL,
        RepositoryVstsId                UNIQUEIDENTIFIER NULL,
        RepositoryUrl                   NVARCHAR(400)    COLLATE DATABASE_DEFAULT NULL
    )

    INSERT  #Branch (RepositoryId, BranchName)
    SELECT  DISTINCT RepositoryId, BranchName
    FROM    AnalyticsStage.tbl_TestRun WITH (INDEX (IX_tbl_TestRun_AxBatchIdChanged), FORCESEEK)
    WHERE   PartitionId = @partitionId
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND RepositoryId IS NOT NULL
            AND BranchName IS NOT NULL
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- TODO - how soon can we populate url for vsts repos?
    UPDATE  #Branch
    SET     RepositoryVstsId = TRY_CAST(RepositoryId AS UNIQUEIDENTIFIER),
            RepositoryUrl = IIF(RepositoryId LIKE 'http%', RepositoryId, NULL)

    INSERT AnalyticsModel.tbl_Branch
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        RepositoryId,
        BranchName,
        ProjectSK,
        RepositoryVstsId,
        RepositoryUrl
    )
    SELECT  TOP (@batchSizeMax) @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            s.RepositoryId,
            s.BranchName,
            NULL,
            s.RepositoryVstsId,
            s.RepositoryUrl
    FROM    #Branch AS s
    LEFT JOIN AnalyticsModel.tbl_Branch AS t
    ON      t.PartitionId = @partitionId
            AND t.RepositoryId = s.RepositoryId
            AND t.BranchName = s.BranchName
    WHERE   t.PartitionId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT
    SET @complete = IIF(@insertedCount < @batchSizeMax, 1, 0)

    DROP TABLE #Branch

    RETURN 0
END

GO

