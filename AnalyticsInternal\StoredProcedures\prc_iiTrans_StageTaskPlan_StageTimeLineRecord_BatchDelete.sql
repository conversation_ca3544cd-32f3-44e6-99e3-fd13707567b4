/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: DE39011C6C2BE50CF75FB10ACF8755316BAB82F1
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTaskPlan_StageTimeLineRecord_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #DeletedTaskPlan
    (
        PlanId INT NOT NULL,
        ProjectGuid UNIQUEIDENTIFIER NOT NULL,
        PipelineType NVARCHAR(260) COLLATE DATABASE_DEFAULT NOT NULL
    )

    INSERT  #DeletedTaskPlan
    SELECT  TOP (@batchSizeMax) WITH TIES
            PlanId,
            ProjectGuid,
            PipelineType
    FROM    AnalyticsStage.tbl_TaskPlan_Deleted d
    WHERE   PartitionId = @partitionId
            AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND d.PlanId > ISNULL(@stateData, -1)
    GROUP BY PlanId, ProjectGuid, PipelineType
    ORDER BY PlanId ASC
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)
    SET @endStateData = (SELECT MAX(PlanId) FROM #DeletedTaskPlan)

    -- make sure it's still gone from stage
    DELETE  t
    FROM    #DeletedTaskPlan t
    JOIN    AnalyticsStage.tbl_TaskPlan s
    ON      s.PlanId = t.PlanId
            AND s.ProjectGuid = t.ProjectGuid
            AND s.PipelineType = t.PipelineType
    WHERE   s.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    FROM    AnalyticsStage.tbl_TaskTimelineRecord t
    JOIN    #DeletedTaskPlan d
    ON      d.PlanId = t.PlanId
            AND d.ProjectGuid = t.ProjectGuid
            AND d.PipelineType = t.PipelineType
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #DeletedTaskPlan

    RETURN 0
END

GO

