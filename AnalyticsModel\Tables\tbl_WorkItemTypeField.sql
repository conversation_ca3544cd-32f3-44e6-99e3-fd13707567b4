CREATE TABLE [AnalyticsModel].[tbl_WorkItemTypeField] (
    [PartitionId]          INT              NOT NULL,
    [AnalyticsCreatedDate] DATETIME         NOT NULL,
    [AnalyticsUpdatedDate] DATETIME         NOT NULL,
    [AnalyticsBatchId]     BIGINT           NOT NULL,
    [ProjectSK]            UNIQUEIDENTIFIER NOT NULL,
    [FieldName]            NVARCHAR (128)   NOT NULL,
    [FieldType]            NVARCHAR (50)    NOT NULL,
    [WorkItemTypeCategory] NVARCHAR (70)    NOT NULL,
    [WorkItemType]         NVARCHAR (128)   NOT NULL,
    [FieldReferenceName]   NVARCHAR (386)   NULL
);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_AnalyticsModel_tbl_WorkItemTypeField]
    ON [AnalyticsModel].[tbl_WorkItemTypeField]([PartitionId] ASC, [ProjectSK] ASC, [FieldName] ASC, [WorkItemType] ASC, [WorkItemTypeCategory] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

