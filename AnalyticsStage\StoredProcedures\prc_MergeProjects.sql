/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F708DB59889E36BDF28EB593A261A3E85575EF6B
CREATE PROCEDURE AnalyticsStage.prc_MergeProjects
    @partitionId     INT,
    @providerShardId INT,
    @streamId        INT,
    @tableName       VARCHAR(64),
    @operation       VARCHAR(10),
    @replace         BIT,
    @keysOnly        BIT,
    @records         AnalyticsStage.typ_Project1 READONLY,
    @extendedFields  AnalyticsStage.typ_ExtendedField2 READONLY,
    @tableLoading    BIT,
    @watermark       NVARCHAR(255),
    @contentVersion  INT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT =  0
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage  NVARCHAR(2048)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)
    DECLARE @shortProcName VARCHAR(100) = OBJECT_NAME(@@PROCID)

    DECLARE @batchDt DATETIME = GETUTCDATE()
    DECLARE @batchId BIGINT

    IF (@keysOnly = 1)
    BEGIN
        SET @status = 1670025
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN
    END

    EXEC AnalyticsInternal.prc_iCreateStageBatch @partitionId, @tableName, @providerShardId, @streamId, @operation, @shortProcName, @batchDt, @newBatchId = @batchId OUTPUT

    DECLARE @deletedRowCount INT = 0

    BEGIN TRY
        BEGIN TRAN

        EXEC AnalyticsInternal.prc_iiSetWatermark @partitionId, @tableName, @providerShardId, @streamId, @watermark, @contentVersion

        IF (@replace = 1)
        BEGIN
            DELETE FROM AnalyticsStage.tbl_Project
            WHERE PartitionId = @partitionId
                AND AnalyticsProviderShardId = @providerShardId
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @deletedRowCount = @@ROWCOUNT
        END

        DECLARE @changes TABLE
        (
            MergeAction NVARCHAR(10),
            Changed BIT
        );

        MERGE AnalyticsStage.tbl_Project AS t
        USING @records AS s
            ON (t.PartitionId       = @partitionId
            AND t.AnalyticsProviderShardId = @providerShardId
            AND t.ProjectGuid       = s.ProjectGuid
            )
        WHEN MATCHED THEN
        UPDATE SET
              t.AnalyticsBatchId     = @batchId
            , t.AnalyticsStreamId    = IIF(@streamId >= t.AnalyticsStreamId, @streamId, t.AnalyticsStreamId)
            , t.AnalyticsUpdatedDate  = @batchDt
            , t.AnalyticsBatchIdChanged = IIF(NOT EXISTS(
                SELECT
                    s.ProjectName
                    , s.IsDeleted
                    , s.ProcessId
                INTERSECT
                SELECT
                    t.ProjectName
                    , t.IsDeleted
                    , t.ProcessId
                ), @batchId, t.AnalyticsBatchIdChanged)
            , t.ProjectName   = s.ProjectName
            , t.IsDeleted     = s.IsDeleted
            , t.ProcessId     = s.ProcessId
        WHEN NOT MATCHED BY TARGET THEN
        INSERT (
              PartitionId
            , AnalyticsProviderShardId
            , AnalyticsStreamId
            , AnalyticsBatchId
            , AnalyticsBatchIdChanged
            , AnalyticsCreatedDate
            , AnalyticsUpdatedDate
            , ProjectGuid
            , ProjectName
            , IsDeleted
            , ProcessId
        )
        VALUES
        (
              @partitionId
            , @providerShardId
            , @streamID
            , @batchId
            , @batchId
            , @batchDt
            , @batchDt
            , s.ProjectGuid
            , s.ProjectName
            , s.IsDeleted
            , s.ProcessId
        )
        OUTPUT $action, IIF(INSERTED.AnalyticsBatchId = INSERTED.AnalyticsBatchIdChanged, 1, 0) INTO @changes
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        COMMIT TRAN
    END TRY
    BEGIN CATCH
        ROLLBACK TRAN
        SET @errorMessage = dbo.func_FormatErrorMessage(ERROR_NUMBER(), ERROR_MESSAGE(), ERROR_LINE())
        SET @status = 1670003
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @errorMessage)
    END CATCH

    DECLARE @ready BIT = CASE WHEN @status = 0 THEN 1 ELSE 0 END
    DECLARE @failed BIT = CASE WHEN @status <> 0 THEN 1 ELSE 0 END
    DECLARE @insertedRowCount INT = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    DECLARE @updatedRowCount INT = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE' AND Changed = 1)

    EXEC AnalyticsInternal.prc_iUpdateStageBatch @partitionId, @batchId, false, @ready, @failed, @insertedRowCount, @updatedRowCount, @deletedRowCount, @tableLoading

    RETURN @status
END

GO

