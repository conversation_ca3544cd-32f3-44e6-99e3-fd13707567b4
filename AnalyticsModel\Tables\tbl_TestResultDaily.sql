CREATE TABLE [AnalyticsModel].[tbl_TestResultDaily] (
    [PartitionId]              INT              NOT NULL,
    [AnalyticsCreatedDate]     DATETIME2 (7)    NOT NULL,
    [AnalyticsUpdatedDate]     DATETIME2 (7)    NOT NULL,
    [AnalyticsBatchId]         BIGINT           NOT NULL,
    [TestResultDailySK]        BIGINT           IDENTITY (1, 1) NOT NULL,
    [ProjectSK]                UNIQUEIDENTIFIER NULL,
    [TestSK]                   INT              NULL,
    [BranchSK]                 INT              NULL,
    [DateSK]                   INT              NULL,
    [DataSourceId]             TINYINT          NOT NULL,
    [ReleasePipelineSK]        INT              NULL,
    [ReleaseStageSK]           INT              NULL,
    [<PERSON><PERSON><PERSON><PERSON>elineSK]          INT              NULL,
    [ResultDurationSeconds]    DECIMAL (18, 3)  NULL,
    [ResultCount]              INT              NULL,
    [ResultPassCount]          INT              NULL,
    [ResultFailCount]          INT              NULL,
    [ResultNoneCount]          INT              NULL,
    [ResultInconclusiveCount]  INT              NULL,
    [ResultTimeoutCount]       INT              NULL,
    [ResultAbortedCount]       INT              NULL,
    [ResultBlockedCount]       INT              NULL,
    [ResultNotExecutedCount]   INT              NULL,
    [ResultWarningCount]       INT              NULL,
    [ResultErrorCount]         INT              NULL,
    [ResultNotApplicableCount] INT              NULL,
    [ResultNotImpactedCount]   INT              NULL,
    [TestRunType]              TINYINT          NULL,
    [WorkFlow]                 TINYINT          NULL
) ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_TestResultDaily]
    ON [AnalyticsModel].[tbl_TestResultDaily]
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

