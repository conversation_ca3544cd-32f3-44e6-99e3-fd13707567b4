/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: DB528C62811C188A9876E1AF722AE63327E2A51B
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageProject_ModelProject_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DELETE TOP (@batchSizeMax) FROM AnalyticsModel.tbl_Project
    FROM AnalyticsModel.tbl_Project t
    LEFT JOIN AnalyticsStage.tbl_Project s
        ON s.PartitionId = t.PartitionId
        AND s.ProjectGuid = t.ProjectSK
      WHERE t.PartitionId = @partitionId
      AND s.ProjectGuid IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    IF (@deletedCount > 0)
    BEGIN
        EXEC prc_iiSendNotification @partitionId, 'BF5C56C8-849B-4BC3-A604-83128F921352'
    END

    RETURN 0
END

GO

