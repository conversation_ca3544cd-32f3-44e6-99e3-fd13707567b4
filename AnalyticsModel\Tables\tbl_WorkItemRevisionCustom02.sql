CREATE TABLE [AnalyticsModel].[tbl_WorkItemRevisionCustom02] (
    [PartitionId]          INT                NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]     BIGINT             NOT NULL,
    [WorkItemId]           INT                NOT NULL,
    [Revision]             INT                NOT NULL,
    [WorkItemRevisionSK]   INT                NOT NULL,
    [String0201]           NVARCHAR (256)     NULL,
    [String0202]           NVARCHAR (256)     NULL,
    [String0203]           NVARCHAR (256)     NULL,
    [String0204]           NVARCHAR (256)     NULL,
    [String0205]           NVARCHAR (256)     NULL,
    [String0206]           NVARCHAR (256)     NULL,
    [String0207]           NVARCHAR (256)     NULL,
    [String0208]           NVARCHAR (256)     NULL,
    [String0209]           NVARCHAR (256)     NULL,
    [String0210]           NVARCHAR (256)     NULL,
    [String0211]           NVARCHAR (256)     NULL,
    [String0212]           NVARCHAR (256)     NULL,
    [String0213]           NVARCHAR (256)     NULL,
    [String0214]           NVARCHAR (256)     NULL,
    [String0215]           NVARCHAR (256)     NULL,
    [String0216]           NVARCHAR (256)     NULL,
    [String0217]           NVARCHAR (256)     NULL,
    [String0218]           NVARCHAR (256)     NULL,
    [String0219]           NVARCHAR (256)     NULL,
    [String0220]           NVARCHAR (256)     NULL,
    [String0221]           NVARCHAR (256)     NULL,
    [String0222]           NVARCHAR (256)     NULL,
    [String0223]           NVARCHAR (256)     NULL,
    [String0224]           NVARCHAR (256)     NULL,
    [String0225]           NVARCHAR (256)     NULL,
    [String0226]           NVARCHAR (256)     NULL,
    [String0227]           NVARCHAR (256)     NULL,
    [String0228]           NVARCHAR (256)     NULL,
    [String0229]           NVARCHAR (256)     NULL,
    [String0230]           NVARCHAR (256)     NULL,
    [String0231]           NVARCHAR (256)     NULL,
    [String0232]           NVARCHAR (256)     NULL,
    [String0233]           NVARCHAR (256)     NULL,
    [String0234]           NVARCHAR (256)     NULL,
    [String0235]           NVARCHAR (256)     NULL,
    [String0236]           NVARCHAR (256)     NULL,
    [String0237]           NVARCHAR (256)     NULL,
    [String0238]           NVARCHAR (256)     NULL,
    [String0239]           NVARCHAR (256)     NULL,
    [String0240]           NVARCHAR (256)     NULL,
    [String0241]           NVARCHAR (256)     NULL,
    [String0242]           NVARCHAR (256)     NULL,
    [String0243]           NVARCHAR (256)     NULL,
    [String0244]           NVARCHAR (256)     NULL,
    [String0245]           NVARCHAR (256)     NULL,
    [String0246]           NVARCHAR (256)     NULL,
    [String0247]           NVARCHAR (256)     NULL,
    [String0248]           NVARCHAR (256)     NULL,
    [String0249]           NVARCHAR (256)     NULL,
    [String0250]           NVARCHAR (256)     NULL,
    [String0251]           NVARCHAR (256)     NULL,
    [String0252]           NVARCHAR (256)     NULL,
    [String0253]           NVARCHAR (256)     NULL,
    [String0254]           NVARCHAR (256)     NULL,
    [String0255]           NVARCHAR (256)     NULL,
    [String0256]           NVARCHAR (256)     NULL,
    [String0257]           NVARCHAR (256)     NULL,
    [String0258]           NVARCHAR (256)     NULL,
    [String0259]           NVARCHAR (256)     NULL,
    [String0260]           NVARCHAR (256)     NULL,
    [String0261]           NVARCHAR (256)     NULL,
    [String0262]           NVARCHAR (256)     NULL,
    [String0263]           NVARCHAR (256)     NULL,
    [String0264]           NVARCHAR (256)     NULL,
    [String0265]           NVARCHAR (256)     NULL,
    [String0266]           NVARCHAR (256)     NULL,
    [String0267]           NVARCHAR (256)     NULL,
    [String0268]           NVARCHAR (256)     NULL,
    [String0269]           NVARCHAR (256)     NULL,
    [String0270]           NVARCHAR (256)     NULL,
    [String0271]           NVARCHAR (256)     NULL,
    [String0272]           NVARCHAR (256)     NULL,
    [String0273]           NVARCHAR (256)     NULL,
    [String0274]           NVARCHAR (256)     NULL,
    [String0275]           NVARCHAR (256)     NULL,
    [String0276]           NVARCHAR (256)     NULL,
    [String0277]           NVARCHAR (256)     NULL,
    [String0278]           NVARCHAR (256)     NULL,
    [String0279]           NVARCHAR (256)     NULL,
    [String0280]           NVARCHAR (256)     NULL,
    [String0281]           NVARCHAR (256)     NULL,
    [String0282]           NVARCHAR (256)     NULL,
    [String0283]           NVARCHAR (256)     NULL,
    [String0284]           NVARCHAR (256)     NULL,
    [String0285]           NVARCHAR (256)     NULL,
    [String0286]           NVARCHAR (256)     NULL,
    [String0287]           NVARCHAR (256)     NULL,
    [String0288]           NVARCHAR (256)     NULL,
    [String0289]           NVARCHAR (256)     NULL,
    [String0290]           NVARCHAR (256)     NULL,
    [String0291]           NVARCHAR (256)     NULL,
    [String0292]           NVARCHAR (256)     NULL,
    [String0293]           NVARCHAR (256)     NULL,
    [String0294]           NVARCHAR (256)     NULL,
    [String0295]           NVARCHAR (256)     NULL,
    [String0296]           NVARCHAR (256)     NULL,
    [String0297]           NVARCHAR (256)     NULL,
    [String0298]           NVARCHAR (256)     NULL,
    [String0299]           NVARCHAR (256)     NULL,
    [String0300]           NVARCHAR (256)     NULL,
    [String0301]           NVARCHAR (256)     NULL,
    [String0302]           NVARCHAR (256)     NULL,
    [String0303]           NVARCHAR (256)     NULL,
    [String0304]           NVARCHAR (256)     NULL,
    [String0305]           NVARCHAR (256)     NULL,
    [String0306]           NVARCHAR (256)     NULL,
    [String0307]           NVARCHAR (256)     NULL,
    [String0308]           NVARCHAR (256)     NULL,
    [String0309]           NVARCHAR (256)     NULL,
    [String0310]           NVARCHAR (256)     NULL,
    [String0311]           NVARCHAR (256)     NULL,
    [String0312]           NVARCHAR (256)     NULL,
    [String0313]           NVARCHAR (256)     NULL,
    [String0314]           NVARCHAR (256)     NULL,
    [String0315]           NVARCHAR (256)     NULL,
    [String0316]           NVARCHAR (256)     NULL,
    [String0317]           NVARCHAR (256)     NULL,
    [String0318]           NVARCHAR (256)     NULL,
    [String0319]           NVARCHAR (256)     NULL,
    [String0320]           NVARCHAR (256)     NULL,
    [String0321]           NVARCHAR (256)     NULL,
    [String0322]           NVARCHAR (256)     NULL,
    [String0323]           NVARCHAR (256)     NULL,
    [String0324]           NVARCHAR (256)     NULL,
    [String0325]           NVARCHAR (256)     NULL,
    [String0326]           NVARCHAR (256)     NULL,
    [String0327]           NVARCHAR (256)     NULL,
    [String0328]           NVARCHAR (256)     NULL,
    [String0329]           NVARCHAR (256)     NULL,
    [String0330]           NVARCHAR (256)     NULL,
    [String0331]           NVARCHAR (256)     NULL,
    [String0332]           NVARCHAR (256)     NULL,
    [String0333]           NVARCHAR (256)     NULL,
    [String0334]           NVARCHAR (256)     NULL,
    [String0335]           NVARCHAR (256)     NULL,
    [String0336]           NVARCHAR (256)     NULL,
    [String0337]           NVARCHAR (256)     NULL,
    [String0338]           NVARCHAR (256)     NULL,
    [String0339]           NVARCHAR (256)     NULL,
    [String0340]           NVARCHAR (256)     NULL,
    [String0341]           NVARCHAR (256)     NULL,
    [String0342]           NVARCHAR (256)     NULL,
    [String0343]           NVARCHAR (256)     NULL,
    [String0344]           NVARCHAR (256)     NULL,
    [String0345]           NVARCHAR (256)     NULL,
    [String0346]           NVARCHAR (256)     NULL,
    [String0347]           NVARCHAR (256)     NULL,
    [String0348]           NVARCHAR (256)     NULL,
    [String0349]           NVARCHAR (256)     NULL,
    [String0350]           NVARCHAR (256)     NULL,
    [String0351]           NVARCHAR (256)     NULL,
    [String0352]           NVARCHAR (256)     NULL,
    [String0353]           NVARCHAR (256)     NULL,
    [String0354]           NVARCHAR (256)     NULL,
    [String0355]           NVARCHAR (256)     NULL,
    [String0356]           NVARCHAR (256)     NULL,
    [String0357]           NVARCHAR (256)     NULL,
    [String0358]           NVARCHAR (256)     NULL,
    [String0359]           NVARCHAR (256)     NULL,
    [String0360]           NVARCHAR (256)     NULL,
    [String0361]           NVARCHAR (256)     NULL,
    [String0362]           NVARCHAR (256)     NULL,
    [String0363]           NVARCHAR (256)     NULL,
    [String0364]           NVARCHAR (256)     NULL,
    [String0365]           NVARCHAR (256)     NULL,
    [String0366]           NVARCHAR (256)     NULL,
    [String0367]           NVARCHAR (256)     NULL,
    [String0368]           NVARCHAR (256)     NULL,
    [String0369]           NVARCHAR (256)     NULL,
    [String0370]           NVARCHAR (256)     NULL,
    [String0371]           NVARCHAR (256)     NULL,
    [String0372]           NVARCHAR (256)     NULL,
    [String0373]           NVARCHAR (256)     NULL,
    [String0374]           NVARCHAR (256)     NULL,
    [String0375]           NVARCHAR (256)     NULL,
    [String0376]           NVARCHAR (256)     NULL,
    [String0377]           NVARCHAR (256)     NULL,
    [String0378]           NVARCHAR (256)     NULL,
    [String0379]           NVARCHAR (256)     NULL,
    [String0380]           NVARCHAR (256)     NULL,
    [String0381]           NVARCHAR (256)     NULL,
    [String0382]           NVARCHAR (256)     NULL,
    [String0383]           NVARCHAR (256)     NULL,
    [String0384]           NVARCHAR (256)     NULL,
    [String0385]           NVARCHAR (256)     NULL,
    [String0386]           NVARCHAR (256)     NULL,
    [String0387]           NVARCHAR (256)     NULL,
    [String0388]           NVARCHAR (256)     NULL,
    [String0389]           NVARCHAR (256)     NULL,
    [String0390]           NVARCHAR (256)     NULL,
    [String0391]           NVARCHAR (256)     NULL,
    [String0392]           NVARCHAR (256)     NULL,
    [String0393]           NVARCHAR (256)     NULL,
    [String0394]           NVARCHAR (256)     NULL,
    [String0395]           NVARCHAR (256)     NULL,
    [String0396]           NVARCHAR (256)     NULL,
    [String0397]           NVARCHAR (256)     NULL,
    [String0398]           NVARCHAR (256)     NULL,
    [String0399]           NVARCHAR (256)     NULL,
    [String0400]           NVARCHAR (256)     NULL,
    [Integer0051]          BIGINT             NULL,
    [Integer0052]          BIGINT             NULL,
    [Integer0053]          BIGINT             NULL,
    [Integer0054]          BIGINT             NULL,
    [Integer0055]          BIGINT             NULL,
    [Integer0056]          BIGINT             NULL,
    [Integer0057]          BIGINT             NULL,
    [Integer0058]          BIGINT             NULL,
    [Integer0059]          BIGINT             NULL,
    [Integer0060]          BIGINT             NULL,
    [Integer0061]          BIGINT             NULL,
    [Integer0062]          BIGINT             NULL,
    [Integer0063]          BIGINT             NULL,
    [Integer0064]          BIGINT             NULL,
    [Integer0065]          BIGINT             NULL,
    [Integer0066]          BIGINT             NULL,
    [Integer0067]          BIGINT             NULL,
    [Integer0068]          BIGINT             NULL,
    [Integer0069]          BIGINT             NULL,
    [Integer0070]          BIGINT             NULL,
    [Integer0071]          BIGINT             NULL,
    [Integer0072]          BIGINT             NULL,
    [Integer0073]          BIGINT             NULL,
    [Integer0074]          BIGINT             NULL,
    [Integer0075]          BIGINT             NULL,
    [Integer0076]          BIGINT             NULL,
    [Integer0077]          BIGINT             NULL,
    [Integer0078]          BIGINT             NULL,
    [Integer0079]          BIGINT             NULL,
    [Integer0080]          BIGINT             NULL,
    [Integer0081]          BIGINT             NULL,
    [Integer0082]          BIGINT             NULL,
    [Integer0083]          BIGINT             NULL,
    [Integer0084]          BIGINT             NULL,
    [Integer0085]          BIGINT             NULL,
    [Integer0086]          BIGINT             NULL,
    [Integer0087]          BIGINT             NULL,
    [Integer0088]          BIGINT             NULL,
    [Integer0089]          BIGINT             NULL,
    [Integer0090]          BIGINT             NULL,
    [Integer0091]          BIGINT             NULL,
    [Integer0092]          BIGINT             NULL,
    [Integer0093]          BIGINT             NULL,
    [Integer0094]          BIGINT             NULL,
    [Integer0095]          BIGINT             NULL,
    [Integer0096]          BIGINT             NULL,
    [Integer0097]          BIGINT             NULL,
    [Integer0098]          BIGINT             NULL,
    [Integer0099]          BIGINT             NULL,
    [Integer0100]          BIGINT             NULL,
    [Double0051]           FLOAT (53)         NULL,
    [Double0052]           FLOAT (53)         NULL,
    [Double0053]           FLOAT (53)         NULL,
    [Double0054]           FLOAT (53)         NULL,
    [Double0055]           FLOAT (53)         NULL,
    [Double0056]           FLOAT (53)         NULL,
    [Double0057]           FLOAT (53)         NULL,
    [Double0058]           FLOAT (53)         NULL,
    [Double0059]           FLOAT (53)         NULL,
    [Double0060]           FLOAT (53)         NULL,
    [Double0061]           FLOAT (53)         NULL,
    [Double0062]           FLOAT (53)         NULL,
    [Double0063]           FLOAT (53)         NULL,
    [Double0064]           FLOAT (53)         NULL,
    [Double0065]           FLOAT (53)         NULL,
    [Double0066]           FLOAT (53)         NULL,
    [Double0067]           FLOAT (53)         NULL,
    [Double0068]           FLOAT (53)         NULL,
    [Double0069]           FLOAT (53)         NULL,
    [Double0070]           FLOAT (53)         NULL,
    [Double0071]           FLOAT (53)         NULL,
    [Double0072]           FLOAT (53)         NULL,
    [Double0073]           FLOAT (53)         NULL,
    [Double0074]           FLOAT (53)         NULL,
    [Double0075]           FLOAT (53)         NULL,
    [Double0076]           FLOAT (53)         NULL,
    [Double0077]           FLOAT (53)         NULL,
    [Double0078]           FLOAT (53)         NULL,
    [Double0079]           FLOAT (53)         NULL,
    [Double0080]           FLOAT (53)         NULL,
    [Double0081]           FLOAT (53)         NULL,
    [Double0082]           FLOAT (53)         NULL,
    [Double0083]           FLOAT (53)         NULL,
    [Double0084]           FLOAT (53)         NULL,
    [Double0085]           FLOAT (53)         NULL,
    [Double0086]           FLOAT (53)         NULL,
    [Double0087]           FLOAT (53)         NULL,
    [Double0088]           FLOAT (53)         NULL,
    [Double0089]           FLOAT (53)         NULL,
    [Double0090]           FLOAT (53)         NULL,
    [Double0091]           FLOAT (53)         NULL,
    [Double0092]           FLOAT (53)         NULL,
    [Double0093]           FLOAT (53)         NULL,
    [Double0094]           FLOAT (53)         NULL,
    [Double0095]           FLOAT (53)         NULL,
    [Double0096]           FLOAT (53)         NULL,
    [Double0097]           FLOAT (53)         NULL,
    [Double0098]           FLOAT (53)         NULL,
    [Double0099]           FLOAT (53)         NULL,
    [Double0100]           FLOAT (53)         NULL,
    [DateTime0051]         DATETIMEOFFSET (7) NULL,
    [DateTime0052]         DATETIMEOFFSET (7) NULL,
    [DateTime0053]         DATETIMEOFFSET (7) NULL,
    [DateTime0054]         DATETIMEOFFSET (7) NULL,
    [DateTime0055]         DATETIMEOFFSET (7) NULL,
    [DateTime0056]         DATETIMEOFFSET (7) NULL,
    [DateTime0057]         DATETIMEOFFSET (7) NULL,
    [DateTime0058]         DATETIMEOFFSET (7) NULL,
    [DateTime0059]         DATETIMEOFFSET (7) NULL,
    [DateTime0060]         DATETIMEOFFSET (7) NULL,
    [DateTime0061]         DATETIMEOFFSET (7) NULL,
    [DateTime0062]         DATETIMEOFFSET (7) NULL,
    [DateTime0063]         DATETIMEOFFSET (7) NULL,
    [DateTime0064]         DATETIMEOFFSET (7) NULL,
    [DateTime0065]         DATETIMEOFFSET (7) NULL,
    [DateTime0066]         DATETIMEOFFSET (7) NULL,
    [DateTime0067]         DATETIMEOFFSET (7) NULL,
    [DateTime0068]         DATETIMEOFFSET (7) NULL,
    [DateTime0069]         DATETIMEOFFSET (7) NULL,
    [DateTime0070]         DATETIMEOFFSET (7) NULL,
    [DateTime0071]         DATETIMEOFFSET (7) NULL,
    [DateTime0072]         DATETIMEOFFSET (7) NULL,
    [DateTime0073]         DATETIMEOFFSET (7) NULL,
    [DateTime0074]         DATETIMEOFFSET (7) NULL,
    [DateTime0075]         DATETIMEOFFSET (7) NULL,
    [DateTime0076]         DATETIMEOFFSET (7) NULL,
    [DateTime0077]         DATETIMEOFFSET (7) NULL,
    [DateTime0078]         DATETIMEOFFSET (7) NULL,
    [DateTime0079]         DATETIMEOFFSET (7) NULL,
    [DateTime0080]         DATETIMEOFFSET (7) NULL,
    [DateTime0081]         DATETIMEOFFSET (7) NULL,
    [DateTime0082]         DATETIMEOFFSET (7) NULL,
    [DateTime0083]         DATETIMEOFFSET (7) NULL,
    [DateTime0084]         DATETIMEOFFSET (7) NULL,
    [DateTime0085]         DATETIMEOFFSET (7) NULL,
    [DateTime0086]         DATETIMEOFFSET (7) NULL,
    [DateTime0087]         DATETIMEOFFSET (7) NULL,
    [DateTime0088]         DATETIMEOFFSET (7) NULL,
    [DateTime0089]         DATETIMEOFFSET (7) NULL,
    [DateTime0090]         DATETIMEOFFSET (7) NULL,
    [DateTime0091]         DATETIMEOFFSET (7) NULL,
    [DateTime0092]         DATETIMEOFFSET (7) NULL,
    [DateTime0093]         DATETIMEOFFSET (7) NULL,
    [DateTime0094]         DATETIMEOFFSET (7) NULL,
    [DateTime0095]         DATETIMEOFFSET (7) NULL,
    [DateTime0096]         DATETIMEOFFSET (7) NULL,
    [DateTime0097]         DATETIMEOFFSET (7) NULL,
    [DateTime0098]         DATETIMEOFFSET (7) NULL,
    [DateTime0099]         DATETIMEOFFSET (7) NULL,
    [DateTime0100]         DATETIMEOFFSET (7) NULL,
    [Boolean0051]          BIT                NULL,
    [Boolean0052]          BIT                NULL,
    [Boolean0053]          BIT                NULL,
    [Boolean0054]          BIT                NULL,
    [Boolean0055]          BIT                NULL,
    [Boolean0056]          BIT                NULL,
    [Boolean0057]          BIT                NULL,
    [Boolean0058]          BIT                NULL,
    [Boolean0059]          BIT                NULL,
    [Boolean0060]          BIT                NULL,
    [Boolean0061]          BIT                NULL,
    [Boolean0062]          BIT                NULL,
    [Boolean0063]          BIT                NULL,
    [Boolean0064]          BIT                NULL,
    [Boolean0065]          BIT                NULL,
    [Boolean0066]          BIT                NULL,
    [Boolean0067]          BIT                NULL,
    [Boolean0068]          BIT                NULL,
    [Boolean0069]          BIT                NULL,
    [Boolean0070]          BIT                NULL,
    [Boolean0071]          BIT                NULL,
    [Boolean0072]          BIT                NULL,
    [Boolean0073]          BIT                NULL,
    [Boolean0074]          BIT                NULL,
    [Boolean0075]          BIT                NULL,
    [Boolean0076]          BIT                NULL,
    [Boolean0077]          BIT                NULL,
    [Boolean0078]          BIT                NULL,
    [Boolean0079]          BIT                NULL,
    [Boolean0080]          BIT                NULL,
    [Boolean0081]          BIT                NULL,
    [Boolean0082]          BIT                NULL,
    [Boolean0083]          BIT                NULL,
    [Boolean0084]          BIT                NULL,
    [Boolean0085]          BIT                NULL,
    [Boolean0086]          BIT                NULL,
    [Boolean0087]          BIT                NULL,
    [Boolean0088]          BIT                NULL,
    [Boolean0089]          BIT                NULL,
    [Boolean0090]          BIT                NULL,
    [Boolean0091]          BIT                NULL,
    [Boolean0092]          BIT                NULL,
    [Boolean0093]          BIT                NULL,
    [Boolean0094]          BIT                NULL,
    [Boolean0095]          BIT                NULL,
    [Boolean0096]          BIT                NULL,
    [Boolean0097]          BIT                NULL,
    [Boolean0098]          BIT                NULL,
    [Boolean0099]          BIT                NULL,
    [Boolean0100]          BIT                NULL,
    [Identity0051]         UNIQUEIDENTIFIER   NULL,
    [Identity0052]         UNIQUEIDENTIFIER   NULL,
    [Identity0053]         UNIQUEIDENTIFIER   NULL,
    [Identity0054]         UNIQUEIDENTIFIER   NULL,
    [Identity0055]         UNIQUEIDENTIFIER   NULL,
    [Identity0056]         UNIQUEIDENTIFIER   NULL,
    [Identity0057]         UNIQUEIDENTIFIER   NULL,
    [Identity0058]         UNIQUEIDENTIFIER   NULL,
    [Identity0059]         UNIQUEIDENTIFIER   NULL,
    [Identity0060]         UNIQUEIDENTIFIER   NULL,
    [Identity0061]         UNIQUEIDENTIFIER   NULL,
    [Identity0062]         UNIQUEIDENTIFIER   NULL,
    [Identity0063]         UNIQUEIDENTIFIER   NULL,
    [Identity0064]         UNIQUEIDENTIFIER   NULL,
    [Identity0065]         UNIQUEIDENTIFIER   NULL,
    [Identity0066]         UNIQUEIDENTIFIER   NULL,
    [Identity0067]         UNIQUEIDENTIFIER   NULL,
    [Identity0068]         UNIQUEIDENTIFIER   NULL,
    [Identity0069]         UNIQUEIDENTIFIER   NULL,
    [Identity0070]         UNIQUEIDENTIFIER   NULL,
    [Identity0071]         UNIQUEIDENTIFIER   NULL,
    [Identity0072]         UNIQUEIDENTIFIER   NULL,
    [Identity0073]         UNIQUEIDENTIFIER   NULL,
    [Identity0074]         UNIQUEIDENTIFIER   NULL,
    [Identity0075]         UNIQUEIDENTIFIER   NULL,
    [Identity0076]         UNIQUEIDENTIFIER   NULL,
    [Identity0077]         UNIQUEIDENTIFIER   NULL,
    [Identity0078]         UNIQUEIDENTIFIER   NULL,
    [Identity0079]         UNIQUEIDENTIFIER   NULL,
    [Identity0080]         UNIQUEIDENTIFIER   NULL,
    [Identity0081]         UNIQUEIDENTIFIER   NULL,
    [Identity0082]         UNIQUEIDENTIFIER   NULL,
    [Identity0083]         UNIQUEIDENTIFIER   NULL,
    [Identity0084]         UNIQUEIDENTIFIER   NULL,
    [Identity0085]         UNIQUEIDENTIFIER   NULL,
    [Identity0086]         UNIQUEIDENTIFIER   NULL,
    [Identity0087]         UNIQUEIDENTIFIER   NULL,
    [Identity0088]         UNIQUEIDENTIFIER   NULL,
    [Identity0089]         UNIQUEIDENTIFIER   NULL,
    [Identity0090]         UNIQUEIDENTIFIER   NULL,
    [Identity0091]         UNIQUEIDENTIFIER   NULL,
    [Identity0092]         UNIQUEIDENTIFIER   NULL,
    [Identity0093]         UNIQUEIDENTIFIER   NULL,
    [Identity0094]         UNIQUEIDENTIFIER   NULL,
    [Identity0095]         UNIQUEIDENTIFIER   NULL,
    [Identity0096]         UNIQUEIDENTIFIER   NULL,
    [Identity0097]         UNIQUEIDENTIFIER   NULL,
    [Identity0098]         UNIQUEIDENTIFIER   NULL,
    [Identity0099]         UNIQUEIDENTIFIER   NULL,
    [Identity0100]         UNIQUEIDENTIFIER   NULL
) ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_AnalyticsModel_tbl_WorkItemRevisionCustom02_WorkItemRevisionSK]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom02]([PartitionId] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_WorkItemRevisionCustom02]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom02]
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

