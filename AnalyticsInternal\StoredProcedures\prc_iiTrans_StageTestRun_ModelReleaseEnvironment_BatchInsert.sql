/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: CA20977156AE287C04B40E32D6FC2155A4C10E6D
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestRun_ModelReleaseEnvironment_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    CREATE TABLE #ReleaseEnvironment
    (
        ReleaseEnvironmentId            INT              NOT NULL,
        ProjectGuid                     UNIQUEIDENTIFIER NULL,
        ReleaseEnvironmentDefinitionId  INT              NULL,
    )

    INSERT  #ReleaseEnvironment (ReleaseEnvironmentId, ProjectGuid, ReleaseEnvironmentDefinitionId)
    SELECT  ReleaseEnvironmentId,
            ProjectGuid,
            MAX(ReleaseEnvironmentDefinitionId) -- ReleaseEnvironmentDefinitionId has a default value in the ops store (0). It's rare but old data may have a mix of default and valid data for a single release environment.
    FROM    AnalyticsStage.tbl_TestRun WITH (INDEX (IX_tbl_TestRun_AxBatchIdChanged), FORCESEEK)
    WHERE   PartitionId = @partitionId
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND ReleaseEnvironmentId > 0
    GROUP BY ReleaseEnvironmentId,
             ProjectGuid
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT AnalyticsModel.tbl_ReleaseEnvironment
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        ReleaseEnvironmentId,
        ReleaseEnvironmentDefinitionId,
        ProjectSK
    )
    SELECT  TOP (@batchSizeMax) @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            s.ReleaseEnvironmentId,
            s.ReleaseEnvironmentDefinitionId,
            s.ProjectGuid
    FROM    #ReleaseEnvironment AS s
    LEFT JOIN AnalyticsModel.tbl_ReleaseEnvironment AS t
    ON      t.PartitionId = @partitionId
            AND t.ProjectSK = s.ProjectGuid
            AND t.ReleaseEnvironmentId = s.ReleaseEnvironmentId
    WHERE   t.PartitionId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT
    SET @complete = IIF(@insertedCount < @batchSizeMax, 1, 0)

    DROP TABLE #ReleaseEnvironment

    RETURN 0
END

GO

