CREATE PROCEDURE dbo.prc_DevChangeChangesetOwner
    @partitionId        INT,
    @changesetId        INT,
    @newOwnerId         UNIQUEIDENTIFIER,
    @changerTfId        UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON
    SET XACT_ABORT ON

    DECLARE @procedureName SYSNAME
    SELECT @procedureName = @@SERVERNAME + '.' + db_name() + '.tfvc.' + object_name(@@PROCID)
    DECLARE @tfError NVARCHAR(255)
    DECLARE @status INT = 0

    -- Validate changeset exists
    DECLARE @currentOwnerId UNIQUEIDENTIFIER
    SELECT @currentOwnerId = OwnerId
    FROM tbl_ChangeSet
    WHERE PartitionId = @partitionId AND ChangeSetId = @changesetId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF @currentOwnerId IS NULL
    BEGIN
        SET @tfError = dbo.func_GetMessage(500054); 
        RAISERROR(@tfError, 16, -1, @procedureName, @changesetId)
        RETURN 500054
    END

    -- Don't update if already the correct owner
    IF @currentOwnerId = @newOwnerId
    BEGIN
        RETURN 0
    END

    BEGIN TRANSACTION

    -- Update changeset owner
    UPDATE tfvc.tbl_ChangeSet
    SET OwnerId = @newOwnerId
    WHERE PartitionId = @partitionId AND ChangeSetId = @changesetId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF @@ROWCOUNT <> 1
    BEGIN
        ROLLBACK TRANSACTION
        SET @tfError = dbo.func_GetMessage(500054); 
        RAISERROR(@tfError, 16, -1, @procedureName, @changesetId)
        RETURN 500054
    END

    COMMIT TRANSACTION
    RETURN 0
END