/******************************************************************************************************
** Warning: the contents of this function are critical to its functioning.
** Modifying the contents of this function could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: BDBA37C4E8253BA45807811F5E03F9E40AF92EF9
CREATE FUNCTION AnalyticsInternal.func_SplitString( @stringToSplit NVARCHAR(MAX), @delim NVARCHAR(20))
RETURNS
    @returnList TABLE (
        Value [nvarchar](500) NULL,
        Ordinal INT NOT NULL
        )
AS
BEGIN

    DECLARE @value NVARCHAR(255)
    DECLARE @pos INT
    DECLARE @ordinal INT = 0
    -- LEN ignores trailing spaces, but we want them as part of our delimiter.
    -- CHARINDEX respects trailing spaces, so we're OK there.
    DECLARE @delimLen INT = LEN(@delim + 'x') - 1

    WHILE CHARINDEX(@delim, @stringToSplit) > 0
    BEGIN
        SELECT @pos  = CHARINDEX(@delim, @stringToSplit)
        SELECT @value = SUBSTRING(@stringToSplit, 1, @pos - 1)

        INSERT INTO @returnList
        SELECT @value, @ordinal

        SET @ordinal = @ordinal + 1

        SELECT @stringToSplit = SUBSTRING(@stringToSplit, @pos + @delimLen, LEN(@stringToSplit) - @pos)
    END

    INSERT INTO @returnList
    SELECT @stringToSplit, @ordinal

    RETURN
END

GO

