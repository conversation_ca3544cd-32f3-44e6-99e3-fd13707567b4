/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 2E818502CE1046CBC9B9555A83C5088828BD4E4A
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_ModelWorkItem_BatchMerge_FromUpdate_noop
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    DECLARE @endStateOut VARCHAR(10)
    DECLARE @endStateDataOut BIGINT
    DECLARE @completeOut BIT
    DECLARE @insertedCountOut INT
    DECLARE @updatedCountOut INT
    DECLARE @deletedCountOut INT

    EXEC AnalyticsInternal.prc_iMergeWorkItemCore_noop
        @partitionId,
        @batchId,
        @batchDt,
        @settings,
        @triggerTableName,
        'update',
        @triggerBatchIdStart,
        @triggerBatchIdEnd,
        @state,
        @stateData,
        @attemptCount,
        @subBatchCount,
        @lastFailedSubBatchCount,
        @failedCount,
        @endState = @endStateOut OUTPUT,
        @endStateData = @endStateDataOut OUTPUT,
        @complete = @completeOut OUTPUT,
        @insertedCount = @insertedCountOut OUTPUT,
        @updatedCount = @updatedCountOut OUTPUT,
        @deletedCount = @deletedCountOut OUTPUT

    SET @endState = @endStateOut
    SET @endStateData = @endStateDataOut
    SET @complete = @completeOut
    SET @insertedCount = @insertedCountOut
    SET @updatedCount = @updatedCountOut
    SET @deletedCount = @deletedCountOut

    RETURN 0
END

GO

