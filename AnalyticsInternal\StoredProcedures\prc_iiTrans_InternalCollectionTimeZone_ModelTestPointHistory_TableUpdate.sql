/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 2578A891B34573B7EAB6B68D0C1C9F336E4784AB
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelTestPointHistory_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 20000) -- Declare a lower max limit as we will be operating at TestPointId level.
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @pointIdStart BIGINT = ISNULL(@stateData, 0)
    DECLARE @pointIdEnd BIGINT
    DECLARE @pointCount INT

    SELECT @pointIdEnd = MAX(TestPointId), @pointCount = COUNT(*)
    FROM
    (
        SELECT   TOP (@batchSizeMax) TestPointId -- Operate at TestPointId level. Including revisions of test points, the rows to act on can be more than @batchSizeMax.
        FROM     AnalyticsModel.tbl_TestPointHistory
        WHERE    PartitionId = @partitionId
                 AND TestPointId > @pointIdStart
        GROUP BY TestPointId
        ORDER BY TestPointId
    ) T
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @endStateData = @pointIdEnd
    SET @complete = IIF(@pointCount < @batchSizeMax, 1, 0)

    UPDATE  t
    SET     AnalyticsUpdatedDate = @batchDt,
            AnalyticsBatchId = @batchId,
            ResultFromDate = ResultFromDate AT TIME ZONE @timeZone,
            ResultFromDateSK = AnalyticsInternal.func_GenDateSK(ResultFromDate AT TIME ZONE @timeZone),
            ResultToDate = ResultToDate AT TIME ZONE @timeZone,
            ResultToDateSK = AnalyticsInternal.func_GenDateSK(ResultToDate AT TIME ZONE @timeZone)
    FROM    AnalyticsModel.tbl_TestPointHistory t
    WHERE   PartitionId = @partitionId
            AND TestPointId BETWEEN @pointIdStart AND @pointIdEnd
            AND NOT EXISTS (
                SELECT
                CAST(ResultFromDate AS DATETIME),
                CAST(ResultToDate AS DATETIME)
                INTERSECT
                SELECT
                CAST(ResultFromDate AT TIME ZONE @timeZone AS DATETIME),
                CAST(ResultToDate AT TIME ZONE @timeZone AS DATETIME)
                )
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    RETURN 0
END

GO

