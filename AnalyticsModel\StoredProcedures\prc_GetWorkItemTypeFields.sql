/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 1E90EF61218CE1673089B7E08DABBBB5316C024E
CREATE PROCEDURE AnalyticsModel.prc_GetWorkItemTypeFields
    @partitionId     INT,
    @projectIds      typ_GuidTable READONLY,
    @workItemTypeIds typ_StringTable READONLY
AS
BEGIN

    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @witTypeIds         typ_StringTable

    INSERT  @witTypeIds (Data)
    SELECT  Data
    FROM    @workItemTypeIds

    -- if a list of work item types is provided, we will filter the set of fields to those types.
    IF (@@ROWCOUNT > 0)
    BEGIN
        SELECT  ProjectSK,
                FieldName,
                FieldReferenceName,
                FieldType,
                WorkItemTypeCategory,
                WorkItemType
        FROM    AnalyticsModel.tbl_WorkItemTypeField witf
        JOIN    @witTypeIds witIds
        ON      witf.WorkItemType = witIds.Data
        JOIN    @projectIds projectIds
        ON      witf.ProjectSK = projectIds.Id
        WHERE   witf.PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    -- if no work item types are provided, we will return the set of fields across all work item types.
    ELSE
    BEGIN
        SELECT  ProjectSK,
                FieldName,
                FieldReferenceName,
                FieldType,
                WorkItemTypeCategory,
                WorkItemType
        FROM    AnalyticsModel.tbl_WorkItemTypeField witf
        JOIN    @projectIds projectIds
        ON      witf.ProjectSK = projectIds.Id
        WHERE   witf.PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

END

GO

