/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A40A7A19078E39A5CF51C1B0A908B02FC8BD6624
CREATE PROCEDURE AnalyticsInternal.prc_GetPartitionsToSplitMetric
AS
BEGIN

    CREATE TABLE #PartitionCount
    (
        PCount INT NOT NULL,
        PartitionId INT NOT NULL,
        SchemeName NVARCHAR(50) NOT NULL,
        boundary_id INT NOT NULL,
        PRIMARY KEY (SchemeName, PartitionId)
    )

    CREATE TABLE #Result
    (
        PCount INT NOT NULL,
        PartitionId INT NOT NULL,
        boundary_id INT NOT NULL,
        GroupCount INT NOT NULL,
        PartitionInGroup INT NOT NULL,
        BigPartition BIT NOT NULL,
        BigGroup BIT NOT NULL,
        PrevIsBig BIT NULL,
        NextIsBig BIT NULL,
        NextSize INT NULL,
        PrevPartitionId INT NULL,
        SchemeName NVARCHAR(50)
    )

    INSERT #PartitionCount
    SELECT COUNT(*) AS PCount,
        PartitionId,
        'scheme_AnalyticsTest',
        $partition.func_AnalyticsTestPartition(PartitionId)
        FROM   AnalyticsModel.tbl_TestResult (NOLOCK)
        GROUP BY PartitionId

    INSERT #PartitionCount
    SELECT COUNT(*) AS PCount,
        PartitionId,
        'scheme_AnalyticsWorkItem',
        $partition.func_AnalyticsWorkItemPartition(PartitionId)
        FROM   AnalyticsModel.tbl_WorkItemHistory (NOLOCK)
        GROUP BY PartitionId

    INSERT #PartitionCount
    SELECT COUNT(*) AS PCount,
        PartitionId,
        'scheme_AnalyticsBuild',
        $partition.func_AnalyticsBuildPartition(PartitionId)
        FROM   AnalyticsModel.tbl_BuildTaskResult
        GROUP BY PartitionId

    INSERT #Result
    SELECT
        PCount,
        PartitionId,
        boundary_id,
        GroupCount,
        PartitionInGroup,
        BigPartition,
        BigGroup,
        LAG(BigPartition) OVER(PARTITION BY boundary_id ORDER BY PartitionId) AS PrevIsBig,
        LEAD(BigPartition) OVER(PARTITION BY boundary_id ORDER BY PartitionId) AS NextIsBig,
        LEAD(Pcount) OVER(PARTITION BY boundary_id ORDER BY PartitionId) AS NextSize,
        LAG(PartitionId) OVER(PARTITION BY boundary_id ORDER BY PartitionId) AS PrevPartitionId,
        SchemeName
    FROM
    (
        SELECT  *,
            CAST(IIF(Pcount> 1000*1000, 1, 0) AS BIT) AS BigPartition,
            CAST(IIF(GroupCount> 1000*1000, 1, 0) AS BIT) AS BigGroup
            FROM
            (
                SELECT  PCount,
                    PartitionId,
                    boundary_id,
                    SUM(PCount) OVER(Partition by SchemeName, boundary_id ORDER BY PartitionId ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS GroupCount,
                    COUNT(*) OVER(Partition by  SchemeName, boundary_id ORDER BY PartitionId ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) AS PartitionInGroup,
                    SchemeName
                    FROM
                    (
                        SELECT PCount, PartitionId, boundary_id, p.SchemeName
                        FROM #PartitionCount p
                    ) Pg
            ) Pg1
            WHERE GroupCount > 1000 * 1000
                    AND PartitionInGroup != 1
    ) PG2

    SELECT PartitionId,
           DB_NAME() AS DBNAME,
           PCount AS NOOFRecords,
           BigPartition,
           NextIsBig,
           SchemeName
    FROM #Result
    WHERE NEXTIsBig = 1
          OR BigPartition = 1
          AND NextIsBig = 0
    ORDER BY boundary_id, PartitionId

    DROP TABLE #Result
    DROP TABLE #PartitionCount

END

GO

