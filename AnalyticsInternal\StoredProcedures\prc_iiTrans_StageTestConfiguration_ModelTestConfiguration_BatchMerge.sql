/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B072D498619B261283BD555B3ABF70D7A5E2C475
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestConfiguration_ModelTestConfiguration_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #TestConfiguration
    (
        ProjectSK                   UNIQUEIDENTIFIER    NOT NULL,
        TestConfigurationId         INT                 NOT NULL,
        Name                        NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        State                       NVARCHAR(128)       COLLATE DATABASE_DEFAULT NULL,
        DataSourceId                INT                 NOT NULL
    )

    INSERT  #TestConfiguration
    (
        ProjectSK,
        TestConfigurationId,
        Name,
        State,
        DataSourceId
    )
    SELECT TOP (@batchSizeMax) WITH TIES
           c.ProjectGuid,
           c.TestConfigurationId,
           c.Name,
           c.State,
           c.DataSourceId
    FROM   AnalyticsStage.tbl_TestConfiguration c
    WHERE  PartitionId = @partitionId
           AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
           AND TestConfigurationId > ISNULL(@stateData, 0)
    ORDER BY TestConfigurationId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(TestConfigurationId) FROM #TestConfiguration)

    MERGE AnalyticsModel.tbl_TestConfiguration t
    USING #TestConfiguration s
    ON (t.PartitionId = @partitionId AND t.ProjectSK = s.ProjectSK AND t.TestConfigurationId = s.TestConfigurationId AND t.DataSourceId = s.DataSourceId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.Name,
        s.State
        INTERSECT
        SELECT
        t.Name,
        t.State
    ) THEN
    UPDATE SET
        AnalyticsUpdatedDate     = @batchDt,
        AnalyticsBatchId         = @batchId,
        Name                     = s.Name,
        State                    = s.State
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        ProjectSK,
        TestConfigurationId,
        Name,
        State,
        DataSourceId
    )
    VALUES (
        @partitionId,
        @batchDt,
        @batchDt,
        @batchId,
        s.ProjectSK,
        s.TestConfigurationId,
        s.Name,
        s.State,
        s.DataSourceId
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    DROP TABLE #TestConfiguration

    RETURN 0
END

GO

