/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F52B568A3E4AE6CAAF60A0CC4F4AC5E0E3F4DEAD
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemLinkType_WorkItemLinkHistory_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @localizedUnknown   NVARCHAR(230)

    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_UNKNOWN',
                                                            @defaultValue = 'Unknown',
                                                            @localizedValue =  @localizedUnknown OUTPUT

    SET @deletedCount = 0
    SET @insertedCount = 0

    UPDATE  TOP (@batchSizeMax) t
    SET     LinkTypeReferenceName = 'Unknown',
            LinkTypeName = @localizedUnknown,
            LinkTypeIsAcyclic = NULL,
            LinkTypeIsDirectional = NULL
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory t
    LEFT JOIN AnalyticsStage.tbl_WorkItemLinkType s
    ON      t.PartitionId = s.PartitionId
            AND (t.LinkTypeId = s.LinkTypeId OR t.LinkTypeId = 0 - s.LinkTypeId)
    WHERE   t.PartitionId = @partitionId
            AND s.LinkTypeId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    RETURN 0
END

GO

