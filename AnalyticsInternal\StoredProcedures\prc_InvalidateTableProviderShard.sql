/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: AB26039362283A5973E3B5C35E730FBA7047BD08
CREATE PROCEDURE AnalyticsInternal.prc_InvalidateTableProviderShard
    @partitionId INT,
    @tableName VARCHAR(64),
    @providerShardId INT,
    @disableCurrentStream BIT,
    @keysOnly BIT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @now DATETIME = GETUTCDATE()
    DECLARE @data AnalyticsInternal.typ_CreateStreamsData
    DECLARE @partition TABLE
    (
        PartitionId INT
    )

    IF (@partitionId IS NULL)
    BEGIN
        INSERT @partition
        SELECT DISTINCT PartitionId
        FROM tbl_TableProviderShardStream
    END
    ELSE
    BEGIN
        INSERT @partition
        SELECT @partitionId
    END

    WHILE (EXISTS (SELECT * FROM @partition))
    BEGIN
        SELECT TOP 1 @partitionId = PartitionId
        FROM @partition

        DELETE @data

        BEGIN TRANSACTION

        BEGIN TRY
            UPDATE  AnalyticsInternal.tbl_TableProviderShardStream WITH (ROWLOCK)
            SET     [Enabled] = IIF([Current] = 1 AND @disableCurrentStream = 0, 1, 0),
                    UpdateTime = @now
            OUTPUT  INSERTED.TableName,
                    INSERTED.AnalyticsProviderShardId,
                    -- the new stream will be created with the MAX priority of updated streams
                    -- if we are killing the current stream, then make sure we create a new high priority stream, otherwise use existing priorities
                    IIF(INSERTED.[Current] = 1, IIF(INSERTED.[Enabled] = 0, 8, 2), INSERTED.Priority)
            INTO    @data
            WHERE   PartitionId = @partitionId
                    AND [Enabled] = 1
                    AND (@tableName IS NULL OR TableName = @tableName)
                    AND (@providerShardId IS NULL OR (@tableName IS NOT NULL AND AnalyticsProviderShardId = @providerShardId))
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            EXECUTE AnalyticsInternal.prc_iCreateStreams
                    @partitionId,
                    @data,
                    1,
                    @keysOnly,
                    @now

            COMMIT TRANSACTION
        END TRY
        BEGIN CATCH
            ROLLBACK TRANSACTION;
            THROW
        END CATCH

        DELETE @partition
        WHERE PartitionId = @partitionId
    END

END

GO

