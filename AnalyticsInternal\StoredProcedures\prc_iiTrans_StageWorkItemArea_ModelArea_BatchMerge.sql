/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: AE01F77B1A044F9FA437E52F921C293796FE25A1
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemArea_ModelArea_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    DECLARE @localizedUnknown   NVARCHAR(230)

    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_UNKNOWN',
                                                            @defaultValue = 'Unknown',
                                                            @localizedValue =  @localizedUnknown OUTPUT

    CREATE TABLE #ImpactedWithParents
    (
        PartitionId       INT              NOT NULL,
        AreaGuid          UNIQUEIDENTIFIER NOT NULL,
        AreaId            INT              NULL,
        AreaName          NVARCHAR(256)    COLLATE DATABASE_DEFAULT NULL,
        ParentAreaGuid    UNIQUEIDENTIFIER NULL,
        ProjectGuid       UNIQUEIDENTIFIER NULL,
        IsDeleted         BIT              NULL,
        IsRootNode        BIT              NOT NULL,
        Impacted          INT              NOT NULL
    )

    CREATE TABLE #Trees
    (
        PartitionId       INT              NOT NULL,
        AreaGuid          UNIQUEIDENTIFIER NOT NULL,
        AreaId            INT              NULL,
        AreaName          NVARCHAR(256)    COLLATE DATABASE_DEFAULT NULL,
        AreaPath          NVARCHAR(4000)   COLLATE DATABASE_DEFAULT NULL,
        ParentAreaGuid    UNIQUEIDENTIFIER NULL,
        ProjectGuid       UNIQUEIDENTIFIER NULL,
        IsDeleted         BIT              NULL,
        IsRootNode        BIT              NOT NULL,
        Impacted          INT              NOT NULL
    )

    CREATE UNIQUE CLUSTERED INDEX CL_Trees ON #Trees (PartitionId, AreaGuid)

    DECLARE @changedAreaGuids TABLE
    (
        AreaGuid UNIQUEIDENTIFIER PRIMARY KEY
    )

    IF (@triggerTableName = 'Project')
    BEGIN
        INSERT INTO @changedAreaGuids
        SELECT a.AreaGuid
        FROM AnalyticsStage.tbl_Project p
        JOIN AnalyticsStage.tbl_WorkItemArea a
            ON a.PartitionId = p.PartitionId
            AND a.ProjectGuid = p.ProjectGuid
        WHERE p.PartitionId = @partitionId
            AND p.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE IF (@triggerTableName = 'WorkItemArea')
    BEGIN
        INSERT INTO @changedAreaGuids
        SELECT a.AreaGuid
        FROM AnalyticsStage.tbl_WorkItemArea a
        WHERE a.PartitionId = @partitionId
            AND a.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    -- Recursive CTE to chase up to roots from impacted guids
    ;WITH ImpactedWithParents AS
    (
        SELECT a.*, IIF(a.ParentAreaGuid = a.ProjectGuid OR a.ParentAreaGuid IS NULL, 1, 0) AS IsRootNode, 1 AS Impacted
        FROM AnalyticsStage.tbl_WorkItemArea a
        WHERE a.PartitionId = @partitionId AND a.AreaGuid IN (SELECT AreaGuid FROM @changedAreaGuids)

        UNION ALL

        SELECT a.*, IIF(a.ParentAreaGuid = a.ProjectGuid OR a.ParentAreaGuid IS NULL, 1, 0) AS IsRootNode, 0 AS Impacted
        FROM ImpactedWithParents ch
        JOIN AnalyticsStage.tbl_WorkItemArea a ON a.PartitionId = ch.PartitionId AND a.AreaGuid = ch.ParentAreaGuid
    )
    INSERT #ImpactedWithParents
    SELECT PartitionId, AreaGuid, AreaId, AreaName, ParentAreaGuid, ProjectGuid, IsDeleted, IsRootNode, MAX(Impacted) AS Impacted
    FROM ImpactedWithParents
    GROUP BY PartitionId, AreaGuid, AreaId, AreaName, ParentAreaGuid, ProjectGuid, IsDeleted, IsRootNode
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- first insert the roots
    INSERT #Trees
    SELECT PartitionId, AreaGuid, AreaId, AreaName, NULL AS AreaPath, ParentAreaGuid, ProjectGuid, IsDeleted, IsRootNode, Impacted
    FROM #ImpactedWithParents
    WHERE IsRootNode = 1

    -- now insert other impacted nodes and parents
    WHILE (@@ROWCOUNT > 0)
    BEGIN
        INSERT #Trees
        SELECT a.PartitionId, a.AreaGuid, a.AreaId, a.AreaName, IIF(p.IsRootNode = 1, a.AreaName, CONCAT(p.AreaPath, '\', a.AreaName)) AS AreaPath, a.ParentAreaGuid, a.ProjectGuid, a.IsDeleted, 0 AS IsRootNode, IIF(c.AreaGuid IS NOT NULL, 1, p.Impacted) AS Impacted
        FROM #ImpactedWithParents a
        JOIN #Trees p ON a.PartitionId = p.PartitionId AND a.ParentAreaGuid = p.AreaGuid --parent
        LEFT JOIN #Trees s ON s.PartitionId = a.PartitionId AND s.AreaGuid = a.AreaGuid --self
        LEFT JOIN @changedAreaGuids c ON c.AreaGuid = a.AreaGuid
        WHERE s.AreaGuid IS NULL -- make sure it's not already in the table - working on new leaves only
    END

    DECLARE @Noop INT = (SELECT 1) -- to inialize @@ROWCOUNT

    -- now insert children of impacted nodes
    WHILE (@@ROWCOUNT > 0)
    BEGIN
        INSERT #Trees
        SELECT a.PartitionId, a.AreaGuid, a.AreaId, a.AreaName, IIF(p.IsRootNode = 1, a.AreaName, CONCAT(p.AreaPath, '\', a.AreaName)) AS AreaPath, a.ParentAreaGuid, a.ProjectGuid, a.IsDeleted, 0 AS IsRootNode, 1 AS Impacted
        FROM AnalyticsStage.tbl_WorkItemArea a
        JOIN #Trees p ON a.PartitionId = p.PartitionId AND a.ParentAreaGuid = p.AreaGuid --parent
        LEFT JOIN #Trees s ON s.PartitionId = a.PartitionId AND s.AreaGuid = a.AreaGuid --self
        WHERE s.AreaGuid IS NULL -- make sure it's not already in the table - working on new leaves only
            AND p.Impacted = 1
            AND p.IsRootNode = 0 -- root nodes changes don't impact children (name is not used)
    END

    ;WITH Splits AS
    (
        SELECT
            a.PartitionId,
            AreaGuid,
            MAX(CASE WHEN Ordinal = 0 THEN Value END) AreaLevel2,
            MAX(CASE WHEN Ordinal = 1 THEN Value END) AreaLevel3,
            MAX(CASE WHEN Ordinal = 2 THEN Value END) AreaLevel4,
            MAX(CASE WHEN Ordinal = 3 THEN Value END) AreaLevel5,
            MAX(CASE WHEN Ordinal = 4 THEN Value END) AreaLevel6,
            MAX(CASE WHEN Ordinal = 5 THEN Value END) AreaLevel7,
            MAX(CASE WHEN Ordinal = 6 THEN Value END) AreaLevel8,
            MAX(CASE WHEN Ordinal = 7 THEN Value END) AreaLevel9,
            MAX(CASE WHEN Ordinal = 8 THEN Value END) AreaLevel10,
            MAX(CASE WHEN Ordinal = 9 THEN Value END) AreaLevel11,
            MAX(CASE WHEN Ordinal = 10 THEN Value END) AreaLevel12,
            MAX(CASE WHEN Ordinal = 11 THEN Value END) AreaLevel13,
            MAX(CASE WHEN Ordinal = 12 THEN Value END) AreaLevel14,
            MAX(Ordinal) AS Depth
        FROM #Trees a
        CROSS APPLY AnalyticsInternal.func_SplitString(a.AreaPath, '\')
        WHERE a.Impacted = 1
        GROUP BY a.PartitionId, AreaGuid
    )
    , Src AS
    (
        SELECT
            a.PartitionId,
            a.AreaGuid AS AreaId,
            ISNULL(a.AreaName, ISNULL(p.ProjectName, @localizedUnknown)) AS AreaName,
            a.AreaId AS Number,
            CASE WHEN a.AreaName IS NULL THEN ISNULL(p.ProjectName, @localizedUnknown) ELSE CONCAT(ISNULL(p.ProjectName, @localizedUnknown), '\', a.AreaPath) END AS AreaPath,
            ISNULL(p.ProjectName, @localizedUnknown) AS AreaLevel1,
            AreaLevel2,
            AreaLevel3,
            AreaLevel4,
            AreaLevel5,
            AreaLevel6,
            AreaLevel7,
            AreaLevel8,
            AreaLevel9,
            AreaLevel10,
            AreaLevel11,
            AreaLevel12,
            AreaLevel13,
            AreaLevel14,
            IIF(a.AreaName IS NULL, Depth, Depth + 1) AS Depth,
            ISNULL(a.IsDeleted,0) AS IsDeleted,
            a.ProjectGuid AS ProjectSK
        FROM #Trees a
        LEFT JOIN AnalyticsStage.tbl_Project p ON p.PartitionId = a.PartitionId AND p.ProjectGuid = a.ProjectGuid
        LEFT JOIN Splits ON a.PartitionId = Splits.PartitionId AND a.AreaGuid = Splits.AreaGuid
        WHERE a.Impacted = 1
    )
    MERGE TOP (@batchSizeMax) AnalyticsModel.tbl_Area AS t
    USING Src AS s
    ON (t.PartitionId = s.PartitionId AND t.AreaId = s.AreaId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.AreaName
        , s.Number
        , s.AreaPath
        , s.AreaLevel1
        , s.AreaLevel2
        , s.AreaLevel3
        , s.AreaLevel4
        , s.AreaLevel5
        , s.AreaLevel6
        , s.AreaLevel7
        , s.AreaLevel8
        , s.AreaLevel9
        , s.AreaLevel10
        , s.AreaLevel11
        , s.AreaLevel12
        , s.AreaLevel13
        , s.AreaLevel14
        , s.Depth
        , s.IsDeleted
        , s.ProjectSK
        INTERSECT
        SELECT
        t.AreaName
        , t.Number
        , t.AreaPath
        , t.AreaLevel1
        , t.AreaLevel2
        , t.AreaLevel3
        , t.AreaLevel4
        , t.AreaLevel5
        , t.AreaLevel6
        , t.AreaLevel7
        , t.AreaLevel8
        , t.AreaLevel9
        , t.AreaLevel10
        , t.AreaLevel11
        , t.AreaLevel12
        , t.AreaLevel13
        , t.AreaLevel14
        , t.Depth
        , t.IsDeleted
        , t.ProjectSK
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt
        , AnalyticsBatchId = @batchId
        , AreaName = s.AreaName
        , Number = s.Number
        , AreaId = s.AreaId
        , AreaPath = s.AreaPath
        , AreaLevel1 = s.AreaLevel1
        , AreaLevel2 = s.AreaLevel2
        , AreaLevel3 = s.AreaLevel3
        , AreaLevel4 = s.AreaLevel4
        , AreaLevel5 = s.AreaLevel5
        , AreaLevel6 = s.AreaLevel6
        , AreaLevel7 = s.AreaLevel7
        , AreaLevel8 = s.AreaLevel8
        , AreaLevel9 = s.AreaLevel9
        , AreaLevel10 = s.AreaLevel10
        , AreaLevel11 = s.AreaLevel11
        , AreaLevel12 = s.AreaLevel12
        , AreaLevel13 = s.AreaLevel13
        , AreaLevel14 = s.AreaLevel14
        , Depth = s.Depth
        , IsDeleted = s.IsDeleted
        , ProjectSK = s.ProjectSK
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId
        , AnalyticsBatchId
        , AnalyticsCreatedDate
        , AnalyticsUpdatedDate
        , AreaId
        , AreaName
        , Number
        , AreaPath
        , AreaLevel1
        , AreaLevel2
        , AreaLevel3
        , AreaLevel4
        , AreaLevel5
        , AreaLevel6
        , AreaLevel7
        , AreaLevel8
        , AreaLevel9
        , AreaLevel10
        , AreaLevel11
        , AreaLevel12
        , AreaLevel13
        , AreaLevel14
        , Depth
        , IsDeleted
        , ProjectSK
    )
    VALUES (
        s.PartitionId
        , @batchId
        , @batchDt
        , @batchDt
        , s.AreaId
        , s.AreaName
        , s.Number
        , s.AreaPath
        , s.AreaLevel1
        , s.AreaLevel2
        , s.AreaLevel3
        , s.AreaLevel4
        , s.AreaLevel5
        , s.AreaLevel6
        , s.AreaLevel7
        , s.AreaLevel8
        , s.AreaLevel9
        , s.AreaLevel10
        , s.AreaLevel11
        , s.AreaLevel12
        , s.AreaLevel13
        , s.AreaLevel14
        , s.Depth
        , s.IsDeleted
        , s.ProjectSK
    )
    OUTPUT $action INTO @changes;

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    DROP TABLE #ImpactedWithParents
    DROP TABLE #Trees

    RETURN 0
END

GO

