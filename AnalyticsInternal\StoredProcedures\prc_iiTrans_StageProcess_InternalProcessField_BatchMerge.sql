/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B0B9541EF17CFA87A0162424F6AB2AD4D3C7CEA1
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageProcess_InternalProcessField_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @changes TABLE
    (
        ReferenceName   NVARCHAR(256),
        ProcessId       UNIQUEIDENTIFIER,
        MergeAction     NVARCHAR(10)
    );

    CREATE TABLE #Field
    (
        ProcessId           UNIQUEIDENTIFIER                                    NOT NULL,
        ReferenceName       NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NOT NULL,
        SourceFieldName     NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NOT NULL,
        SourceKeyFieldName  NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NULL,
        Name                NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NULL,
        Description         NVARCHAR(1024)          COLLATE DATABASE_DEFAULT    NULL,
        IsSystem            BIT                                                 NOT NULL,
        FieldType           NVARCHAR(50)            COLLATE DATABASE_DEFAULT    NOT NULL,
        IsHistoryEnabled    BIT                                                 NOT NULL,
        IsPerson            BIT                                                 NOT NULL,
        INDEX IX_Field_SourceFieldName CLUSTERED (SourceFieldName)
    )

    ;WITH ProcessField AS
    (
        SELECT
            p.ProcessId,
            p.AnalyticsBatchIdChanged,
            field.x.value('ReferenceName[1]','nvarchar(256)')                   AS ReferenceName,
            REPLACE(field.x.value('ReferenceName[1]','nvarchar(256)'),'.','_')  AS SourceFieldName,
            field.x.value('Name[1]','nvarchar(256)')                            AS Name,
            field.x.value('Description[1]','nvarchar(256)')                     AS Description,
            field.x.value('IsSystem[1]','BIT')                                  AS IsSystem,
            field.x.value('Type[1]','nvarchar(256)')                            AS FieldType,
            ISNULL(field.x.value('IsHistoryEnabled[1]','BIT'), 1)               AS IsHistoryEnabled,
            field.x.value('IsPerson[1]','BIT')                                  AS IsPerson
        FROM AnalyticsStage.tbl_Process p
        CROSS APPLY Fields.nodes('//Item') AS field(x)
        WHERE p.PartitionId = @partitionId
    ),
    CorrectedField AS
    (
        -- coerce null IsPerson values to a non-null value for the same ReferenceName (peferring 1, defaulting to 0)
        SELECT
            ProcessId,
            AnalyticsBatchIdChanged,
            ReferenceName,
            SourceFieldName,
            Name,
            Description,
            IsSystem,
            FieldType,
            IsHistoryEnabled,
            ISNULL(IsPerson, ISNULL(MAX(CAST(IsPerson AS INT)) OVER (PARTITION BY ReferenceName), 0)) AS IsPerson
        FROM ProcessField
    )
    INSERT INTO #Field
    SELECT
        ProcessId,
        ReferenceName,
        SourceFieldName,
        IIF((FieldType = 'String' AND IsPerson = 1) OR (FieldType = 'Identity'), SourceFieldName + 'Guid', NULL) AS SourceKeyFieldName,
        Name,
        Description,
        IsSystem,
        -- Return back to FieldType when TFS start send Identity
        IIF(FieldType = 'String' AND IsPerson = 1, 'Identity', FieldType),
        IsHistoryEnabled,
        IsPerson
    FROM CorrectedField p
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    --filter out columns that starts with System_ but is not in AnalyticsStage.tbl_WorkItemRevision
    DELETE  f
    FROM    #Field f
    LEFT JOIN sys.columns c
            ON (SourceFieldName = c.Name OR SourceKeyFieldName = c.Name)
            AND c.object_id=OBJECT_ID('AnalyticsStage.tbl_WorkItemRevision')
    WHERE   (CHARINDEX('System_', SourceFieldName) = 1 AND c.Name IS NULL)

    UPDATE pf
    SET pf.IsDeleted = 1,
        pf.AnalyticsUpdatedDate = @batchDt,
        pf.AnalyticsBatchId = @batchId
    OUTPUT INSERTED.ReferenceName, INSERTED.ProcessId, 'UPDATE' INTO @changes
    FROM AnalyticsInternal.tbl_ProcessField pf
    INNER JOIN #Field f ON pf.ProcessId = f.ProcessId
        AND pf.ReferenceName = f.ReferenceName
        AND pf.FieldType <> f.FieldType
        AND pf.IsDeleted <> 1
        AND pf.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #IncompleteProcess
    (
        ProcessId UNIQUEIDENTIFIER  NOT NULL
    )

    INSERT INTO #IncompleteProcess
    SELECT DISTINCT
        ProcessId
    FROM AnalyticsStage.tbl_Process
    WHERE PartitionId = @partitionId
        AND Fields IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #ExistingField
    (
        ReferenceName   NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NOT NULL,
        FieldType       NVARCHAR(50)            COLLATE DATABASE_DEFAULT    NOT NULL,
        ModelTableName  NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NULL,
        ModelColumnName NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NULL,
        PRIMARY KEY CLUSTERED(ReferenceName, FieldType)
    )

    INSERT INTO #ExistingField
    SELECT DISTINCT
        ReferenceName,
        FieldType,
        ModelTableName,
        ModelColumnName
    FROM AnalyticsInternal.tbl_ProcessField WHERE PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    CREATE TABLE #Src
    (
        ProcessId           UNIQUEIDENTIFIER                                    NOT NULL,
        ReferenceName       NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NOT NULL,
        FieldType           NVARCHAR(50)            COLLATE DATABASE_DEFAULT    NOT NULL,
        SourceFieldName     NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NOT NULL,
        SourceKeyFieldName  NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NULL,
        Name                NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NOT NULL,
        Description         NVARCHAR(1024)          COLLATE DATABASE_DEFAULT    NULL,
        IsSystem            BIT                                                 NOT NULL,
        IsDeleted           BIT                                                 NOT NULL,
        ModelTableName      NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NULL,
        ModelColumnName     NVARCHAR(256)           COLLATE DATABASE_DEFAULT    NULL,
        IsHistoryEnabled    BIT                                                 NOT NULL,
        IsPerson            BIT                                                 NOT NULL,
    )

    ;WITH ModelCustomTable AS
    (
        SELECT
            'WorkItemRevisionCustom01' AS ModelTableName,
            1 AS TableOrder
        UNION ALL
        SELECT
            'WorkItemRevisionCustom02' AS ModelTableName,
            2 AS TableOrder
        UNION ALL
        SELECT
            'WorkItemRevisionCustom03' AS ModelTableName,
            3 AS TableOrder
        UNION ALL
        SELECT
            'WorkItemRevisionCustom04' AS ModelTableName,
            4 AS TableOrder
        UNION ALL
        SELECT
            'WorkItemRevisionCustom05' AS ModelTableName,
            5 AS TableOrder
        UNION ALL
        SELECT
            'WorkItemRevisionCustom06' AS ModelTableName,
            6 AS TableOrder
        UNION ALL
        SELECT
            'WorkItemRevisionCustom07' AS ModelTableName,
            7 AS TableOrder
        UNION ALL
        SELECT
            'WorkItemRevisionCustom08' AS ModelTableName,
            8 AS TableOrder
    )
    ,FieldType AS
    (
        SELECT 'String' AS FieldType, 200 AS PerTable
        UNION ALL
        SELECT 'Integer' AS FieldType, 50 AS PerTable
        UNION ALL
        SELECT 'Double' AS FieldType, 50 AS PerTable
        UNION ALL
        SELECT 'DateTime' AS FieldType, 50 AS PerTable
        UNION ALL
        SELECT 'Boolean' AS FieldType, 50 AS PerTable
        UNION ALL
        SELECT 'Identity' AS FieldType, 50 AS PerTable
    )
    -- Get last used column number per table per column type
    ,MaxField AS
    (
        SELECT
            F.FieldType,
            ISNULL(M.ModelLastNumber, 0) as ModelLastNumber,
            F.PerTable
        FROM FieldType F
        LEFT JOIN
        (
            SELECT
                FieldType,
                MAX(CAST(REPLACE(ModelColumnName, FieldType, '') AS INT)) AS ModelLastNumber
            FROM AnalyticsInternal.tbl_ProcessField
            WHERE PartitionId = @partitionId
            GROUP BY FieldType
        ) M ON F.FieldType = M.FieldType
    )
    ,SrcField AS
    (
        SELECT
            f.ProcessId,
            f.ReferenceName,
            f.SourceFieldName,
            f.SourceKeyFieldName,
            ISNULL(f.Name, f.ReferenceName) AS Name,
            f.Description,
            f.FieldType,
            f.IsSystem,
            IIF(ef.ReferenceName IS NULL AND c.Name IS NULL AND IsSystem = 0,
                -- It's new column => generate number
                DENSE_RANK() -- Group fields with the same name into one column
                            OVER
                            (
                                PARTITION BY f.FieldType, IIF(ef.ReferenceName IS NULL AND c.Name IS  NULL AND IsSystem = 0, 0, 1) --Push columns that already exists in separate category to exclude them from number generation
                                ORDER BY f.ReferenceName
                            ),
                -- Existing field => do nothing
                 0)  AS RowNumber,
            ef.ModelColumnName AS ModelCurrentColumnName,
            ef.ModelTableName AS ModelCurrentTableName,
            f.IsHistoryEnabled,
            f.IsPerson,
            IIF(f.IsSystem = 0 AND c.Name IS NULL, 1, 0) AS IsCustomField
        FROM #Field f
        LEFT JOIN    #ExistingField ef
        ON           ef.ReferenceName = f.ReferenceName
                     AND ef.FieldType = f.FieldType
        LEFT JOIN    sys.columns c
        ON           f.SourceFieldName = c.Name
                     AND c.object_id = OBJECT_ID('AnalyticsStage.tbl_WorkItemRevision')
    )
    INSERT #Src
    (
        ProcessId,
        ReferenceName,
        FieldType,
        SourceFieldName,
        SourceKeyFieldName,
        Name,
        Description,
        IsSystem,
        IsDeleted,
        ModelTableName,
        ModelColumnName,
        IsHistoryEnabled,
        IsPerson
    )
    SELECT
        s.ProcessId,
        s.ReferenceName,
        s.FieldType,
        s.SourceFieldName,
        s.SourceKeyFieldName,
        s.Name,
        s.Description,
        s.IsSystem,
        0, -- IsDeleted
        CASE WHEN s.IsCustomField = 1 THEN ISNULL(s.ModelCurrentTableName, mt.ModelTableName) ELSE NULL END AS ModelTableName,
        CASE WHEN s.IsCustomField = 1 THEN ISNULL(s.ModelCurrentColumnName, m.FieldType + FORMAT(m.ModelLastNumber + RowNumber,'0000')) ELSE NULL END AS ModelColumnName,
        s.IsHistoryEnabled,
        s.IsPerson
    FROM SrcField s
    JOIN MaxField m
        ON s.FieldType = m.FieldType
    LEFT JOIN ModelCustomTable mt
        ON mt.TableOrder = (m.ModelLastNumber + RowNumber) / m.PerTable + IIF((m.ModelLastNumber + RowNumber) % m.PerTable =0, 0, 1)
    WHERE s.ModelCurrentColumnName IS NOT NULL -- Keep existing columns
        OR mt.ModelTableName IS NOT NULL -- We have space in CustomOX for new columns
        OR s.IsCustomField = 0
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

   ;WITH Tgt AS
    (
        -- MERGE has a bug where it tries accessing entities in all partitions (even offline ones) for the target table.
        -- To overcome this problem the target table has to be pre-filtered by correct partition id.
        SELECT * FROM AnalyticsInternal.tbl_ProcessField WHERE PartitionId = @partitionId
    )
    MERGE Tgt AS t
    USING #Src AS s
    ON (t.PartitionId = @partitionId
        AND t.ProcessId = s.ProcessId
        AND t.ReferenceName = s.ReferenceName
        AND t.FieldType = s.FieldType
        )
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.Name,
        s.Description,
        s.IsDeleted,
        s.IsHistoryEnabled,
        s.IsPerson,
        s.SourceFieldName,
        s.SourceKeyFieldName,
        s.ReferenceName
        INTERSECT
        SELECT
        t.Name,
        t.Description,
        t.IsDeleted,
        t.IsHistoryEnabled,
        t.IsPerson,
        t.SourceFieldName,
        t.SourceKeyFieldName,
        t.ReferenceName COLLATE Latin1_General_100_CS_AS
        )
    THEN UPDATE SET
        AnalyticsUpdatedDate = @batchDt,
        AnalyticsBatchId = @batchId,
        Name = s.Name,
        Description = s.Description,
        IsDeleted = s.IsDeleted,
        IsHistoryEnabled = s.IsHistoryEnabled,
        IsPerson = s.IsPerson,
        SourceFieldName = s.SourceFieldName,
        SourceKeyFieldName = s.SourceKeyFieldName,
        ReferenceName = s.ReferenceName
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        ProcessId,
        ReferenceName,
        SourceFieldName,
        SourceKeyFieldName,
        Name,
        Description,
        FieldType,
        IsSystem,
        IsDeleted,
        ModelTableName,
        ModelColumnName,
        IsHistoryEnabled,
        IsPerson
        )
    VALUES (
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.ProcessId,
        s.ReferenceName,
        s.SourceFieldName,
        s.SourceKeyFieldName,
        s.Name,
        s.Description,
        s.FieldType,
        s.IsSystem,
        0, --IsDeleted
        ModelTableName,
        ModelColumnName,
        IsHistoryEnabled,
        IsPerson
        )
    WHEN NOT MATCHED BY SOURCE
        AND t.IsDeleted = 0
        AND ProcessId NOT IN (SELECT ProcessId FROM #IncompleteProcess)
    THEN UPDATE SET
        IsDeleted = 1,
        AnalyticsUpdatedDate = @batchDt,
        AnalyticsBatchId = @batchId
    OUTPUT INSERTED.ReferenceName, INSERTED.ProcessId, $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    DROP TABLE #Field
    DROP TABLE #IncompleteProcess
    DROP TABLE #ExistingField
    DROP TABLE #Src

     -- Check that after transformation columns with the same ReferenceName have the same type
    DECLARE @referenceNameErrors TABLE
    (
        ReferenceName NVARCHAR(256)
    )
    INSERT INTO @referenceNameErrors
    SELECT ReferenceName
    FROM
    (
        SELECT
            ReferenceName,
            FieldType
        FROM AnalyticsInternal.tbl_ProcessField
        WHERE PartitionId = @partitionId AND ReferenceName IN (SELECT ReferenceName FROM @changes)  AND IsDeleted = 0
        GROUP BY ReferenceName, FieldType
    ) T
    GROUP BY ReferenceName
    HAVING COUNT(*) > 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF EXISTS (SELECT * FROM @referenceNameErrors)
    BEGIN
            DECLARE @badColumns NVARCHAR(MAX) = ''
            SELECT @badColumns += ReferenceName + ' ' FROM @referenceNameErrors

            SET @tfError = dbo.func_GetMessage(1670008); RAISERROR(@tfError, 16, -1, @procedureName, @badColumns)
            RETURN 1670008
    END

    -- Check that after transformation columns with the same ReferenceName have the same IsHistoryEnabled
    DELETE FROM @referenceNameErrors
    INSERT INTO @referenceNameErrors
    SELECT ReferenceName
    FROM
    (
        SELECT
            ReferenceName,
            IsHistoryEnabled
        FROM AnalyticsInternal.tbl_ProcessField
        WHERE PartitionId = @partitionId AND ReferenceName IN (SELECT ReferenceName FROM @changes)  AND IsDeleted = 0
        GROUP BY ReferenceName, IsHistoryEnabled
    ) T
    GROUP BY ReferenceName
    HAVING COUNT(*) > 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF EXISTS (SELECT * FROM @referenceNameErrors)
    BEGIN
            SELECT @badColumns = ''
            SELECT @badColumns += ReferenceName + ' ' FROM @referenceNameErrors

            SET @tfError = dbo.func_GetMessage(1670008); RAISERROR(@tfError, 16, -1, @procedureName, @badColumns)
            RETURN 1670008
    END

    SET @complete = 1
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    IF @insertedCount >0 OR @updatedCount > 0  OR @deletedCount >0
    BEGIN
        -- We have process updates => send notifications
        DECLARE @processes NVARCHAR(MAX) = ''

        SELECT @processes += CAST(ProcessId AS VARCHAR(40)) + ','
        FROM ( SELECT DISTINCT ProcessID FROM @changes) T

        EXEC prc_iiSendNotification @partitionId, '88DA0908-358C-4A6C-B431-01072D2FDA9D', @processes
    END

    RETURN 0
END

GO

