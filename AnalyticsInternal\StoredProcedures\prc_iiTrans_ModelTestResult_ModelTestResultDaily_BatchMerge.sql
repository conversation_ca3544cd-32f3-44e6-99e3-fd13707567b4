/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 744D8ABA47533A43594B1217DCC272774DF741DE
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTestResult_ModelTestResultDaily_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    -- Get impacted tests first to do sorting on narrow results sets
    -- Helps with performance for big refransforms
    CREATE TABLE #ImpactedTests
    (
        TestSK                          INT                 NOT NULL PRIMARY KEY,
        MinDateSK                       INT                 NOT NULL,
        MaxDateSK                       INT                 NOT NULL,
    )

    INSERT INTO #ImpactedTests(TestSK, MinDateSK, MaxDateSK)
    SELECT  TOP (@batchSizeMax) WITH TIES
                TestSK,
                MIN(CompletedDateSK),
                MAX(CompletedDateSK)
    FROM    AnalyticsModel.tbl_TestResult
    WHERE   PartitionId = @partitionId
            AND TestSK > ISNULL(@stateData, -1)
            AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND TestSK IS NOT NULL
            AND CompletedDateSK IS NOT NULL
    GROUP BY TestSK
    ORDER BY TestSK
    OPTION  (HASH GROUP, OPTIMIZE FOR (@partitionId UNKNOWN, @stateData = 0))

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END

    DECLARE @minDateSK INT
    DECLARE @maxDateSK INT

    SELECT @minDateSK = MIN(MinDateSK), @maxDateSK = MAX(MaxDateSK) FROM #ImpactedTests

    SET @endStateData = (SELECT MAX(TestSK) FROM #ImpactedTests)

    CREATE TABLE #TestResultDaily
    (
        TestResultDailySK               BIGINT              NULL,
        BuildPipelineSK                 INT                 NULL,
        ReleasePipelineSK               INT                 NULL,
        ReleaseStageSK                  INT                 NULL,
        BranchSK                        INT                 NULL,
        TestSK                          INT                 NOT NULL,
        ProjectSK                       UNIQUEIDENTIFIER    NOT NULL,
        DateSK                          INT                 NOT NULL,
        DataSourceId                    INT                 NOT NULL,
        -- aggregations of test results
        ResultDurationSeconds           DECIMAL(18,3)       NULL,
        ResultCount                     INT                 NULL,
        ResultPassCount                 INT                 NULL,
        ResultFailCount                 INT                 NULL,
        -- counts for invididual outcomes
        ResultNoneCount                 INT                 NULL,
        ResultInconclusiveCount         INT                 NULL,
        ResultTimeoutCount              INT                 NULL,
        ResultAbortedCount              INT                 NULL,
        ResultBlockedCount              INT                 NULL,
        ResultNotExecutedCount          INT                 NULL,
        ResultWarningCount              INT                 NULL,
        ResultErrorCount                INT                 NULL,
        ResultNotApplicableCount        INT                 NULL,
        ResultNotImpactedCount          INT                 NULL,
        TestRunType                     INT                 NULL,
        WorkFlow                        INT                 NULL,
        INDEX CI_TestResultDaily CLUSTERED (DateSK, TestSK, ProjectSK, BuildPipelineSK, ReleasePipelineSK, ReleaseStageSK, BranchSK, DataSourceId)
    )
    WITH (DATA_COMPRESSION=ROW)

    INSERT  #TestResultDaily
    (
        TestSK,
        ProjectSK,
        DateSK,
        BuildPipelineSK,
        ReleasePipelineSK,
        ReleaseStageSK,
        BranchSK,
        DataSourceId,
        ResultDurationSeconds,
        ResultCount,
        ResultPassCount,
        ResultFailCount,
        ResultNoneCount,
        ResultInconclusiveCount,
        ResultTimeoutCount,
        ResultAbortedCount,
        ResultBlockedCount,
        ResultNotExecutedCount,
        ResultWarningCount,
        ResultErrorCount,
        ResultNotApplicableCount,
        ResultNotImpactedCount,
        TestRunType,
        WorkFlow
    )
    SELECT
                t.TestSK,
                ProjectSK,
                CompletedDateSK AS DateSK,
                BuildPipelineSK,
                ReleasePipelineSK,
                ReleaseStageSK,
                BranchSK,
                DataSourceId,
                SUM(DurationSeconds)                                                AS ResultDurationSeconds,
                COUNT(*) AS ResultCount,
                -- Have to rely on string, will refactor as soon as TestResult and TestRun are migrated to enums
                ISNULL(SUM(IIF(Outcome IN (2), 1, 0)), 0)     AS ResultPassCount,
                ISNULL(SUM(IIF(Outcome IN (3), 1, 0)), 0)     AS ResultFailCount,
                ISNULL(SUM(IIF(Outcome = 1, 1, 0)), 0)          AS ResultNoneCount,
                ISNULL(SUM(IIF(Outcome = 4, 1, 0)), 0)  AS ResultInconclusiveCount,
                ISNULL(SUM(IIF(Outcome = 5, 1, 0)), 0)       AS ResultTimeoutCount,
                ISNULL(SUM(IIF(Outcome = 6, 1, 0)), 0)       AS ResultAbortedCount,
                ISNULL(SUM(IIF(Outcome = 7, 1, 0)), 0)       AS ResultBlockedCount,
                ISNULL(SUM(IIF(Outcome = 8, 1, 0)), 0)   AS ResultNotExecutedCount,
                ISNULL(SUM(IIF(Outcome = 9, 1, 0)), 0)       AS ResultWarningCount,
                ISNULL(SUM(IIF(Outcome = 10, 1, 0)), 0)         AS ResultErrorCount,
                ISNULL(SUM(IIF(Outcome = 11, 1, 0)), 0) AS ResultNotApplicableCount,
                ISNULL(SUM(IIF(Outcome = 14, 1, 0)), 0)   AS ResultNotImpactedCount,
                t.TestRunType,
                t.Workflow
    FROM    #ImpactedTests it
    INNER JOIN AnalyticsModel.tbl_TestResult t
    ON it.TestSK = t.TestSK
    WHERE   t.PartitionId = @partitionId
            -- No filter of trigger batches we need to look into all results for selected tests
            AND CompletedDateSK IS NOT NULL
            AND CompletedDateSK BETWEEN @minDateSK AND @maxDateSK -- Avoid to looking in older/newer dates => less updates + good segment elimination
            AND (BuildPipelineSK IS NOT NULL OR ReleasePipelineSK IS NOT NULL OR ReleaseStageSK IS NOT NULL) -- Avoid creating records for pre new dim results
    GROUP BY CompletedDateSK, t.TestSK, ProjectSK, BuildPipelineSK, ReleasePipelineSK, ReleaseStageSK, BranchSK, DataSourceId, Workflow, TestRunType
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- Get SKs to make MERGE cheaper
    UPDATE  s
    SET     TestResultDailySK = t.TestResultDailySK
    FROM    #TestResultDaily AS s
    INNER JOIN AnalyticsModel.tbl_TestResultDaily AS t
    ON      t.TestSK = s.TestSK
            AND t.DateSK = s.DateSK
            AND t.DataSourceId = s.DataSourceId
            AND t.ProjectSK = s.ProjectSK
            AND t.Workflow = s.Workflow
            AND EXISTS (
                SELECT
                    s.BuildPipelineSK,
                    s.ReleasePipelineSK,
                    s.ReleaseStageSK,
                    s.BranchSK
                INTERSECT
                SELECT
                    t.BuildPipelineSK,
                    t.ReleasePipelineSK,
                    t.ReleaseStageSK,
                    t.BranchSK
                )
    WHERE   t.PartitionId = @partitionId
    AND     t.DateSK BETWEEN @minDateSK AND @maxDateSK -- Avoid to looking in older/newer dates => less updates + good segment elimination
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    MERGE AnalyticsModel.tbl_TestResultDaily AS t
    USING #TestResultDaily AS s
    ON (t.PartitionId = @partitionId AND t.TestResultDailySK = s.TestResultDailySK)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.ProjectSK,
        s.BranchSK,
        s.ReleasePipelineSK,
        s.ReleaseStageSK,
        s.BuildPipelineSK,
        s.DateSK,
        s.ResultDurationSeconds,
        s.ResultCount,
        s.ResultNoneCount,
        s.ResultPassCount,
        s.ResultFailCount,
        s.ResultInconclusiveCount,
        s.ResultTimeoutCount,
        s.ResultAbortedCount,
        s.ResultBlockedCount,
        s.ResultNotExecutedCount,
        s.ResultWarningCount,
        s.ResultErrorCount,
        s.ResultNotApplicableCount,
        s.ResultNotImpactedCount,
        s.WorkFlow,
        s.TestRunType
        INTERSECT
        SELECT
        t.ProjectSK,
        t.BranchSK,
        t.ReleasePipelineSK,
        t.ReleaseStageSK,
        t.BuildPipelineSK,
        t.DateSK,
        t.ResultDurationSeconds,
        t.ResultCount,
        t.ResultNoneCount,
        t.ResultPassCount,
        t.ResultFailCount,
        t.ResultInconclusiveCount,
        t.ResultTimeoutCount,
        t.ResultAbortedCount,
        t.ResultBlockedCount,
        t.ResultNotExecutedCount,
        t.ResultWarningCount,
        t.ResultErrorCount,
        t.ResultNotApplicableCount,
        t.ResultNotImpactedCount,
        t.WorkFlow,
        t.TestRunType
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate            = @batchDt,
        AnalyticsBatchId                = @batchId,
        ProjectSK                       = s.ProjectSK,
        TestSK                          = s.TestSK,
        ReleasePipelineSK               = s.ReleasePipelineSK,
        ReleaseStageSK                  = s.ReleaseStageSK,
        BuildPipelineSK                 = s.BuildPipelineSK,
        BranchSK                        = s.BranchSK,
        DateSK                          = s.DateSK,
        ResultDurationSeconds           = s.ResultDurationSeconds,
        ResultCount                     = s.ResultCount,
        ResultNoneCount                 = s.ResultNoneCount,
        ResultPassCount                 = s.ResultPassCount,
        ResultFailCount                 = s.ResultFailCount,
        ResultInconclusiveCount         = s.ResultInconclusiveCount,
        ResultTimeoutCount              = s.ResultTimeoutCount,
        ResultAbortedCount              = s.ResultAbortedCount,
        ResultBlockedCount              = s.ResultBlockedCount,
        ResultNotExecutedCount          = s.ResultNotExecutedCount,
        ResultWarningCount              = s.ResultWarningCount,
        ResultErrorCount                = s.ResultErrorCount,
        ResultNotApplicableCount        = s.ResultNotApplicableCount,
        ResultNotImpactedCount          = s.ResultNotImpactedCount,
        Workflow                        = s.Workflow,
        TestRunType                     = s.TestrunType
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        DataSourceId,
        ProjectSK,
        TestSK,
        ReleasePipelineSK,
        ReleaseStageSK,
        BuildPipelineSK,
        BranchSK,
        DateSK,
        ResultDurationSeconds,
        ResultCount,
        ResultNoneCount,
        ResultPassCount,
        ResultFailCount,
        ResultInconclusiveCount,
        ResultTimeoutCount,
        ResultAbortedCount,
        ResultBlockedCount,
        ResultNotExecutedCount,
        ResultWarningCount,
        ResultErrorCount,
        ResultNotApplicableCount,
        ResultNotImpactedCount,
        Workflow,
        TestRunType
    )
    VALUES (
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.DataSourceId,
        s.ProjectSK,
        s.TestSK,
        s.ReleasePipelineSK,
        s.ReleaseStageSK,
        s.BuildPipelineSK,
        s.BranchSK,
        s.DateSK,
        s.ResultDurationSeconds,
        s.ResultCount,
        s.ResultNoneCount,
        s.ResultPassCount,
        s.ResultFailCount,
        s.ResultInconclusiveCount,
        s.ResultTimeoutCount,
        s.ResultAbortedCount,
        s.ResultBlockedCount,
        s.ResultNotExecutedCount,
        s.ResultWarningCount,
        s.ResultErrorCount,
        s.ResultNotApplicableCount,
        s.ResultNotImpactedCount,
        s.Workflow,
        s.TestRunType
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')

    DROP TABLE #TestResultDaily
    DROP TABLE #ImpactedTests

    RETURN 0
END

GO

