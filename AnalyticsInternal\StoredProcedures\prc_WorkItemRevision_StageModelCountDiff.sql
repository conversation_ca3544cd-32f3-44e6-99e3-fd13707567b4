/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B662C0068B06C43C43A738F6D2E433C8F82C9710
CREATE PROCEDURE AnalyticsInternal.prc_WorkItemRevision_StageModelCountDiff
    @partitionId INT,
    @compareStartDate DATETIME,
    @compareEndDate DATETIME,
    @expectedCount BIGINT OUTPUT,
    @actualCount BIGINT OUTPUT,
    @kpiValue FLOAT OUTPUT,
    @failed BIT OUTPUT
AS
BEGIN
    SELECT  @expectedCount = COUNT(*)
    FROM    [AnalyticsStage].[tbl_WorkItemRevision]
    WHERE   PartitionId = @partitionId
            AND System_AuthorizedDate < @compareEndDate
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SELECT  @actualCount = COUNT(*)
    FROM
    (
        SELECT  WorkItemRevisionSK
        FROM    [AnalyticsModel].[tbl_WorkItem]
        WHERE   PartitionId = @partitionId
                AND AuthorizedDate < @compareEndDate
        UNION ALL
        SELECT  WorkItemRevisionSK
        FROM    [AnalyticsModel].[tbl_WorkItemHistory]
        WHERE   PartitionId = @partitionId
                AND AuthorizedDate < @compareEndDate
    ) core
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SELECT @failed = CASE WHEN @expectedCount != @actualCount THEN 1 ELSE 0 END
    SELECT @kpiValue = @expectedCount - @actualCount

    RETURN 0
END

GO

