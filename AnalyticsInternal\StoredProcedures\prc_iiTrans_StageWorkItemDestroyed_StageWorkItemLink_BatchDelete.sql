/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F347FF50A7BAB31B43B5BA897C365F4DE18A8EE3
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemDestroyed_StageWorkItemLink_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    CREATE TABLE #DeletedWorkItem (WorkItemId INT)

    INSERT  #DeletedWorkItem
    SELECT  TOP (@batchSizeMax) WorkItemId
    FROM    AnalyticsStage.tbl_WorkItemDestroyed d
    WHERE   PartitionId = @partitionId
            AND d.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND d.WorkItemId > ISNULL(@stateData, -1)
    ORDER BY WorkItemId ASC
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)
    SET @endStateData = (SELECT MAX(WorkItemId) FROM #DeletedWorkItem)

    --Split into 2 tables instead of doing (l.SourceId = d.WorkItemId OR l.TargetId = d.WorkItemId)
    -- because SQL creates bad plan

    DECLARE @deleted TABLE
    (
        AnalyticsProviderShardId     INT             NOT NULL,
        SourceId              INT             NOT NULL,
        TargetId              INT             NOT NULL,
        LinkTypeId            INT             NOT NULL,
        ChangedDate           DATETIMEOFFSET  NOT NULL,
        IsActive              BIT             NULL
    )

    DELETE  l
    OUTPUT  DELETED.AnalyticsProviderShardId, DELETED.SourceId, DELETED.TargetId, DELETED.LinkTypeId, DELETED.ChangedDate, DELETED.IsActive INTO @deleted
    FROM    AnalyticsStage.tbl_WorkItemLink l
    JOIN    #DeletedWorkItem d
    ON      l.SourceId = d.WorkItemId
    WHERE   l.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DELETE  l
    OUTPUT  DELETED.AnalyticsProviderShardId, DELETED.SourceId, DELETED.TargetId, DELETED.LinkTypeId, DELETED.ChangedDate, DELETED.IsActive INTO @deleted
    FROM    AnalyticsStage.tbl_WorkItemLink l
    JOIN    #DeletedWorkItem d
    ON      l.TargetId = d.WorkItemId
    WHERE   l.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount += @@ROWCOUNT

    INSERT AnalyticsStage.tbl_WorkItemLink_Deleted
    (
        PartitionId,
        AnalyticsProviderShardId,
        AnalyticsBatchIdDeleted,
        AnalyticsDeletedDate,
        SourceId,
        TargetId,
        LinkTypeId,
        ChangedDate,
        IsActive
    )
    SELECT  @partitionId,
            AnalyticsProviderShardId,
            @batchId,
            @batchDt,
            SourceId,
            TargetId,
            LinkTypeId,
            ChangedDate,
            IsActive
    FROM    @deleted

    RETURN 0
END

GO

