/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 3CBECD238CD8FE40FA3CF194E8519EE0DBAC518B
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_ModelWorkItemDescendantHistory_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)
    DECLARE @triggerWorkItemId TABLE
    (
        System_Id INT NOT NULL PRIMARY KEY,
        [Count] INT NOT NULL
    )

    IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
                    AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = 0
        SET @endState = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 'dense', '')
    END
    ELSE -- use NCI
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 0, 1)
        SET @endState = IIF(@complete = 0 AND @endStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
    END

    DELETE  s
    FROM    @triggerWorkItemId s
    JOIN    AnalyticsModel.tbl_WorkItemDescendantHistory t
    ON      t.PartitionId = @partitionId
            AND t.WorkItemId = s.System_Id
            AND t.DescendantWorkItemId = s.System_Id

    INSERT  AnalyticsModel.tbl_WorkItemDescendantHistory
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        WorkItemId,
        DescendantWorkItemId,
        Depth,
        CreatedDate,
        DeletedDate,
        CreatedDateSK,
        DeletedDateSK,
        IsCurrent,
        IsLastRevisionOfPeriod
    )
    SELECT  @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            r.System_Id,
            r.System_Id,
            0,
            ISNULL(MIN(System_CreatedDate), '1900-01-01') AT TIME ZONE @timeZone,
            CAST('9999-01-01' AS DATETIMEOFFSET) AT TIME ZONE @timeZone,
            AnalyticsInternal.func_GenDateSK(ISNULL(MIN(System_CreatedDate), '1900-01-01') AT TIME ZONE @timeZone),
            NULL,
            1,
            2047
    FROM    @triggerWorkItemId t
    INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision r
    ON      r.PartitionId = @partitionId
            AND r.System_Id = t.System_Id
    GROUP BY r.System_Id

    SET @insertedCount = @@ROWCOUNT

    RETURN 0
END

GO

