/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 21864E91E84B339ED6F7E27B4946D91FF5BED371
CREATE PROCEDURE AnalyticsInternal.prc_Project_StageModelCountDiff
    @partitionId INT,
    @compareStartDate DATETIME,
    @compareEndDate DATETIME,
    @expectedCount BIGINT OUTPUT,
    @actualCount BIGINT OUTPUT,
    @kpiValue FLOAT OUTPUT,
    @failed BIT OUTPUT
AS
BEGIN

    SELECT  @expectedCount = COUNT(*)
    FROM    AnalyticsStage.tbl_Project
    WHERE   PartitionId = @partitionId
            AND AnalyticsCreatedDate <= @compareEndDate
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- exclude any running batches to ensure NOLOCK queries don't double count rows added to history but not yet removed from current
    DECLARE @maxBatchId BIGINT
    SELECT  TOP 1 @maxBatchId = BatchId
    FROM    AnalyticsInternal.tbl_Batch
    WHERE   PartitionId = @partitionId
            AND TableName = 'Model.Project'
            AND Ready = 1
    ORDER BY BatchId DESC
    OPTION(OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @maxBatchId = ISNULL(@maxBatchId, 9223372036854775807)

    SELECT  @actualCount = COUNT(*)
    FROM    AnalyticsModel.tbl_Project AS m
    JOIN    AnalyticsStage.tbl_Project AS s
    ON      m.PartitionId = s.PartitionId
            AND m.ProjectSK = s.ProjectGuid
    WHERE   m.PartitionId = @partitionId
            AND s.AnalyticsCreatedDate <= @compareEndDate
            AND m.AnalyticsBatchId <= @maxBatchId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SELECT @failed = CASE WHEN @expectedCount != @actualCount THEN 1 ELSE 0 END
    SELECT @kpiValue = @expectedCount - @actualCount

    RETURN 0

END

GO

