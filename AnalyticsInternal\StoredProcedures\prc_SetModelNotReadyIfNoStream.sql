/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: CD3D4B3192BC67F832C5C0C5B9B79B69B4AE543E
--This is simplified version of AnalyticsInternal.prc_DQ_ModelReady.
--Mark <PERSON> if the account has never been staged instead of checking if model has all staged data.
CREATE PROCEDURE AnalyticsInternal.prc_SetModelNotReadyIfNoStream
    @partitionId INT
AS
BEGIN

    IF NOT EXISTS
    (
        SELECT      TOP 1 *
        FROM        AnalyticsInternal.tbl_TableProviderShardStream
        WHERE       PartitionId = @partitionId
    )
    BEGIN
        exec prc_SetRegistryValue @partitionId, '#\Service\Analytics\State\ModelNotReady\', 'True'
    END
END

GO

