/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 5AEE295AD6E3168B73F3C286B95A8A2CCC7E5A2D
--------------------------------------------------------------------
-- Returns any pending transform work as batches
-- Creates new transform batches as need to ensure highest priority work is covered
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_GetNextTransformBatch
    @partitionId    INT,
    @tableName      VARCHAR(64),
    @settings       typ_KeyValuePairStringTable READONLY
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @newBatchId BIGINT

    DECLARE @transformBatches TABLE
    (
        BatchId BIGINT NOT NULL,
        Ready BIT NOT NULL,
        Failed BIT NOT NULL,
        Active BIT NOT NULL,
        AttemptCount INT NOT NULL,
        TriggerTableName VARCHAR(64) NOT NULL,
        TriggerBatchIdStart BIGINT NOT NULL,
        TriggerBatchIdEnd BIGINT NOT NULL,
        TableName VARCHAR(64) NOT NULL,
        Sproc VARCHAR(100) NOT NULL,
        SprocVersion INT NOT NULL,
        TransformPriority INT NOT NULL,
        [State] VARCHAR(10),
        StateData BIGINT,
        CreateDateTime DATETIME NOT NULL,
        Held BIT NOT NULL
    )

    INSERT  @transformBatches
    SELECT  b.BatchId,
            b.Ready,
            b.Failed,
            b.OperationActive AS Active,
            ISNULL(b.AttemptCount, 0) AS AttemptCount,
            b.OperationTriggerTableName AS TriggerTableName,
            b.OperationTriggerBatchIdStart AS TriggerBatchIdStart,
            b.OperationTriggerBatchIdEnd AS TriggerBatchIdEnd,
            b.TableName,
            b.OperationSproc AS Sproc,
            b.OperationSprocVersion AS SprocVersion,
            ISNULL(b.OperationPriority, 5) AS TransformPriority,
            b.OperationState AS [State],
            b.OperationStateData AS StateData,
            b.CreateDateTime,
            ISNULL(ts.Hold, 0) AS Held
    FROM    AnalyticsInternal.tbl_Batch b WITH (READPAST, INDEX (IX_AnalyticsInternal_tbl_Batch_NotReadyNotFailedProcess))
    INNER LOOP JOIN AnalyticsInternal.tbl_TransformState ts
    ON      b.PartitionId = ts.PartitionId
            AND b.OperationTriggerTableName = ts.TriggerTableName
            AND b.TableName = ts.TargetTableName
            AND b.Operation = ts.TargetOperation
            AND b.OperationSproc = ts.SProcName
    WHERE   b.PartitionId = @partitionId
            AND Ready = 0
            AND Failed = 0
            AND OperationTriggerTableName IS NOT NULL
            AND (@tableName IS NULL OR TableName = @tableName)
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN), MAXDOP 1); -- stats on this filtered index are not trustworthy

    DECLARE @minNewBatchPriority INT

    SELECT  @minNewBatchPriority = ISNULL(MAX(TransformPriority) + 1, 0)
    FROM    @transformBatches
    WHERE   Held = 0 -- Don't consider held batchs when determining min new batch priority

    EXEC AnalyticsInternal.prc_iCreateProcessingBatch @partitionId, @tablename, @minPriority = @minNewBatchPriority, @settings = @settings, @newBatchId = @newBatchId OUTPUT

    INSERT  @transformBatches
    SELECT  b.BatchId,
            b.Ready,
            b.Failed,
            b.OperationActive AS Active,
            ISNULL(b.AttemptCount, 0) AS AttemptCount,
            b.OperationTriggerTableName AS TriggerTableName,
            b.OperationTriggerBatchIdStart AS TriggerBatchIdStart,
            b.OperationTriggerBatchIdEnd AS TriggerBatchIdEnd,
            b.TableName,
            b.OperationSproc AS Sproc,
            b.OperationSprocVersion AS SprocVersion,
            ISNULL(ts.TransformPriority, 5) AS TransformPriority,
            b.OperationState AS [State],
            b.OperationStateData AS StateData,
            b.CreateDateTime,
            ISNULL(ts.Hold, 0) AS Held
    FROM    AnalyticsInternal.tbl_Batch b WITH (READPAST)
    JOIN    AnalyticsInternal.tbl_TransformState ts
    ON      b.PartitionId = ts.PartitionId
            AND b.OperationTriggerTableName = ts.TriggerTableName
            AND b.TableName = ts.TargetTableName
            AND b.Operation = ts.TargetOperation
            AND b.OperationSproc = ts.SProcName
    WHERE   b.PartitionId = @partitionId
            AND BatchId = @newBatchId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN));

    SELECT  TOP 1 BatchId,
            Ready,
            Failed,
            Active,
            AttemptCount,
            TriggerTableName,
            TriggerBatchIdStart,
            TriggerBatchIdEnd,
            TableName,
            Sproc,
            SprocVersion,
            TransformPriority,
            [State],
            StateData,
            CreateDateTime,
            Held
    FROM    @transformBatches
    ORDER BY Held ASC,
            TransformPriority DESC,
            TriggerBatchIdStart ASC,
            CreateDateTime ASC
END

GO

