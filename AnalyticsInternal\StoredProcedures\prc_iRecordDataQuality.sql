/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 8BDF03F8FB029A9EDC613B1B163AF6F2CFC1DFA7
CREATE PROCEDURE AnalyticsInternal.prc_iRecordDataQuality(
    @partitionId INT,
    @name VARCHAR(256),
    @result AnalyticsInternal.typ_DataQualityResult3 READONLY
)
AS
BEGIN
    DECLARE @runEndDate DATETIME = GETUTCDATE()

    BEGIN TRANSACTION

    -- Unset existing latest results
    UPDATE  u
    SET     Latest = 0
    FROM    AnalyticsInternal.tbl_DataQualityResult u
    JOIN    @result r
    ON      u.Name = @name
            AND u.TargetTable = r.TargetTable
            AND EXISTS (
                SELECT u.scope
                    INTERSECT
                SELECT r.scope)
    WHERE   u.PartitionId = @partitionId
            AND u.Latest = 1

    -- Could contain multiple records, need to determine latest within new batch of results
    ; WITH cte
    AS (
        SELECT  RunDate,
                StartDate,
                EndDate,
                Scope,
                TargetTable,
                ExpectedValue,
                ActualValue,
                Failed,
                KpiValue,
                ROW_NUMBER() OVER (PARTITION BY TargetTable, Scope ORDER BY RunDate DESC, EndDate DESC) AS rn
        FROM    @result
    )
    INSERT INTO AnalyticsInternal.tbl_DataQualityResult(PartitionId, RunDate, RunEndDate, StartDate, EndDate, Name, Scope, TargetTable, ExpectedValue, ActualValue, Failed, KpiValue, Latest)
    SELECT  @partitionId,
            RunDate,
            @runEndDate,
            StartDate,
            EndDate,
            @name,
            Scope,
            TargetTable,
            ExpectedValue,
            ActualValue,
            Failed,
            KpiValue,
            iif(rn = 1, 1, 0)
    FROM    cte
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    COMMIT

    SELECT  @partitionId AS PartitionId,
            @name AS Name,
            ISNULL(Scope, TargetTable) AS Scope,
            TargetTable,
            RunDate,
            @runEndDate AS RunEndDate,
            StartDate,
            EndDate,
            ExpectedValue,
            ActualValue,
            KpiValue,
            Failed
    FROM    @result

    RETURN 0

END

GO

