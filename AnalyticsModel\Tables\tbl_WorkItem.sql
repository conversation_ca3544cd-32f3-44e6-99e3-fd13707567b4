CREATE TABLE [AnalyticsModel].[tbl_WorkItem] (
    [PartitionId]                 INT                NOT NULL,
    [AnalyticsCreatedDate]        DATETIME           NOT NULL,
    [AnalyticsUpdatedDate]        DATETIME           NOT NULL,
    [AnalyticsBatchId]            BIGINT             NOT NULL,
    [WorkItemRevisionSK]          INT                NOT NULL,
    [WorkItemId]                  INT                NOT NULL,
    [Revision]                    INT                NOT NULL,
    [Watermark]                   INT                NULL,
    [Title]                       NVARCHAR (256)     NULL,
    [WorkItemType]                NVARCHAR (256)     NULL,
    [ChangedDate]                 DATETIMEOFFSET (7) NOT NULL,
    [CreatedDate]                 DATETIMEOFFSET (7) NULL,
    [State]                       NVARCHAR (256)     NULL,
    [Reason]                      NVARCHAR (256)     NULL,
    [FoundIn]                     NVARCHAR (256)     NULL,
    [IntegrationBuild]            NVARCHAR (256)     NULL,
    [ActivatedDate]               DATETIMEOFFSET (7) NULL,
    [Activity]                    NVARCHAR (256)     NULL,
    [BacklogPriority]             FLOAT (53)         NULL,
    [BusinessValue]               INT                NULL,
    [ClosedDate]                  DATETIMEOFFSET (7) NULL,
    [Discipline]                  NVARCHAR (256)     NULL,
    [Issue]                       NVARCHAR (256)     NULL,
    [Priority]                    INT                NULL,
    [Rating]                      NVARCHAR (256)     NULL,
    [ResolvedDate]                DATETIMEOFFSET (7) NULL,
    [ResolvedReason]              NVARCHAR (256)     NULL,
    [Risk]                        NVARCHAR (256)     NULL,
    [Severity]                    NVARCHAR (256)     NULL,
    [StackRank]                   FLOAT (53)         NULL,
    [TimeCriticality]             FLOAT (53)         NULL,
    [Triage]                      NVARCHAR (256)     NULL,
    [ValueArea]                   NVARCHAR (256)     NULL,
    [DueDate]                     DATETIMEOFFSET (7) NULL,
    [FinishDate]                  DATETIMEOFFSET (7) NULL,
    [StartDate]                   DATETIMEOFFSET (7) NULL,
    [TargetDate]                  DATETIMEOFFSET (7) NULL,
    [Blocked]                     NVARCHAR (256)     NULL,
    [Committed]                   NVARCHAR (256)     NULL,
    [Escalate]                    NVARCHAR (256)     NULL,
    [FoundInEnvironment]          NVARCHAR (256)     NULL,
    [HowFound]                    NVARCHAR (256)     NULL,
    [Probability]                 INT                NULL,
    [RequirementType]             NVARCHAR (256)     NULL,
    [RequiresReview]              NVARCHAR (256)     NULL,
    [RequiresTest]                NVARCHAR (256)     NULL,
    [RootCause]                   NVARCHAR (256)     NULL,
    [SubjectMatterExpert1]        NVARCHAR (256)     NULL,
    [SubjectMatterExpert2]        NVARCHAR (256)     NULL,
    [SubjectMatterExpert3]        NVARCHAR (256)     NULL,
    [TargetResolveDate]           DATETIMEOFFSET (7) NULL,
    [TaskType]                    NVARCHAR (256)     NULL,
    [UserAcceptanceTest]          NVARCHAR (256)     NULL,
    [ProjectSK]                   UNIQUEIDENTIFIER   NULL,
    [IsDeleted]                   BIT                NOT NULL,
    [AutomatedTestId]             NVARCHAR (256)     NULL,
    [AutomatedTestName]           NVARCHAR (256)     NULL,
    [AutomatedTestStorage]        NVARCHAR (256)     NULL,
    [AutomatedTestType]           NVARCHAR (256)     NULL,
    [AutomationStatus]            NVARCHAR (256)     NULL,
    [DateSK]                      INT                NULL,
    [AreaSK]                      UNIQUEIDENTIFIER   NULL,
    [IterationSK]                 UNIQUEIDENTIFIER   NULL,
    [CompletedWork]               FLOAT (53)         NULL,
    [Effort]                      FLOAT (53)         NULL,
    [OriginalEstimate]            FLOAT (53)         NULL,
    [RemainingWork]               FLOAT (53)         NULL,
    [Size]                        FLOAT (53)         NULL,
    [StoryPoints]                 FLOAT (53)         NULL,
    [CreatedDateSK]               INT                NULL,
    [ActivatedDateSK]             INT                NULL,
    [ClosedDateSK]                INT                NULL,
    [ResolvedDateSK]              INT                NULL,
    [AssignedToUserSK]            UNIQUEIDENTIFIER   NULL,
    [ChangedByUserSK]             UNIQUEIDENTIFIER   NULL,
    [CreatedByUserSK]             UNIQUEIDENTIFIER   NULL,
    [ActivatedByUserSK]           UNIQUEIDENTIFIER   NULL,
    [ClosedByUserSK]              UNIQUEIDENTIFIER   NULL,
    [ResolvedByUserSK]            UNIQUEIDENTIFIER   NULL,
    [ParentWorkItemId]            INT                NULL,
    [TagNames]                    NVARCHAR (1024)    NULL,
    [StateCategory]               NVARCHAR (256)     NULL,
    [InProgressDate]              DATETIMEOFFSET (7) NULL,
    [InProgressDateSK]            INT                NULL,
    [CompletedDate]               DATETIMEOFFSET (7) NULL,
    [CompletedDateSK]             INT                NULL,
    [LeadTimeDays]                FLOAT (53)         NULL,
    [CycleTimeDays]               FLOAT (53)         NULL,
    [InternalForSnapshotHashJoin] BIT                NOT NULL,
    [AuthorizedDate]              DATETIMEOFFSET (7) NULL,
    [StateChangeDate]             DATETIMEOFFSET (7) NULL,
    [StateChangeDateSK]           INT                NULL,
    [TeamFieldSK]                 INT                NULL,
    [CommentCount]                INT                NULL
) ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_WorkItem_WorkItemRevisionSK]
    ON [AnalyticsModel].[tbl_WorkItem]([PartitionId] ASC, [WorkItemRevisionSK] ASC)
    INCLUDE([WorkItemId])
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_WorkItem_WorkItemIdRev]
    ON [AnalyticsModel].[tbl_WorkItem]([PartitionId] ASC, [WorkItemId] ASC, [Revision] ASC)
    INCLUDE([WorkItemRevisionSK], [IsDeleted], [ChangedDate], [InProgressDate], [CompletedDate], [ProjectSK], [Watermark], [TeamFieldSK])
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_WorkItem_WorkItemId]
    ON [AnalyticsModel].[tbl_WorkItem]([PartitionId] ASC, [WorkItemId] ASC)
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_WorkItem]
    ON [AnalyticsModel].[tbl_WorkItem]
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

