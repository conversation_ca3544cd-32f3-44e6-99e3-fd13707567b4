/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 4E63FD17C6F922773570AAFC1699FFE2ED2B8778
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemRevision_ModelWorkItemDescendantHistory_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #DeletedRev (System_Id INT, System_Rev INT)

    IF (@subBatchCount = 1)
    BEGIN
        INSERT  #DeletedRev (System_Id, System_Rev)
        SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev
        FROM    AnalyticsStage.tbl_WorkItemRevision_Deleted d WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdDeleted))
        WHERE   d.PartitionId = @partitionId
                AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND d.System_Id > ISNULL(@stateData, -1)
        ORDER BY System_Id
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        INSERT  #DeletedRev (System_Id, System_Rev)
        SELECT  TOP (@batchSizeMax) WITH TIES System_Id, System_Rev
        FROM    AnalyticsStage.tbl_WorkItemRevision_Deleted d WITH (INDEX (CI_tbl_WorkItemRevision_Deleted))
        WHERE   d.PartitionId = @partitionId
                AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND d.System_Id > ISNULL(@stateData, -1)
        ORDER BY System_Id
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(System_Id) FROM #DeletedRev)

    DELETE  d
    FROM    #DeletedRev d
    JOIN    AnalyticsStage.tbl_WorkItemRevision s
    ON      s.PartitionId = @partitionId
            AND s.System_Id = d.System_Id
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  t
    FROM    AnalyticsModel.tbl_WorkItemDescendantHistory t
    JOIN    #DeletedRev d
    ON      d.System_Id = t.WorkItemId
            AND d.System_Id = t.DescendantWorkItemId
            AND t.Depth = 0
    WHERE   t.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #DeletedRev

    RETURN 0
END

GO

