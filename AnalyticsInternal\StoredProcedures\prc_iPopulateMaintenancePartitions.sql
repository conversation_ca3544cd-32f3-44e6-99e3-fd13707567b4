/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FF62A54BCA61D7FF17A4F9B97EB4BCDFEA6F31DD
CREATE PROCEDURE AnalyticsInternal.prc_iPopulateMaintenancePartitions
   @maintenanceId INT,
   @tableName NVARCHAR(255) = NULL, -- Table to sort
   @startPartitionId INT,
   @endPartitionId INT,
   @splitPartitionId INT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON
    SET DEADLOCK_PRIORITY HIGH -- Maintenance is expensive in case of deadlock kill transform that will be retried

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @newLine CHAR(2) = CHAR(10)+CHAR(13) -- CR+LF
    DECLARE @indexName NVARCHAR(256)
    DECLARE @mainTableName NVARCHAR(256)
    DECLARE @transformTableName NVARCHAR(256)
    DECLARE @applyMaxBatchFilter NVARCHAR(256)
    DECLARE @keyColumn NVARCHAR(256)
    DECLARE @matchPredicate NVARCHAR(256)
    DECLARE @hasIdentity BIT
    DECLARE @optimizeBatchCalculation BIT
    DECLARE @shemeName NVARCHAR(256)

    SELECT  @mainTableName = TableName,
            @applyMaxBatchFilter = ApplyMaxBatchFilter,
            @keyColumn = KeyColumn,
            @hasIdentity = HasIdentity,
            @matchPredicate = MatchPredicate,
            @indexName= IndexName,
            @transformTableName  = TransformTableName,
            @optimizeBatchCalculation = OptimizeBatchCalculation,
            @shemeName = PartitionScheme
    FROM    AnalyticsInternal.func_iGetTableMaintenanceDefinitions()
    WHERE   TableName = @tableName

    DECLARE @currentPhase VARCHAR(20) = 'Online Clone'

    DECLARE @populateCommand NVARCHAR(MAX) = N'
        IF(NOT EXISTS(SELECT * FROM AnalyticsInternal.tbl_TableMaintenancePartition WHERE TableMaintenanceId = @maintenanceId))
        BEGIN
            DECLARE @Partition TABLE (StartPartitionId INT, EndPartitionId INT)
            -- We are coping data by physical partitions
            IF @startPartitionId IS NOT NULL
            BEGIN
                IF @splitPartitionId IS NOT NULL
                BEGIN
                    -- SPLIT or MERGE
                   INSERT INTO @Partition
                   VALUES(@startPartitionId, @splitPartitionId)

                   INSERT INTO @Partition
                   VALUES(@splitPartitionId + 1, @endPartitionId)
                END
                ELSE
                BEGIN
                   -- SORT
                   INSERT INTO @Partition
                   VALUES(@startPartitionId, @endPartitionId)
                END
            END
            ELSE
            BEGIN
                INSERT INTO @Partition
                SELECT
                ISNULL((LAG(PartitionId) OVER (ORDER BY PartitionId)) + 1,0) AS StartPartitionId,
                PartitionId AS EndPartitionId
                FROM
                (
                    SELECT CAST(value AS INT ) AS PartitionId
                    FROM sys.partition_schemes s
                    JOIN sys.partition_range_values r
                    ON s.function_id = r.function_id
                    WHERE s.name = @shemeName
                ) T

                INSERT INTO @Partition
                SELECT MAX(EndPartitionId) + 1, 2147483647 FROM @Partition
            END

            SELECT ''Populate'', * FROM @Partition

            -- We are doing 2 phase copying process to keep data latency low
            -- Save max batch id for each partition

            INSERT INTO AnalyticsInternal.tbl_TableMaintenancePartition
            SELECT @maintenanceId,
                   PartitionId,
                   @currentPhase,
                   StartPartitionId,
                   EndPartitionId,
                   MAX(AnalyticsBatchId),
                   NULL
            FROM   ' + @tableName +' t
            JOIN   @Partition p
            ON     t.PartitionId BETWEEN StartPartitionId AND EndPartitionId
            WHERE  PartitionId > 0
            GROUP BY PartitionId,
                     StartPartitionId,
                     EndPartitionId

            -- The oldest active transformation
            UPDATE t
            SET LastBatchId = IIF(LastBatchId > b.BatchId, b.BatchId, LastBatchId)
            FROM AnalyticsInternal.tbl_TableMaintenancePartition t
            JOIN AnalyticsInternal.tbl_Batch b
            ON   t.ProcessPartitionId = b.PartitionId
                 AND Ready = 0
                 AND Failed = 0
                 AND TableName = @transformTableName
                 AND TableMaintenanceId = @maintenanceId
                 AND Operation <> ''delete'' -- deleted records will be removed anyway
        END
    '

    --SELECT @populateCommand as Command FOR XML PATH

    EXEC sp_executesql @populateCommand
    ,N'@startPartitionId INT, @endPartitionId INT, @splitPartitionId INT, @maintenanceId INT, @transformTableName NVARCHAR(256), @shemeName NVARCHAR(256), @currentPhase VARCHAR(20)'
    , @startPartitionId = @startPartitionId
    , @endPartitionId = @endPartitionId
    , @splitPartitionId = @splitPartitionId
    , @maintenanceId = @maintenanceId
    , @transformTableName = @transformTableName
    , @shemeName = @shemeName
    , @currentPhase = @currentPhase

    RETURN 0
END

GO

