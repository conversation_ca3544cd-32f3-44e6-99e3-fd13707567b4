/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 685DF1A617BFFA325721E2BE802EAAD0DF913F7E
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTeamSetting_ModelTeamIteration_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    ; WITH TeamsToProcess AS
    (
        SELECT
            c.PartitionId,
            c.TeamGuid AS TeamSK,
            c.Iterations
        FROM AnalyticsStage.tbl_TeamSetting c
        WHERE c.PartitionId = @partitionId
            AND c.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    ),
    Src AS
    (
        SELECT
            t.PartitionId,
            t.TeamSK,
            di.IterationSK
        FROM TeamsToProcess t
        CROSS APPLY Iterations.nodes('//Guid/text()') i(IterationId)
        JOIN AnalyticsModel.tbl_Iteration di ON di.PartitionId = t.PartitionId and di.IterationSK = i.IterationId.value('.[1]','uniqueidentifier')
    ),
    Tgt AS
    (
        -- MERGE has a bug where it tries accessing entities in all partitions (even offline ones) for the target table.
        -- To overcome this problem the target table has to be pre-filtered by correct partition id.
        SELECT * FROM AnalyticsModel.tbl_TeamIteration WHERE PartitionId = @partitionId
    )
    MERGE TOP (@batchSizeMax) Tgt AS t
    USING Src AS s
    ON (t.PartitionId = s.PartitionId AND t.TeamSK = s.TeamSK AND t.IterationSK = s.IterationSK)
    WHEN NOT MATCHED BY TARGET
    THEN INSERT
        (
            PartitionId,
            AnalyticsBatchId,
            AnalyticsCreatedDate,
            AnalyticsUpdatedDate,
            TeamSK,
            IterationSK
        )
    VALUES
        (
        s.PartitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.TeamSK,
        s.IterationSK
        )
    WHEN NOT MATCHED BY SOURCE
    AND t.TeamSK IN (SELECT TeamSK FROM TeamsToProcess)
    THEN DELETE
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    RETURN 0
END

GO

