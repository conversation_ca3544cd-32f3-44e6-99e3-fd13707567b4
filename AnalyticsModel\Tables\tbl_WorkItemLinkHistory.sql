CREATE TABLE [AnalyticsModel].[tbl_WorkItemLinkHistory] (
    [PartitionId]           INT                NOT NULL,
    [AnalyticsCreatedDate]  DATETIME           NOT NULL,
    [AnalyticsUpdatedDate]  DATETIME           NOT NULL,
    [AnalyticsBatchId]      BIGINT             NOT NULL,
    [WorkItem<PERSON>inkHistorySK] INT                IDENTITY (1, 1) NOT NULL,
    [SourceWorkItemId]      INT                NOT NULL,
    [TargetWorkItemId]      INT                NOT NULL,
    [CreatedDate]           DATETIMEOFFSET (7) NOT NULL,
    [DeletedDate]           DATETIMEOFFSET (7) NOT NULL,
    [Comment]               NVARCHAR (256)     NULL,
    [LinkTypeId]            INT                NOT NULL,
    [LinkTypeReferenceName] NVARCHAR (136)     NULL,
    [LinkTypeName]          NVARCHAR (128)     NULL,
    [LinkTypeIsAcyclic]     BIT                NULL,
    [LinkTypeIsDirectional] BIT                NULL,
    [ProjectSK]             UNIQUEIDENTIFIER   NULL,
    [IsCurrent]             BIT                NOT NULL
);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_WorkItemLinkHistory_TargetWorkItemIdDeletedDate]
    ON [AnalyticsModel].[tbl_WorkItemLinkHistory]([PartitionId] ASC, [TargetWorkItemId] ASC, [DeletedDate] ASC) WHERE ([LinkTypeReferenceName]='System.LinkTypes.Hierarchy-Forward') WITH (DATA_COMPRESSION = PAGE);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_WorkItemLinkHistory_AxBatchId]
    ON [AnalyticsModel].[tbl_WorkItemLinkHistory]([PartitionId] ASC, [AnalyticsBatchId] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE CLUSTERED INDEX [CI_AnalyticsModel_tbl_WorkItemLinkHistory]
    ON [AnalyticsModel].[tbl_WorkItemLinkHistory]([PartitionId] ASC, [SourceWorkItemId] ASC, [TargetWorkItemId] ASC, [LinkTypeId] ASC, [CreatedDate] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_WorkItemLinkHistory_Current]
    ON [AnalyticsModel].[tbl_WorkItemLinkHistory]([PartitionId] ASC, [SourceWorkItemId] ASC, [TargetWorkItemId] ASC, [LinkTypeId] ASC) WHERE ([IsCurrent]=(1)) WITH (DATA_COMPRESSION = PAGE);


GO

