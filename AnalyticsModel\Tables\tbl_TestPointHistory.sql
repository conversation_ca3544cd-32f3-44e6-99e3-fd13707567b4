CREATE TABLE [AnalyticsModel].[tbl_TestPointHistory] (
    [PartitionId]                 INT                NOT NULL,
    [AnalyticsCreatedDate]        DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate]        DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]            BIGINT             NOT NULL,
    [TestPointHistorySK]          BIGINT             IDENTITY (1, 1) NOT NULL,
    [ProjectSK]                   UNIQUEIDENTIFIER   NULL,
    [TestSuiteSK]                 INT                NULL,
    [TestPointSK]                 INT                NULL,
    [TestPlanId]                  INT                NULL,
    [TestSuiteId]                 INT                NULL,
    [TestPointId]                 INT                NOT NULL,
    [TestConfigurationSK]         INT                NULL,
    [TestConfigurationId]         INT                NULL,
    [TestCaseId]                  INT                NULL,
    [State]                       TINYINT            NULL,
    [Revision]                    INT                NULL,
    [TesterUserSK]                UNIQUEIDENTIFIER   NULL,
    [AssignedToUserSK]            UNIQUEIDENTIFIER   NULL,
    [Priority]                    INT                NULL,
    [AutomationStatus]            NVARCHAR (256)     NULL,
    [ResultOutcome]               TINYINT            NULL,
    [ResultFromDate]              DATETIMEOFFSET (0) NULL,
    [ResultFromDateSK]            INT                NULL,
    [ResultToDate]                DATETIMEOFFSET (0) NULL,
    [ResultToDateSK]              INT                NULL,
    [InternalForSnapshotHashJoin] BIT                NOT NULL,
    [IsLastRevisionOfDay]         BIT                NOT NULL,
    [IsDeleted]                   BIT                NOT NULL,
    [DataSourceId]                INT                DEFAULT ((0)) NOT NULL
) ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_TestPointHistory_TestPointId_Revision]
    ON [AnalyticsModel].[tbl_TestPointHistory]([PartitionId] ASC, [TestPointId] ASC, [Revision] ASC, [DataSourceId] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_TestPointHistory]
    ON [AnalyticsModel].[tbl_TestPointHistory]
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

