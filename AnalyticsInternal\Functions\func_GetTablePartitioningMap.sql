/******************************************************************************************************
** Warning: the contents of this function are critical to its functioning.
** Modifying the contents of this function could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 6B4578C19480A344392CA65449E265D2E504D44F
------------------------------------------
-- Returns physical parititons metadata --
------------------------------------------
CREATE FUNCTION AnalyticsInternal.func_GetTablePartitioningMap()
RETURNS TABLE
AS
RETURN
    SELECT *
    FROM
    (
        VALUES
            ('func_AnalyticsWorkItemPartition', 'scheme_AnalyticsWorkItem'),
            ('func_AnalyticsTestPartition', 'scheme_AnalyticsTest'),
            ('func_AnalyticsBuildpartition', 'scheme_AnalyticsBuild')
    )
    Map(PartitionFunction, PartitionScheme)

GO

