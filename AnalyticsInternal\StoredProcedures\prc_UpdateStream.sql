/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F1B2860BC3ECEFBFB0D8B2FBEFC56684A1BB14C3
CREATE PROCEDURE AnalyticsInternal.prc_UpdateStream
    @partitionId    INT,
    @tableName VARCHAR(64),
    @providerShardId INT,
    @streamId INT,
    @enabled BIT,
    @current BIT,
    @priority INT,
    @disposed BIT,
    @keysOnly BIT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @now DATETIME = GETUTCDATE()

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @lowerCurrentStreamId BIT = 0
    IF (@disposed = 1)
    BEGIN
        -- safety check - don't dispose streams when current stream id is less
        -- doing so would potentially delete stage table data
        IF EXISTS (
            SELECT *
            FROM AnalyticsInternal.tbl_TableProviderShardStream WITH (ROWLOCK)
            WHERE PartitionId = @partitionId
                AND TableName = @tableName
                AND AnalyticsProviderShardId = @providerShardId
                AND AnalyticsStreamId < @streamId
                AND [Current] = 1
                )
        BEGIN
            SET @lowerCurrentStreamId = 1
        END
    END

    UPDATE AnalyticsInternal.tbl_TableProviderShardStream WITH (ROWLOCK)
    SET [Priority]    = ISNULL(@priority, [Priority]),
        [Enabled]     = @enabled,
        [Current]     = @current,
        LoadedTime    = ISNULL(LoadedTime, IIF(@current = 1, @now, NULL)),
        UpdateTime    = @now,
        Disposed      = IIF(@disposed = 1 AND @enabled = 0 AND @current = 0 AND @lowerCurrentStreamId = 0, 1, 0),
        KeysOnly      = ISNULL(@keysOnly, KeysOnly)
    WHERE PartitionId = @partitionId
        AND TableName = @tableName
        AND AnalyticsProviderShardId = @providerShardId
        AND AnalyticsStreamId = @streamId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
END

GO

