/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D29CE07E67E90D3847A42559AE69E860B03D7126
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_Weekly_InternalWorkitemCompletedTimePredictModel_TableInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @areMLServicesAvailable BIT
    EXEC AnalyticsInternal.prc_iAreMLServicesAvailable @areMLServicesAvailable OUTPUT

    IF (@areMLServicesAvailable = 1)
    BEGIN
        CREATE TABLE #WorkItemRevisionFeatures
        (
            TimeToFinishInDays          FLOAT               NULL,
            WorkItemId                  INT                 NOT NULL,
            Revision                    INT                 NOT NULL,
            State                       NVARCHAR(256)       NULL,
            WorkItemType                NVARCHAR(256)       NULL
        )

        DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)
        DECLARE @completedDateSK INT = AnalyticsInternal.func_GenDateSK(DATEADD(YEAR, -2, GETUTCDATE() AT TIME ZONE @timeZone))

        -- For training the model only get the workitemrevisions which have been closed after 2 years
        ;WITH FirstClosed AS
        (
            SELECT  *,
                    ROW_NUMBER() OVER(PARTITION BY WorkItemId ORDER BY Revision ASC) AS RowNum
            FROM
            (
                SELECT  WorkItemId,
                        CompletedDate,
                        Revision
                FROM    [AnalyticsModel].[tbl_WorkItem]
                WHERE   PartitionId = @partitionId
                        AND CompletedDate IS NOT NULL
                        AND CompletedDateSK > @completedDateSK
                UNION ALL
                SELECT  WorkItemId,
                        CompletedDate,
                        Revision
                FROM    [AnalyticsModel].[tbl_WorkItemHistory]
                WHERE   PartitionId = @partitionId
                        AND CompletedDate IS NOT NULL
                        AND CompletedDateSK > @completedDateSK
            ) AllClosed
        )

        INSERT INTO #WorkItemRevisionFeatures
        SELECT      TOP(2000000) DATEDIFF_BIG(MINUTE, a.ChangedDate, b.CompletedDate) / 1440.0 AS TimeToFinishInDays,
                    a.WorkItemId,
                    a.Revision,
                    State,
                    WorkItemType
        FROM        [AnalyticsModel].[tbl_WorkItemHistory] a
        INNER JOIN  FirstClosed b
        ON          a.WorkItemId = b.WorkItemId
                    AND b.RowNum = 1
                    AND a.PartitionId = @partitionId
        WHERE       a.Revision < b.Revision
        ORDER BY    a.CompletedDateSK DESC
        OPTION      (OPTIMIZE FOR (@partitionId UNKNOWN))

        DECLARE @currentRevisionCount INT = @@ROWCOUNT
        DECLARE @minRevisionsForModelCreation INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'MinRevisionsForModelCreation'), 1000)
        -- TODO: Verify that CV has been done here

        SET @insertedCount = 0
        IF (@currentRevisionCount > @minRevisionsForModelCreation) -- TODO: check whether the CV metrics are above threshold
        BEGIN
            DECLARE @model VARBINARY(MAX)
            EXECUTE sp_execute_external_script
                @language = N'R'
                , @script = N'
                wi.dtree <- rxDForest(TimeToFinishInDays ~ WorkItemId+Revision+State+WorkItemType, data = WorkItemRevisionFeatures)
                model <- rxSerializeModel(wi.dtree)
                '
                , @input_data_1 = N'SELECT * FROM #WorkItemRevisionFeatures'
                , @input_data_1_name = N'WorkItemRevisionFeatures'
                , @params = N'@model VARBINARY(MAX) OUTPUT'
                , @model = @model OUTPUT

            IF (@model IS NOT NULL)
            BEGIN
                BEGIN TRAN
                    DECLARE @modelversion INT = ISNULL((SELECT  MAX(ModelVersion)
                                                        FROM    AnalyticsInternal.tbl_WorkItemCompletedTimePredictModel
                                                        WHERE   PartitionId = @partitionId
                                                                AND ModelName = 'WorkItemCompletedTimePredictModel'), 0) + 1;

                    INSERT AnalyticsInternal.tbl_WorkItemCompletedTimePredictModel
                    (
                        PartitionId,
                        AnalyticsBatchId,
                        AnalyticsCreatedDate,
                        AnalyticsUpdatedDate,
                        ModelName,
                        ModelVersion,
                        ModelObject,
                        IsActive
                    )
                    VALUES
                    (
                        @partitionId,
                        @batchId,
                        @batchDt,
                        @batchDt,
                        'WorkItemCompletedTimePredictModel',
                        @modelversion,
                        @model,
                        1
                    )
                    SET @insertedCount = @@ROWCOUNT

                    UPDATE  AnalyticsInternal.tbl_WorkItemCompletedTimePredictModel
                    SET     IsActive = 0
                    WHERE   PartitionId = @partitionId
                            AND IsActive = 1
                            AND ModelVersion <> @modelversion
                            AND ModelName = 'WorkItemCompletedTimePredictModel'
                COMMIT TRAN
            END
        END

        DROP TABLE #WorkItemRevisionFeatures
    END
    SET @complete = 1

    RETURN 0
END

GO

