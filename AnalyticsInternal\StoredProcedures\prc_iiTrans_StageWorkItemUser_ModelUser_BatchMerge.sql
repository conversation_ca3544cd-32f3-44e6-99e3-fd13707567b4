/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: EBC01FA2553B05C878D68E59C41C4BB7EE9028C7
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemUser_ModelUser_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @localizedUnknown   NVARCHAR(230)

    EXEC AnalyticsInternal.prc_iGetLocalizedStringFromCollectionRegistry @partitionId = @partitionId,
                                                            @key = 'ENTITY_FIELD_VALUE_UNKNOWN',
                                                            @defaultValue = 'Unknown',
                                                            @localizedValue =  @localizedUnknown OUTPUT

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @triggerBatchIdStart = IIF(@triggerBatchIdStart <= 1, -1, @triggerBatchIdStart) -- on retransform, lower to -1 to account for initial batchid in M121 servicing

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    ;WITH RawSrc AS
    (
        SELECT  a.PartitionId,
                a.UserGuid AS UserId,
                a.UserGuid AS UserSK,
                a.UserDisplayName
        FROM    AnalyticsStage.tbl_WorkItemUser a
        WHERE   a.PartitionId = @partitionId
                AND a.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    )
    , UserNameEmailSplit AS
    (
        SELECT  PartitionId, UserId, UserSK,
                ISNULL(LTRIM(RTRIM([0])), @localizedUnknown) AS UserName,
                ISNULL(LTRIM(RTRIM(substring([1], 0, len([1])))), @localizedUnknown) AS UserEmail
        FROM
        (
            SELECT      *
            FROM        RawSrc
            CROSS APPLY AnalyticsInternal.func_SplitString(UserDisplayName, '<')
        ) AS source
        PIVOT (MAX(value) FOR ordinal in ([0],[1])) AS pivotTable
    )
    , UserNameValidEmail AS
    (
        SELECT  PartitionId,
                UserId,
                UserSK,
                UserName,
                CASE WHEN CHARINDEX('@', UserEmail) = 0 THEN @localizedUnknown ELSE UserEmail END AS UserEmail
        FROM    UserNameEmailSplit
    )

    MERGE TOP(@batchSizeMax) AnalyticsModel.tbl_User AS t
    USING UserNameValidEmail AS s
    ON (t.PartitionId = s.PartitionId AND t.UserSK = s.UserSK)
    WHEN MATCHED AND NOT EXISTS (
        SELECT s.UserName
            , s.UserEmail
            , s.UserId
        INTERSECT
        SELECT t.UserName
            , t.UserEmail
            , t.UserId
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt
        , AnalyticsBatchId = @batchId
        , UserName = s.UserName
        , UserEmail = s.UserEmail
        , UserId = s.UserId
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId
        , AnalyticsBatchId
        , AnalyticsCreatedDate
        , AnalyticsUpdatedDate
        , UserId
        , UserName
        , UserEmail
        , UserSK
    )
    VALUES (
        s.PartitionId
        , @batchId
        , @batchDt
        , @batchDt
        , s.UserId
        , s.UserName
        , s.UserEmail
        , s.UserSK
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    RETURN 0
END

GO

