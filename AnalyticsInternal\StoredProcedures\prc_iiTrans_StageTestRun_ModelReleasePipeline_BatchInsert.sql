/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 9AD6F7A8318B1830921DB8F0C41C4F177B826EA6
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestRun_ModelReleasePipeline_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    CREATE TABLE #Release
    (
        ProjectGuid                     UNIQUEIDENTIFIER NULL,
        ReleaseDefinitionId             INT              NULL,
    )

    INSERT   #Release (ProjectGuid, ReleaseDefinitionId)
    SELECT   ProjectGuid,
             ReleaseDefinitionId
    FROM     AnalyticsStage.tbl_TestRun WITH (INDEX (IX_tbl_TestRun_AxBatchIdChanged), FORCESEEK)
    WHERE    PartitionId = @partitionId
             AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
             AND ReleaseId > 0
             AND ReleaseDefinitionId > 0 -- ReleaseDefinitionId has a default value in the ops store (0). It's rare but old data may have a mix of default and valid data for a single release.
    GROUP BY ReleaseDefinitionId,
             ProjectGuid
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT AnalyticsModel.tbl_ReleasePipeline
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        ReleasePipelineId,
        ProjectSK,
        IsDeleted
    )
    SELECT  TOP (@batchSizeMax) @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            s.ReleaseDefinitionId,
            s.ProjectGuid,
            0 AS IsDeleted
    FROM    #Release AS s
    LEFT JOIN AnalyticsModel.tbl_ReleasePipeline AS t
    ON      t.PartitionId = @partitionId
            AND t.ProjectSK = s.ProjectGuid
            AND t.ReleasePipelineId = s.ReleaseDefinitionId
    WHERE   t.PartitionId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT
    SET @complete = IIF(@insertedCount < @batchSizeMax, 1, 0)

    DROP TABLE #Release

    RETURN 0
END

GO

