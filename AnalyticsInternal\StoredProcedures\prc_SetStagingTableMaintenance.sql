/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D2F161E77F0150A0873A8F182B7D6E3F5AD07449
CREATE PROCEDURE AnalyticsInternal.prc_SetStagingTableMaintenance
    @tableName VARCHAR(64),
    @maintenance BIT,
    @firstPartitionId INT = NULL,
    @lastPartitionId INT = NULL,
    @priorityThreshold INT = NULL,
    @maintenanceReason NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF @maintenance = 0
    BEGIN
        SET @maintenanceReason = NULL
    END

    IF @maintenance = 1 AND LEN(ISNULL(@maintenanceReason, '')) = 0
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670028); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670005
    END

    UPDATE  AnalyticsInternal.tbl_TableProviderShardStream
    SET     Maintenance = @maintenance,
            MaintenanceReason = @maintenanceReason,
            MaintenanceChangedTime = SYSUTCDATETIME()
    WHERE   TableName = @tableName
            AND PartitionId BETWEEN ISNULL(@firstPartitionId, 0) AND ISNULL(ISNULL(@lastPartitionId, @firstPartitionId), **********)
            AND (@maintenance = 0 OR Priority <= ISNULL(@priorityThreshold, **********)) -- only set maintence = 1 for lower priority streams, if specified
END

GO

