/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B86C1A6EF6079E182720D825A87D5172E2C3B901
CREATE PROCEDURE AnalyticsInternal.prc_CleanupDeletedTable
    @partitionId INT,
    @tableName VARCHAR(64),
    @continueToNextTable BIT,
    @retainHistoryDays INT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @now DATETIME = GETUTCDATE()

    DECLARE @batchSize INT = 100000
    DECLARE @deletedRows INT = 0
    DECLARE @complete BIT = 1
    DECLARE @retainDate DATETIME = CAST(DATEADD(day, 0 - @retainHistoryDays, @now) AS DATE) -- truncate to midnight to avoid excessive calls as new deletes are added
    DECLARE @actingTableName VARCHAR(64) = NULL
    DECLARE @dbfqName VARCHAR(128)

    DECLARE @mainTables TABLE
    (
        ProcessingOrder INT,
        TableName       VARCHAR(64) PRIMARY KEY,
        DBFQTableName   VARCHAR(128)
    )

    INSERT INTO @mainTables
    VALUES (1,  'Model.WorkItem',                  'AnalyticsModel.tbl_WorkItem_Deleted'),
           (2,  'Model.TeamArea',                  'AnalyticsModel.tbl_TeamArea_Deleted'),
           (3,  'Model.TeamToTeamField',           'AnalyticsModel.tbl_TeamToTeamField_Deleted'),
           (4,  'ReleaseDefinition',                  'AnalyticsStage.tbl_ReleaseDefinition_Deleted'),
           (5,  'Release',                            'AnalyticsStage.tbl_Release_Deleted'),
           (6,  'WorkItemRevision',                   'AnalyticsStage.tbl_WorkItemRevision_Deleted'),
           (7,  'WorkItemLink',                       'AnalyticsStage.tbl_WorkItemLink_Deleted'),
           (8,  'Internal.WorkItemRevisionKanban', 'AnalyticsInternal.tbl_WorkItemRevisionKanban_Deleted'),
           (9,  'TaskPlan',                           'AnalyticsStage.tbl_TaskPlan_Deleted'),
           (10, 'TestRun',                            'AnalyticsStage.tbl_TestRun_Deleted') -- placed at end because it is the most likely to have rows

    -- skip over tables if @tableName is provided
    DELETE  @mainTables
    WHERE   ProcessingOrder < (SELECT MIN(ProcessingOrder) FROM @mainTables WHERE TableName = @tableName)

    WHILE (EXISTS (SELECT * FROM @mainTables))
    BEGIN
        SELECT  TOP 1 @actingTableName = TableName,
                @dbfqName = DBFQTableName
        FROM    @mainTables
        ORDER BY ProcessingOrder ASC

        DECLARE @cmd NVARCHAR(MAX) = N'
            DELETE  TOP (@batchSize)
            FROM    ' + @dbfqName + '
            WHERE   PartitionId = @partitionId
                    AND AnalyticsDeletedDate < @retainDate
            OPTION  (MAXDOP 1)

            SELECT  @rowCount = @@ROWCOUNT'

        EXEC sp_executesql  @cmd,
                            N'@partitionId INT, @retainDate DATETIME, @batchSize INT, @rowCount INT OUTPUT',
                            @partitionId = @partitionId,
                            @retainDate = @retainDate,
                            @batchSize = @batchSize,
                            @rowCount = @deletedRows OUTPUT

        IF (@deletedRows > 0)
        BEGIN
            SET @complete = IIF(@deletedRows < @batchSize, 1, 0)
            BREAK
        END

        IF (ISNULL(@continueToNextTable, 0) = 0)
        BEGIN
            BREAK
        END

        DELETE  @mainTables
        WHERE   TableName = @actingTableName

        SET @actingTableName = NULL
    END

    SELECT  @complete AS Complete,
            @actingTableName AS TableName,
            @deletedRows AS DeletedRows

    RETURN 0
END

GO

