/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 9F82420EF507888E4123063CD114AA235ED63864
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_ModelWorkItemProcess_BatchReplace_v2
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    DECLARE @workItemIdStart INT = ISNULL(@stateData + 1, 0)
    DECLARE @triggerWorkItemId TABLE
    (
        System_Id INT NOT NULL PRIMARY KEY,
        [Count] INT NOT NULL
    )

    IF (@state = 'dense') -- use CI for sub-batch selection on denser batches
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (CI_tbl_WorkItemRevision))
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
                    AND wi.System_Id < @workItemIdStart + (@batchSizeMax * 10) -- prevent runaway scans
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = 0
        SET @endState = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 'dense', '')
    END
    ELSE -- use NCI
    BEGIN
        INSERT  @triggerWorkItemId
        SELECT  System_Id, COUNT(*)
        FROM
        (
            SELECT  TOP (@batchSizeMax) System_Id
            FROM    AnalyticsStage.tbl_WorkItemRevision wi WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
            WHERE   wi.PartitionId = @partitionId
                    AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND wi.System_Id >= @workItemIdStart
            ORDER BY System_Id
        ) T
        GROUP BY System_Id
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN, @triggerBatchIdStart = 0, @triggerBatchIdEnd = 1))

        SET @endStateData = ISNULL((SELECT MAX(System_Id) FROM @triggerWorkItemId), @stateData)
        SET @complete = IIF((SELECT SUM([Count]) FROM @triggerWorkItemId) >= @batchSizeMax, 0, 1)
        SET @endState = IIF(@complete = 0 AND @endStateData < @workItemIdStart + (@batchSizeMax * 2), 'dense', '')
    END

    CREATE TABLE #ImpactedRev
    (
        TeamFieldSK         INT NULL,
        ProjectGuid         UNIQUEIDENTIFIER NULL,
        WorkItemType        NVARCHAR(256) COLLATE DATABASE_DEFAULT NULL,
        WorkItemRevisionSK  INT NOT NULL,
        PRIMARY KEY CLUSTERED (WorkItemRevisionSK)
    )

    INSERT  #ImpactedRev
    SELECT  ISNULL(tf1.TeamFieldSK, tf2.TeamFieldSK) AS TeamFieldSK,
            wi.System_ProjectGuid,
            wi.System_WorkItemType,
            r.WorkItemRevisionSK
    FROM    @triggerWorkItemId tid
    INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision wi
    ON      wi.System_Id = tid.System_Id
    INNER LOOP JOIN AnalyticsInternal.vw_WorkItemRevisionSK r
    ON      r.PartitionId = wi.PartitionId
            AND r.WorkItemId = wi.System_Id
            AND r.Revision = wi.System_Rev
    LEFT JOIN AnalyticsModel.tbl_Project p
    ON      p.PartitionId = wi.PartitionId
            AND p.ProjectId = wi.System_ProjectGuid
    LEFT JOIN AnalyticsModel.tbl_TeamField tf1 -- team field from area
    ON      tf1.PartitionId = p.PartitionId
            AND tf1.ProjectId = p.ProjectId
            AND tf1.TeamFieldReferenceName = ISNULL(p.TeamFieldReferenceName, 'System.AreaPath')
            AND tf1.TeamFieldReferenceName = 'System.AreaPath'
            AND tf1.AreaId = wi.System_AreaGuid
    LEFT JOIN AnalyticsInternal.tbl_Fields f
    ON      f.PartitionId = p.PartitionId
            AND f.TableName = 'WorkItemRevision'
            AND f.FieldName = p.TeamFieldSourceFieldName
            AND p.TeamFieldReferenceName <> 'System.AreaPath'
    LEFT LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x
    ON      x.PartitionId = f.PartitionId
            AND x.FieldSK = f.FieldSK
            AND x.System_Id = wi.System_Id
            AND x.System_Rev = wi.System_Rev
    LEFT JOIN AnalyticsModel.tbl_TeamField tf2 -- team field from custom fields
    ON      tf2.PartitionId = p.PartitionId
            AND tf2.ProjectId = p.ProjectId
            AND tf2.TeamFieldReferenceName = ISNULL(p.TeamFieldReferenceName, 'System.AreaPath')
            AND tf2.TeamFieldReferenceName <> 'System.AreaPath'
            AND tf2.TeamFieldValue = x.ValueString
    WHERE   wi.PartitionId = @partitionId
            AND wi.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

    CREATE TABLE #Src
    (
        WorkItemRevisionSK  INT NOT NULL,
        ProcessSK           INT NOT NULL,
    )

    INSERT  #Src (WorkItemRevisionSK, ProcessSK)
    SELECT  wi.WorkItemRevisionSK,
            p.ProcessSK
    FROM    #ImpactedRev wi
    INNER LOOP JOIN AnalyticsModel.tbl_TeamToTeamField ta
    ON      ta.PartitionId = @partitionId
            AND ta.TeamFieldSK = wi.TeamFieldSK
    INNER LOOP JOIN AnalyticsModel.tbl_Process p
    ON      p.PartitionId = @partitionId
            AND p.ProjectSK = wi.ProjectGuid
            AND p.WorkItemType = wi.WorkItemType
    WHERE ta.TeamSK = p.TeamSK
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    BEGIN TRAN
        -- tbl_WorkItemProcess should be much larger, so force loop join using index
        DELETE  t
        FROM    #ImpactedRev AS r
        LEFT JOIN #Src AS s
        ON      s.WorkItemRevisionSK = r.WorkItemRevisionSK
        INNER LOOP JOIN AnalyticsModel.tbl_WorkItemProcess AS t WITH (ROWLOCK)
        ON      r.WorkItemRevisionSK = t.WorkItemRevisionSK
                AND s.ProcessSK = t.ProcessSK
        WHERE   t.PartitionId = @partitionId
                AND s.WorkItemRevisionSK IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @deletedCount = @@ROWCOUNT

        DELETE  s
        FROM    #Src s
        INNER LOOP JOIN AnalyticsModel.tbl_WorkItemProcess t
        ON      t.PartitionId = @partitionId
                AND t.WorkItemRevisionSK = s.WorkItemRevisionSK
                AND t.ProcessSK = s.ProcessSK
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        INSERT AnalyticsModel.tbl_WorkItemProcess
            (
            PartitionId,
            AnalyticsBatchId,
            AnalyticsCreatedDate,
            AnalyticsUpdatedDate,
            WorkItemRevisionSK,
            ProcessSK
            )
        SELECT
            @partitionId,
            @batchId,
            @batchDt,
            @batchDt,
            s.WorkItemRevisionSK,
            s.ProcessSK
        FROM #Src AS s

        SET @insertedCount = @@ROWCOUNT
    COMMIT TRAN

    DROP TABLE #ImpactedRev
    DROP TABLE #Src

    RETURN 0
END

GO

