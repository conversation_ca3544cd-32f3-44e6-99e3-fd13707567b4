/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: CDB3A8CECA96DEFF29A5AD0096F8800A1E018692
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestResult_ModelTest_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #ImpactedRuns
    (
        TestRunId INT NOT NULL,
        DataSourceId INT NOT NULL
    )

    IF (@triggerBatchIdStart > 1 AND (@stateData IS NULL OR @state = 'skip')) -- always assume NCI for small batches, and split datasource tables
    BEGIN
        -- Run that for first sub-batch in incremental processing or if we failed due gaps in TestRunIds caused by TFS and TCM running in parallel
        INSERT  #ImpactedRuns
        SELECT  TOP (@batchSizeMax) WITH TIES
                TestRunId,
                DataSourceId
        FROM    AnalyticsStage.tbl_TestResult WITH (INDEX (IX_tbl_TestResult_AnalyticsBatchIdCreated), FORCESEEK)
        WHERE   PartitionId = @partitionId
                AND TestRunId > ISNULL(@stateData, -1)
                AND AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        GROUP BY TestRunId, DataSourceId
        ORDER BY TestRunId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
        SET @endState = '' -- try sequential optimizaion next time
    END
    ELSE
    BEGIN
        -- this opimization assumes test results arrive with roughly sequential run ids
        CREATE TABLE #ImpactedRuns0
        (
            TestRunId INT NOT NULL
        )

        IF (@triggerBatchIdStart > 1)
        BEGIN
            DECLARE @prevRunId BIGINT = ISNULL(@stateData, -1)
            DECLARE @maxRunId BIGINT = @prevRunId + (@batchSizeMax * 10) -- runaway window
            SET @maxRunId = IIF(@maxRunId > @prevRunId + 1000, @maxRunId, @prevRunId + 1000) -- ensure minimum runaway window

            -- getting TestRunId alone from clustered index is much faster
            INSERT  #ImpactedRuns0
            SELECT  TOP (@batchSizeMax)
                    TestRunId
            FROM    AnalyticsStage.tbl_TestResult WITH (INDEX (CI_tbl_TestResult))
            WHERE   PartitionId = @partitionId
                    AND TestRunId > @prevRunId
                    AND TestRunId < @maxRunId -- prevent runaway scans for split ranges
                    AND AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            GROUP BY TestRunId
            ORDER BY TestRunId
            OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @endState = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 'skip' ELSE '' END -- if we exhausted the run ids in this range, switch back to other index
            SET @complete = 0
        END
        ELSE
        BEGIN -- retransform
            -- getting TestRunId alone from clustered index is much faster
            INSERT  #ImpactedRuns0
            SELECT  TOP (@batchSizeMax)
                    TestRunId
            FROM    AnalyticsStage.tbl_TestResult WITH (INDEX (CI_tbl_TestResult))
            WHERE   PartitionId = @partitionId
                    AND TestRunId > ISNULL(@stateData, -1)
                    AND AnalyticsBatchIdCreated <= @triggerBatchIdEnd
            GROUP BY TestRunId
            ORDER BY TestRunId
            OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endState = ''
        END

        -- now flesh out the DataSourceId
        INSERT  #ImpactedRuns
        SELECT  r.TestRunId,
                r.DataSourceId
        FROM    #ImpactedRuns0 ir
        INNER LOOP JOIN AnalyticsStage.tbl_TestResult r WITH (INDEX (CI_tbl_TestResult))
        ON      r.TestRunId = ir.TestRunId
        WHERE   r.PartitionId = @partitionId
                AND r.AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        GROUP BY r.TestRunId, r.DataSourceId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        DROP TABLE #ImpactedRuns0
    END

    SET @endStateData = ISNULL((SELECT MAX(TestRunId) FROM #ImpactedRuns), @stateData)

    CREATE TABLE #Test
    (
        TestCaseReferenceId             INT                 NOT NULL,
        DataSourceId                    INT                 NOT NULL
    )

    INSERT  #Test (TestCaseReferenceId, DataSourceId)
    SELECT  DISTINCT TestCaseReferenceId, s.DataSourceId
    FROM    AnalyticsStage.tbl_TestResult s
    JOIN    #ImpactedRuns ir
    ON      ir.TestRunId = s.TestRunId
    AND     ir.DataSourceId = s.DataSourceId
    WHERE   PartitionId = @partitionId
            AND AnalyticsBatchIdCreated BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND TestCaseReferenceId IS NOT NULL
            AND s.DataSourceId IS NOT NULL
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- Remove which are not to be added.
    DELETE  s
    FROM    #Test AS s
    INNER JOIN AnalyticsModel.tbl_Test AS t
    ON      t.PartitionId = @partitionId
            AND t.TestCaseReferenceId = s.TestCaseReferenceId
            AND t.DataSourceId = s.DataSourceId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

    -- Insert test refs with data from stage table if available.
    -- This is for corner scenario when tests are deleted from model table by retention and test results are executed after that. This will create refs also.
    INSERT AnalyticsModel.tbl_Test
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        TestCaseReferenceId,
        DataSourceId,
        TestName,
        TestOwner,
        ContainerName,
        Priority,
        ProjectSK,
        FullyQualifiedTestName
    )
    SELECT  @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            s.TestCaseReferenceId,
            s.DataSourceId,
            ref.TestCaseTitle,
            ref.Owner,
            ref.AutomatedTestStorage,
            ref.Priority,
            ref.ProjectGuid,
            ref.AutomatedTestName
    FROM    #Test AS s
    LEFT JOIN AnalyticsStage.tbl_TestCaseReference AS ref
    ON      ref.PartitionId = @partitionId
            AND ref.TestCaseReferenceId = s.TestCaseReferenceId
            AND ref.DataSourceId = s.DataSourceId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT

    DROP TABLE #Test
    DROP TABLE #ImpactedRuns

    RETURN 0
END

GO

