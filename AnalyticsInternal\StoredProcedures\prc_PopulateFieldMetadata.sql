/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 91002637994C315C00590B7233A83ACB2C434B19
CREATE PROCEDURE AnalyticsInternal.prc_PopulateFieldMetadata
    @partitionId    INT,
    @metadata AnalyticsInternal.typ_FieldMetadata2 READONLY
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @now DATETIME = GETUTCDATE()

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    ;WITH Src AS
    (
        SELECT
            TableName
            ,FieldName
            ,ISNULL(ExtensionId,
                IIF(LEFT(FieldName, 4) = 'WEF_',
                    TRY_CAST(
                        SUBSTRING(FieldName, 5, 8) + '-' +
                        SUBSTRING(FieldName, 13, 4) + '-' +
                        SUBSTRING(FieldName, 17, 4) + '-' +
                        SUBSTRING(FieldName, 21, 4) + '-' +
                        SUBSTRING(FieldName, 25, 12)
                        AS UNIQUEIDENTIFIER), NULL)
                ) AS ExtensionId
            ,ISNULL(ExtensionFieldName,
                IIF(LEFT(FieldName, 4) = 'WEF_', SUBSTRING(FieldName, 38, 10000), NULL)
                ) AS ExtensionFieldName
            ,FieldDisplayName
            ,FieldDescription
            ,FieldType
            ,FieldNullable
        FROM @metadata
    )
    MERGE AnalyticsInternal.tbl_Fields AS t
    USING Src AS s
    ON (t.PartitionId = @partitionId AND t.TableName = s.TableName AND t.FieldName = s.FieldName COLLATE Latin1_General_CI_AS)
    WHEN MATCHED
    THEN UPDATE SET
        ExtensionId = s.ExtensionId
        ,ExtensionFieldName = s.ExtensionFieldName
        ,FieldDisplayName = s.FieldDisplayName
        ,FieldDescription = s.FieldDescription
        ,FieldType = s.FieldType
        ,FieldNullable = s.FieldNullable
        ,UpdateDate = @now
    WHEN NOT MATCHED BY TARGET
    THEN INSERT
        (PartitionId
        ,TableName
        ,FieldName
        ,ExtensionId
        ,ExtensionFieldName
        ,FieldDisplayName
        ,FieldDescription
        ,FieldType
        ,FieldNullable
        ,CreateDate
        ,UpdateDate)
    VALUES
        (@partitionId
        ,s.TableName
        ,s.FieldName
        ,s.ExtensionId
        ,s.ExtensionFieldName
        ,s.FieldDisplayName
        ,s.FieldDescription
        ,s.FieldType
        ,s.FieldNullable
        ,@now
        ,@now)
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

END

GO

