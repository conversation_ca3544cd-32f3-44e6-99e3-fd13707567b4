CREATE TABLE [AnalyticsModel].[tbl_TestRun] (
    [PartitionId]                     INT                NOT NULL,
    [AnalyticsCreatedDate]            DATETIME           NOT NULL,
    [AnalyticsUpdatedDate]            DATETIME           NOT NULL,
    [AnalyticsBatchId]                BIGINT             NOT NULL,
    [TestRunSK]                       INT                IDENTITY (1, 1) NOT NULL,
    [TestRunId]                       INT                NOT NULL,
    [ProjectSK]                       UNIQUEIDENTIFIER   NULL,
    [Title]                           NVARCHAR (256)     NULL,
    [IsAutomated]                     BIT                NULL,
    [ReleaseSK]                       INT                NULL,
    [ReleaseEnvironmentSK]            INT                NULL,
    [BuildSK]                         INT                NULL,
    [BranchSK]                        INT                NULL,
    [StartedDate]                     DATETIMEOFFSET (7) NULL,
    [StartedDateSK]                   INT                NULL,
    [CompletedDate]                   DATETIMEOFFSET (7) NULL,
    [CompletedDateSK]                 INT                NULL,
    [RunDurationSeconds]              DECIMAL (18, 3)    NULL,
    [ResultDurationSeconds]           DECIMAL (18, 3)    NULL,
    [ResultCount]                     INT                NULL,
    [ResultPassCount]                 INT                NULL,
    [ResultFailCount]                 INT                NULL,
    [ResultOutcomeNoneCount]          INT                NULL,
    [ResultOutcomePassedCount]        INT                NULL,
    [ResultOutcomeFailedCount]        INT                NULL,
    [ResultOutcomeInconclusiveCount]  INT                NULL,
    [ResultOutcomeTimeoutCount]       INT                NULL,
    [ResultOutcomeAbortedCount]       INT                NULL,
    [ResultOutcomeBlockedCount]       INT                NULL,
    [ResultOutcomeNotExecutedCount]   INT                NULL,
    [ResultOutcomeWarningCount]       INT                NULL,
    [ResultOutcomeErrorCount]         INT                NULL,
    [ResultOutcomeNotApplicableCount] INT                NULL,
    [ResultOutcomeNotImpactedCount]   INT                NULL,
    [Workflow]                        TINYINT            NULL,
    [TestRunType]                     TINYINT            NULL,
    [DataSourceId]                    TINYINT            NOT NULL,
    [ReleasePipelineSK]               INT                NULL,
    [ReleaseStageSK]                  INT                NULL,
    [BuildPipelineSK]                 INT                NULL
) ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE NONCLUSTERED INDEX [IX_tbl_TestRun_AnalyticsBatchId]
    ON [AnalyticsModel].[tbl_TestRun]([PartitionId] ASC, [AnalyticsBatchId] ASC)
    INCLUDE([TestRunId]) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_TestRun_TestRunSK]
    ON [AnalyticsModel].[tbl_TestRun]([PartitionId] ASC, [TestRunSK] ASC)
    INCLUDE([TestRunId]) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (11), DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_TestRun_TestRunId]
    ON [AnalyticsModel].[tbl_TestRun]([PartitionId] ASC, [TestRunId] ASC, [DataSourceId] ASC)
    INCLUDE([ReleaseSK], [ReleaseEnvironmentSK], [BranchSK], [BuildSK]) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_TestRun]
    ON [AnalyticsModel].[tbl_TestRun]
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

