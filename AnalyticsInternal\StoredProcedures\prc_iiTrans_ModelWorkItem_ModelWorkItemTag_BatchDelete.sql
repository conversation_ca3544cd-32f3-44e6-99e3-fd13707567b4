/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: AE17F36DD4DF81FA694ACD22FE5F4A2F806C7DF2
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelWorkItem_ModelWorkItemTag_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #Deleted
    (
        WorkItemRevisionSK BIGINT
    )

    IF (@subBatchCount = 1)
    BEGIN
        INSERT  #Deleted
        SELECT  TOP (@batchSizeMax) WorkItemRevisionSK
        FROM    AnalyticsModel.tbl_WorkItem_Deleted d WITH (INDEX (IX_AnalyticsModel_tbl_WorkItem_AxBatchIdDeleted))
        WHERE   PartitionId = @partitionId
                AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND d.WorkItemRevisionSK > ISNULL(@stateData, -1)
        ORDER BY WorkItemRevisionSK
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        INSERT  #Deleted
        SELECT  TOP (@batchSizeMax) WorkItemRevisionSK
        FROM    AnalyticsModel.tbl_WorkItem_Deleted d WITH (INDEX (CI_AnalyticsModel_tbl_WorkItem_Deleted))
        WHERE   PartitionId = @partitionId
                AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND d.WorkItemRevisionSK > ISNULL(@stateData, -1)
        ORDER BY WorkItemRevisionSK
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    DECLARE @toDeleteCount INT = @@ROWCOUNT

    SET @complete = CASE WHEN @toDeleteCount < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(WorkItemRevisionSK) FROM #Deleted)

    IF (@toDeleteCount > 100)
    BEGIN
        -- make sure the items are really deleted from the revisions table
        DELETE  d
        FROM    #Deleted d
        INNER HASH JOIN AnalyticsInternal.vw_WorkItemRevisionSK r -- force hash join for larger operations
        ON      r.WorkItemRevisionSK = d.WorkItemRevisionSK
        WHERE   r.PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END
    ELSE
    BEGIN
        -- make sure the items are really deleted from the revisions table
        DELETE  d
        FROM    #Deleted d
        JOIN    AnalyticsInternal.vw_WorkItemRevisionSK r
        ON      r.WorkItemRevisionSK = d.WorkItemRevisionSK
        WHERE   r.PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    DELETE  t
    FROM    AnalyticsModel.tbl_WorkItemTag t
    JOIN    #Deleted d
    ON      d.WorkItemRevisionSK = t.WorkItemRevisionSK
    WHERE   t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DROP TABLE #Deleted

    RETURN 0
END

GO

