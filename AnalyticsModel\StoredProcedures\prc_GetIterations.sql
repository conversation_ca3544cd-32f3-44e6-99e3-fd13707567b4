/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 5D412E9B76CB863B2D36DADF9A627D9FDD51969C
CREATE PROCEDURE AnalyticsModel.prc_GetIterations
    @partitionId     INT,
    @iterationIds    typ_GuidTable READONLY
AS
BEGIN

    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    BEGIN
        SELECT  ProjectSK,
                IterationId,
                IterationName,
                Number,
                IterationPath,
                Depth
        FROM    AnalyticsModel.tbl_Iteration i
        JOIN    @iterationIds iterationIds
        ON      i.IterationId = iterationIds.Id
        WHERE   i.PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

END

GO

