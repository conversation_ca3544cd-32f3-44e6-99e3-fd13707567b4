/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F6A5172E278BCE80972F35D83F36776C0FCCCE54
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_InternalCollectionTimeZone_ModelWorkItem_TableUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)

    -- Backoff max batch size on failures
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    -- use sub batching to remember update progress.
    DECLARE @workItemIdStart BIGINT = ISNULL(@stateData + 1, 0)
    DECLARE @subBatchWorkItemIdStart BIGINT
    DECLARE @subBatchWorkItemIdEnd BIGINT

    EXEC AnalyticsInternal.prc_iGetWorkItemSubBatchIdsFromModel
        @partitionId,
        @workItemIdStart,
        @batchSizeMax,
        @subBatchWorkItemIdStart = @subBatchWorkItemIdStart OUTPUT,
        @subBatchWorkItemIdEnd = @subBatchWorkItemIdEnd OUTPUT,
        @complete = @complete OUTPUT

    SET @endStateData = @subBatchWorkItemIdEnd

    -------------------------------------------------------------------------------
    -- WorkItemHistory
    -------------------------------------------------------------------------------
    ;WITH WorkItemBase AS
    (
        SELECT  ChangedDate,
                CreatedDate,
                RevisedDate,
                ActivatedDate,
                ClosedDate,
                ResolvedDate,
                InProgressDate,
                CompletedDate,
                AuthorizedDate,
                StateChangeDate,
                DateSK,
                CreatedDateSK,
                RevisedDateSK,
                ActivatedDateSK,
                ClosedDateSK,
                ResolvedDateSK,
                InProgressDateSK,
                CompletedDateSK,
                StateChangeDateSK,
                IsLastRevisionOfDay,
                IsLastRevisionOfPeriod,
                -- new values
                ChangedDate AT TIME ZONE @timeZone            AS NewChangedDate,
                CreatedDate AT TIME ZONE @timeZone            AS NewCreatedDate,
                RevisedDate AT TIME ZONE @timeZone            AS NewRevisedDate,
                ActivatedDate AT TIME ZONE @timeZone          AS NewActivatedDate,
                ClosedDate AT TIME ZONE @timeZone             AS NewClosedDate,
                ResolvedDate AT TIME ZONE @timeZone           AS NewResolvedDate,
                InProgressDate AT TIME ZONE @timeZone         AS NewInProgressDate,
                CompletedDate AT TIME ZONE @timeZone          AS NewCompletedDate,
                AuthorizedDate AT TIME ZONE @timeZone         AS NewAuthorizedDate,
                StateChangeDate AT TIME ZONE @timeZone        AS NewStateChangeDate,
                CAST(ChangedDate AT TIME ZONE @timeZone AS DATE) AS NewChangedDay,
                CAST(RevisedDate AT TIME ZONE @timeZone AS DATE) AS NewRevisedDay
        FROM    AnalyticsModel.tbl_WorkItemHistory
        WHERE   PartitionId = @partitionId
                AND WorkItemId BETWEEN @subBatchWorkItemIdStart AND @subBatchWorkItemIdEnd
    )
    , WorkItem AS
    (
        SELECT  ChangedDate,
                CreatedDate,
                RevisedDate,
                ActivatedDate,
                ClosedDate,
                ResolvedDate,
                InProgressDate,
                CompletedDate,
                AuthorizedDate,
                StateChangeDate,
                DateSK,
                CreatedDateSK,
                RevisedDateSK,
                ActivatedDateSK,
                ClosedDateSK,
                ResolvedDateSK,
                InProgressDateSK,
                CompletedDateSK,
                StateChangeDateSK,
                IsLastRevisionOfDay,
                IsLastRevisionOfPeriod,
                -- new values
                NewChangedDate,
                NewCreatedDate,
                NewRevisedDate,
                NewActivatedDate,
                NewClosedDate,
                NewResolvedDate,
                NewInProgressDate,
                NewCompletedDate,
                NewAuthorizedDate,
                NewStateChangeDate,
                AnalyticsInternal.func_GenDateSK(NewChangedDate)   AS NewDateSK,
                AnalyticsInternal.func_GenDateSK(NewCreatedDate)   AS NewCreatedDateSK,
                AnalyticsInternal.func_GenDateSK(NewRevisedDate)   AS NewRevisedDateSK,
                AnalyticsInternal.func_GenDateSK(NewActivatedDate) AS NewActivatedDateSK,
                AnalyticsInternal.func_GenDateSK(NewClosedDate)    AS NewClosedDateSK,
                AnalyticsInternal.func_GenDateSK(NewResolvedDate)  AS NewResolvedDateSK,
                AnalyticsInternal.func_GenDateSK(NewInProgressDate) AS NewInProgressDateSK,
                AnalyticsInternal.func_GenDateSK(NewCompletedDate) AS NewCompletedDateSK,
                AnalyticsInternal.func_GenDateSK(NewStateChangeDate) AS NewStateChangeDateSK,
                IIF(CAST(NewRevisedDate AS DATE) = CAST(NewChangedDate AS DATE), 0, 1) AS NewIsLastRevisionOfDay,
                -- flattened AnalyticsInternal.func_GetIsLastRevisionOfPeriod
                IIF(NewRevisedDay IS NULL, 2047,
                      IIF(DATEDIFF(DAY, NewChangedDay, NewRevisedDay) > 0, 1, 0)
                    | IIF(DATEDIFF(WEEK, DATEADD(DAY, -0, NewChangedDay), DATEADD(DAY, -0, NewRevisedDay)) > 0, 128, 0)
                    | IIF(DATEDIFF(WEEK, DATEADD(DAY, -1, NewChangedDay), DATEADD(DAY, -1, NewRevisedDay)) > 0, 2, 0)
                    | IIF(DATEDIFF(WEEK, DATEADD(DAY, -2, NewChangedDay), DATEADD(DAY, -2, NewRevisedDay)) > 0, 4, 0)
                    | IIF(DATEDIFF(WEEK, DATEADD(DAY, -3, NewChangedDay), DATEADD(DAY, -3, NewRevisedDay)) > 0, 8, 0)
                    | IIF(DATEDIFF(WEEK, DATEADD(DAY, -4, NewChangedDay), DATEADD(DAY, -4, NewRevisedDay)) > 0, 16, 0)
                    | IIF(DATEDIFF(WEEK, DATEADD(DAY, -5, NewChangedDay), DATEADD(DAY, -5, NewRevisedDay)) > 0, 32, 0)
                    | IIF(DATEDIFF(WEEK, DATEADD(DAY, -6, NewChangedDay), DATEADD(DAY, -6, NewRevisedDay)) > 0, 64, 0)
                    | IIF(DATEDIFF(MONTH,   NewChangedDay, NewRevisedDay) > 0, 256, 0)
                    | IIF(DATEDIFF(QUARTER, NewChangedDay, NewRevisedDay) > 0, 512, 0)
                    | IIF(DATEDIFF(YEAR,    NewChangedDay, NewRevisedDay) > 0, 1024, 0)
                    ) AS NewIsLastRevisionOfPeriod
        FROM    WorkItemBase
    )
    UPDATE  t
    SET     ChangedDate = NewChangedDate,
            CreatedDate = NewCreatedDate,
            RevisedDate = NewRevisedDate,
            ActivatedDate = NewActivatedDate,
            ClosedDate = NewClosedDate,
            ResolvedDate = NewResolvedDate,
            InProgressDate = NewInProgressDate,
            CompletedDate = NewCompletedDate,
            AuthorizedDate = NewAuthorizedDate,
            StateChangeDate = NewStateChangeDate,
            DateSK = NewDateSK,
            CreatedDateSK = NewCreatedDateSK,
            RevisedDateSK = NewRevisedDateSK,
            ActivatedDateSK = NewActivatedDateSK,
            ClosedDateSK = NewClosedDateSK,
            ResolvedDateSK = NewResolvedDateSK,
            InProgressDateSK = NewInProgressDateSK,
            CompletedDateSK = NewCompletedDateSK,
            StateChangeDateSK = NewStateChangeDateSK,
            IsLastRevisionOfDay = NewIsLastRevisionOfDay,
            IsLastRevisionOfPeriod = NewIsLastRevisionOfPeriod
    FROM    WorkItem t
    WHERE   NOT EXISTS (
                SELECT
                CAST(ChangedDate AS DATETIME), -- detect offset change
                CAST(CreatedDate AS DATETIME), -- detect offset change
                CAST(RevisedDate AS DATETIME), -- detect offset change
                CAST(ActivatedDate AS DATETIME), -- detect offset change
                CAST(ClosedDate AS DATETIME), -- detect offset change
                CAST(ResolvedDate AS DATETIME), -- detect offset change
                CAST(InProgressDate AS DATETIME), -- detect offset change
                CAST(CompletedDate AS DATETIME), -- detect offset change
                CAST(AuthorizedDate AS DATETIME), -- detect offset change
                CAST(StateChangeDate AS DATETIME), -- detect offset change
                DateSK,
                CreatedDateSK,
                RevisedDateSK,
                ActivatedDateSK,
                ClosedDateSK,
                ResolvedDateSK,
                InProgressDateSK,
                CompletedDateSK,
                StateChangeDateSK,
                IsLastRevisionOfDay,
                IsLastRevisionOfPeriod
                INTERSECT
                SELECT
                CAST(NewChangedDate AS DATETIME), -- detect offset change
                CAST(NewCreatedDate AS DATETIME), -- detect offset change
                CAST(NewRevisedDate AS DATETIME), -- detect offset change
                CAST(NewActivatedDate AS DATETIME), -- detect offset change
                CAST(NewClosedDate AS DATETIME), -- detect offset change
                CAST(NewResolvedDate AS DATETIME), -- detect offset change
                CAST(NewInProgressDate AS DATETIME), -- detect offset change
                CAST(NewCompletedDate AS DATETIME), -- detect offset change
                CAST(NewAuthorizedDate AS DATETIME), -- detect offset change
                CAST(NewStateChangeDate AS DATETIME), -- detect offset change
                NewDateSK,
                NewCreatedDateSK,
                NewRevisedDateSK,
                NewActivatedDateSK,
                NewClosedDateSK,
                NewResolvedDateSK,
                NewInProgressDateSK,
                NewCompletedDateSK,
                NewStateChangeDateSK,
                NewIsLastRevisionOfDay,
                NewIsLastRevisionOfPeriod
                )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    -------------------------------------------------------------------------------
    -- WorkItem
    -------------------------------------------------------------------------------
    ;WITH WorkItemBase AS
    (
        SELECT  ChangedDate,
                CreatedDate,
                ActivatedDate,
                ClosedDate,
                ResolvedDate,
                InProgressDate,
                CompletedDate,
                AuthorizedDate,
                StateChangeDate,
                DateSK,
                CreatedDateSK,
                ActivatedDateSK,
                ClosedDateSK,
                ResolvedDateSK,
                InProgressDateSK,
                CompletedDateSK,
                StateChangeDateSK,
                -- new values
                ChangedDate AT TIME ZONE @timeZone            AS NewChangedDate,
                CreatedDate AT TIME ZONE @timeZone            AS NewCreatedDate,
                ActivatedDate AT TIME ZONE @timeZone          AS NewActivatedDate,
                ClosedDate AT TIME ZONE @timeZone             AS NewClosedDate,
                ResolvedDate AT TIME ZONE @timeZone           AS NewResolvedDate,
                InProgressDate AT TIME ZONE @timeZone         AS NewInProgressDate,
                CompletedDate AT TIME ZONE @timeZone          AS NewCompletedDate,
                AuthorizedDate AT TIME ZONE @timeZone         AS NewAuthorizedDate,
                StateChangeDate AT TIME ZONE @timeZone        AS NewStateChangeDate,
                CAST(ChangedDate AT TIME ZONE @timeZone AS DATE) AS NewChangedDay
        FROM    AnalyticsModel.tbl_WorkItem
        WHERE   PartitionId = @partitionId
                AND WorkItemId BETWEEN @subBatchWorkItemIdStart AND @subBatchWorkItemIdEnd
    )
    , WorkItem AS
    (
        SELECT  ChangedDate,
                CreatedDate,
                ActivatedDate,
                ClosedDate,
                ResolvedDate,
                InProgressDate,
                CompletedDate,
                AuthorizedDate,
                StateChangeDate,
                DateSK,
                CreatedDateSK,
                ActivatedDateSK,
                ClosedDateSK,
                ResolvedDateSK,
                InProgressDateSK,
                CompletedDateSK,
                StateChangeDateSK,
                -- new values
                NewChangedDate,
                NewCreatedDate,
                NewActivatedDate,
                NewClosedDate,
                NewResolvedDate,
                NewInProgressDate,
                NewCompletedDate,
                NewAuthorizedDate,
                NewStateChangeDate,
                AnalyticsInternal.func_GenDateSK(NewChangedDate)   AS NewDateSK,
                AnalyticsInternal.func_GenDateSK(NewCreatedDate)   AS NewCreatedDateSK,
                AnalyticsInternal.func_GenDateSK(NewActivatedDate) AS NewActivatedDateSK,
                AnalyticsInternal.func_GenDateSK(NewClosedDate)    AS NewClosedDateSK,
                AnalyticsInternal.func_GenDateSK(NewResolvedDate)  AS NewResolvedDateSK,
                AnalyticsInternal.func_GenDateSK(NewInProgressDate) AS NewInProgressDateSK,
                AnalyticsInternal.func_GenDateSK(NewCompletedDate) AS NewCompletedDateSK,
                AnalyticsInternal.func_GenDateSK(NewStateChangeDate) AS NewStateChangeDateSK
        FROM    WorkItemBase
    )
    UPDATE  t
    SET     ChangedDate = NewChangedDate,
            CreatedDate = NewCreatedDate,
            ActivatedDate = NewActivatedDate,
            ClosedDate = NewClosedDate,
            ResolvedDate = NewResolvedDate,
            InProgressDate = NewInProgressDate,
            CompletedDate = NewCompletedDate,
            AuthorizedDate = NewAuthorizedDate,
            StateChangeDate = NewStateChangeDate,
            DateSK = NewDateSK,
            CreatedDateSK = NewCreatedDateSK,
            ActivatedDateSK = NewActivatedDateSK,
            ClosedDateSK = NewClosedDateSK,
            ResolvedDateSK = NewResolvedDateSK,
            InProgressDateSK = NewInProgressDateSK,
            CompletedDateSK = NewCompletedDateSK,
            StateChangeDateSK = NewStateChangeDateSK
    FROM    WorkItem t
    WHERE   NOT EXISTS (
                SELECT
                CAST(ChangedDate AS DATETIME), -- detect offset change
                CAST(CreatedDate AS DATETIME), -- detect offset change
                CAST(ActivatedDate AS DATETIME), -- detect offset change
                CAST(ClosedDate AS DATETIME), -- detect offset change
                CAST(ResolvedDate AS DATETIME), -- detect offset change
                CAST(InProgressDate AS DATETIME), -- detect offset change
                CAST(CompletedDate AS DATETIME), -- detect offset change
                CAST(AuthorizedDate AS DATETIME), -- detect offset change
                CAST(StateChangeDate AS DATETIME), -- detect offset change
                DateSK,
                CreatedDateSK,
                ActivatedDateSK,
                ClosedDateSK,
                ResolvedDateSK,
                InProgressDateSK,
                CompletedDateSK,
                StateChangeDateSK
                INTERSECT
                SELECT
                CAST(NewChangedDate AS DATETIME), -- detect offset change
                CAST(NewCreatedDate AS DATETIME), -- detect offset change
                CAST(NewActivatedDate AS DATETIME), -- detect offset change
                CAST(NewClosedDate AS DATETIME), -- detect offset change
                CAST(NewResolvedDate AS DATETIME), -- detect offset change
                CAST(NewInProgressDate AS DATETIME), -- detect offset change
                CAST(NewCompletedDate AS DATETIME), -- detect offset change
                CAST(NewAuthorizedDate AS DATETIME), -- detect offset change
                CAST(NewStateChangeDate AS DATETIME), -- detect offset change
                NewDateSK,
                NewCreatedDateSK,
                NewActivatedDateSK,
                NewClosedDateSK,
                NewResolvedDateSK,
                NewInProgressDateSK,
                NewCompletedDateSK,
                NewStateChangeDateSK
                )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount += @@ROWCOUNT

    -------------------------------------------------------------------------------
    -- Now do custom tables
    -------------------------------------------------------------------------------
    DECLARE @CustomTables TABLE
    (
        ModelTableName NVARCHAR(50)
    )

    INSERT INTO @CustomTables
    SELECT DISTINCT ModelTableName
    FROM    AnalyticsInternal.tbl_ProcessField
    WHERE   PartitionId = @partitionId
            AND ModelTableName IS NOT NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    WHILE EXISTS(SELECT * FROM @CustomTables)
    BEGIN
        DECLARE @customTableName NVARCHAR(50)

        SELECT TOP(1) @customTableName = ModelTableName FROM @CustomTables

        DECLARE @newLine CHAR(2) =  CHAR(10) + CHAR(13)
        DECLARE @updateClause NVARCHAR(MAX) = ''
        DECLARE @columnsAsDateTime NVARCHAR(MAX) = ''
        DECLARE @columnsAtTZAsDateTime NVARCHAR(MAX) = ''

        SELECT  @updateClause += ', ' + QUOTENAME(ModelColumnName) + ' = ' + QUOTENAME(ModelColumnName) + ' AT TIME ZONE @timeZone' + @newLine,
                @columnsAsDateTime += ', CAST(' + QUOTENAME(ModelColumnName) + ' AS DATETIME)' + @newLine,
                @columnsAtTZAsDateTime += ', CAST(' + QUOTENAME(ModelColumnName) + ' AT TIME ZONE @timeZone AS DATETIME)' + @newLine
        FROM
        (
            SELECT  DISTINCT ModelColumnName
            FROM    AnalyticsInternal.tbl_ProcessField
            WHERE   PartitionId = @partitionId
                    AND ModelTableName = @customTableName
                    AND ModelColumnName IS NOT NULL
                    AND FieldType = 'DateTime'
        ) T
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        DECLARE @cmd NVARCHAR(MAX) = '
        UPDATE  t
        SET     AnalyticsUpdatedDate = @batchDt,
                AnalyticsBatchId = @batchId
                <UPDATE_CLUASE>
        FROM    [AnalyticsModel].[tbl_<TABLE_NAME>] t WITH (INDEX (CL_AnalyticsModel_tbl_<TABLE_NAME>))
        WHERE   PartitionId = @partitionId
                AND WorkItemId BETWEEN @subBatchWorkItemIdStart AND @subBatchWorkItemIdEnd
                AND NOT EXISTS (
                    SELECT  WorkItemRevisionSK
                            <COLUMNS_AS_DATETIME>
                    INTERSECT
                    SELECT  WorkItemRevisionSK
                            <COLUMNS_AT_TZ_AS_DATETIME>
                            )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @updatedCount = @@ROWCOUNT
        '

        SET @cmd = REPLACE(@cmd, '<UPDATE_CLUASE>', @updateClause)
        SET @cmd = REPLACE(@cmd, '<TABLE_NAME>', @customTableName)
        SET @cmd = REPLACE(@cmd, '<COLUMNS_AS_DATETIME>', @columnsAsDateTime)
        SET @cmd = REPLACE(@cmd, '<COLUMNS_AT_TZ_AS_DATETIME>', @columnsAtTZAsDateTime)

        DECLARE @updatedStep INT

        EXEC sp_executesql @cmd,
            N'@partitionId INT, @batchId INT, @batchDt DATETIME2, @subBatchWorkItemIdStart INT, @subBatchWorkItemIdEnd INT, @timeZone NVARCHAR(128), @updatedCount INT OUTPUT',
            @partitionId = @partitionId, @batchId = @batchId, @batchDt = @batchDt, @subBatchWorkItemIdStart = @subBatchWorkItemIdStart, @subBatchWorkItemIdEnd = @subBatchWorkItemIdEnd, @timeZone = @timeZone, @updatedCount = @updatedStep OUTPUT

        SET @updatedCount = IIF(@updatedCount = 0, @updatedStep, @updatedCount)

        DELETE FROM @CustomTables WHERE @customTableName = ModelTableName
    END

    RETURN 0
END

GO

