/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 4A1549051B17F6DE800C06494C8F340A7004B8A2
CREATE PROCEDURE AnalyticsInternal.prc_iCreateStageBatch
    @partitionId    INT,
    @tableName VARCHAR(64),
    @providerShardId INT,
    @streamId        INT,
    @operation VARCHAR(10),
    @operationSproc VARCHAR(100),
    @batchDt DATETIME,
    @newBatchId BIGINT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT

    EXEC @status = AnalyticsInternal.prc_iCreateBatch
        @partitionId,
        @tableName,
        @providerShardId,
        @streamId,
        @operation,
        @operationSproc,
        NULL, -- OperationSprosVersion
        NULL, -- OperationPriority
        @operationTriggerTableName = NULL,
        @operationTriggerBatchIdStart = NULL,
        @operationTriggerBatchIdEnd = NULL,
        @batchDt = @batchDt,
        @newBatchId = @newBatchId OUTPUT

    RETURN @status
END

GO

