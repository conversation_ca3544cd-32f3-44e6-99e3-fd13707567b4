/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 2D57EDEF1DBA5902B58C746E819D9FC49C0A1BC1
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemLink_WorkItemLinkHistory_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    SET @insertedCount = 0
    SET @updatedCount = 0

    DELETE  TOP (@batchSizeMax) t
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory t
    JOIN    AnalyticsStage.tbl_WorkItemLink_Deleted d
    ON      d.PartitionId = t.PartitionId
            AND d.SourceId = t.SourceWorkItemId
            AND d.TargetId = t.TargetWorkItemId
            AND d.LinkTypeId = t.LinkTypeId
            AND d.ChangedDate = t.CreatedDate
            AND ISNULL(d.IsActive, 1) = 1
    LEFT JOIN AnalyticsStage.tbl_WorkItemLink s
    ON      s.PartitionId = t.PartitionId
            AND s.SourceId = t.SourceWorkItemId
            AND s.TargetId = t.TargetWorkItemId
            AND s.LinkTypeId = t.LinkTypeId
            AND s.ChangedDate = t.CreatedDate
            AND ISNULL(s.IsActive, 1) = 1
    WHERE   t.PartitionId = @partitionId
            AND t.LinkTypeId > 0
            AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND s.LinkTypeId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    DELETE  TOP (@batchSizeMax) t
    FROM    AnalyticsModel.tbl_WorkItemLinkHistory t
    JOIN    AnalyticsStage.tbl_WorkItemLink_Deleted d
    ON      d.PartitionId = t.PartitionId
            AND d.SourceId = t.TargetWorkItemId
            AND d.TargetId = t.SourceWorkItemId
            AND 0 - d.LinkTypeId = t.LinkTypeId
            AND d.ChangedDate = t.CreatedDate
            AND ISNULL(d.IsActive, 1) = 1
    LEFT JOIN AnalyticsStage.tbl_WorkItemLink s
    ON      s.PartitionId = t.PartitionId
            AND s.SourceId = t.TargetWorkItemId
            AND s.TargetId = t.SourceWorkItemId
            AND 0 - s.LinkTypeId = t.LinkTypeId
            AND s.ChangedDate = t.CreatedDate
            AND ISNULL(s.IsActive, 1) = 1
    WHERE   t.PartitionId = @partitionId
            AND t.LinkTypeId < 0
            AND d.AnalyticsBatchIdDeleted BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND s.LinkTypeId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount += @@ROWCOUNT

    SET @complete = IIF(@deletedCount < @batchSizeMax, 1, 0)

    RETURN 0
END

GO

