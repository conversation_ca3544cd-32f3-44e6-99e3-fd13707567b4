/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 70FBA49419207BB861D1E2D6A7AEEE6499257387
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemDestroyed_StageWorkItemRevision_BatchDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 1000)

    CREATE TABLE #DeletedWorkItem (WorkItemId INT)

    INSERT  #DeletedWorkItem
    SELECT  TOP (@batchSizeMax) WorkItemId
    FROM    AnalyticsStage.tbl_WorkItemDestroyed d
    WHERE   PartitionId = @partitionId
            AND d.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND d.WorkItemId > ISNULL(@stateData, -1)
    ORDER BY WorkItemId ASC
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@@ROWCOUNT < @batchSizeMax, 1, 0)
    SET @endStateData = (SELECT MAX(WorkItemId) FROM #DeletedWorkItem)

    -- Delete revisions that is associated with destroyed work items in the batch range
    DECLARE @deleted AnalyticsStage.typ_WorkItemRevisionKey2

    DELETE  r
    OUTPUT  DELETED.AnalyticsProviderShardId, DELETED.System_Id, DELETED.System_Rev INTO @deleted
    FROM    AnalyticsStage.tbl_WorkItemRevision r
    JOIN    #DeletedWorkItem d
    ON      r.System_Id = d.WorkItemId
    WHERE   r.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    INSERT AnalyticsStage.tbl_WorkItemRevision_Deleted
    (
        PartitionId,
        AnalyticsProviderShardId,
        AnalyticsBatchIdDeleted,
        AnalyticsDeletedDate,
        System_Id,
        System_Rev
    )
    SELECT  @partitionId,
            AnalyticsProviderShardId,
            @batchId,
            @batchDt,
            System_Id,
            System_Rev
    FROM    @deleted

    -- Delete work items from extended table
    DELETE  r
    FROM    AnalyticsStage.tbl_WorkItemRevisionExtended r
    JOIN    #DeletedWorkItem d
    ON      r.System_Id = d.WorkItemId
    WHERE   r.PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    RETURN 0
END

GO

