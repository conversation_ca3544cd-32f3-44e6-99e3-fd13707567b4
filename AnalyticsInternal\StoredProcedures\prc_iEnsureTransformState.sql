/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: F3E21ABBABC0462B53214AEF34D26C40989E4E98
--------------------------------------------------------------------
-- Maintains the correct rows in TransformState table
-- Adds and removes rows in reaction to changes to TransformDefintion table
--------------------------------------------------------------------
CREATE PROCEDURE AnalyticsInternal.prc_iEnsureTransformState
    @partitionId INT,
    @definitionsOnly BIT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @definitionsChanged INT = 0
    DECLARE @legacySprocNamePrefix VARCHAR(100) = 'AnalyticsInternal.prc_iiTrans_'

    -- check for newer transform defintions
    IF (SELECT ISNULL(MAX(TransformDefInstallDate), '1900-01-01') FROM AnalyticsInternal.tbl_TransformState WHERE PartitionId = @partitionId) < (SELECT MAX(InstallDate) FROM AnalyticsInternal.tbl_TransformDefinition)
    BEGIN
        SET @definitionsChanged = 1
    END

    IF (@definitionsChanged = 1 OR @definitionsOnly = 0)
    BEGIN
        -- this MERGE refreshes the TransformStates to match the combinations of transform definitions and current streams
        ;WITH Src AS
        (
            SELECT  @partitionId AS PartitionId,
                    d.TriggerTableName,
                    d.TriggerOperation,
                    d.TargetTableName,
                    d.TargetOperation,
                    d.SProcName,
                    d.SprocVersion,
                    d.OperationScope,
                    d.TransformOrder,
                    d.TransformPriority,
                    d.IntervalMinutes,
                    d.InstallDate AS TransformDefInstallDate,
                    p.TriggerBatchIdMin,
                    p.TriggerBatchIdMax,
                    p.DoingBatchIdEnd AS DoingBatchIdEnd,
                    p.DoneBatchIdEnd AS DoneBatchIdEnd,
                    ISNULL(p.DoingBatchCreateDateTime, GETUTCDATE()) AS DoingBatchCreateDateTime, -- initialize to now to avoid initial load of timed work
                    IIF((d.SProcName = p.SprocName OR CONCAT(@legacySprocNamePrefix, d.SProcName) = p.SprocName) AND d.SprocVersion = p.SprocVersion,
                         p.ReTriggerBatchIdMax,
                         p.DoingBatchIdEnd) AS ReTriggerBatchIdMax, -- reprocess on sproc version change up to where incremental left off
                    IIF((d.SProcName = p.SprocName OR CONCAT(@legacySprocNamePrefix, d.SProcName) = p.SprocName) AND d.SprocVersion = p.SprocVersion,
                         p.ReDoingBatchIdEnd,
                         NULL) AS ReDoingBatchIdEnd, -- ignore any previous reprocess work, do it again
                    p.ReDoneBatchIdEnd AS ReDoneBatchIdEnd,
                    p.LoadedBatchIdMax,
                    ISNULL(p.Hold, d.Hold) AS Hold,
                    ISNULL(p.HoldReason, d.HoldReason) AS HoldReason,
                    ISNULL(p.HoldChangedTime, d.HoldChangedTime) AS HoldChangedTime
            FROM    AnalyticsInternal.tbl_TransformDefinition d WITH (READCOMMITTEDLOCK)
            LEFT JOIN AnalyticsInternal.tbl_TransformState p WITH (READCOMMITTEDLOCK, ROWLOCK)
            ON      p.TriggerTableName = d.TriggerTableName
                    AND p.TriggerOperation = d.TriggerOperation
                    AND p.TargetTableName = d.TargetTableName
                    AND p.TargetOperation = d.TargetOperation
                    AND p.SProcName = d.SProcName
                    AND p.PartitionId = @partitionId
        )
        , Tgt AS
        (
            SELECT  *
            FROM    AnalyticsInternal.tbl_TransformState WITH (HOLDLOCK, ROWLOCK)
            WHERE   PartitionId = @partitionId
        )
        MERGE   Tgt AS t
        USING   Src AS s
        ON      t.PartitionId = s.PartitionId
                AND t.TriggerTableName = s.TriggerTableName
                AND t.TriggerOperation = s.TriggerOperation
                AND t.TargetTableName = s.TargetTableName
                AND t.TargetOperation = s.TargetOperation
                AND t.SProcName = s.SProcName
        WHEN MATCHED AND NOT EXISTS(
            SELECT  s.SprocVersion,
                    s.OperationScope,
                    s.TransformOrder,
                    s.TransformPriority,
                    s.IntervalMinutes,
                    s.TransformDefInstallDate,
                    s.TriggerBatchIdMin,
                    s.TriggerBatchIdMax,
                    s.DoingBatchIdEnd,
                    s.DoneBatchIdEnd,
                    s.DoingBatchCreateDateTime,
                    s.ReTriggerBatchIdMax,
                    s.ReDoingBatchIdEnd,
                    s.ReDoneBatchIdEnd,
                    s.LoadedBatchIdMax,
                    s.Hold,
                    s.HoldReason,
                    s.HoldChangedTime
            INTERSECT
            SELECT  t.SprocVersion,
                    t.OperationScope,
                    t.TransformOrder,
                    t.TransformPriority,
                    t.IntervalMinutes,
                    t.TransformDefInstallDate,
                    t.TriggerBatchIdMin,
                    t.TriggerBatchIdMax,
                    t.DoingBatchIdEnd,
                    t.DoneBatchIdEnd,
                    t.DoingBatchCreateDateTime,
                    t.ReTriggerBatchIdMax,
                    t.ReDoingBatchIdEnd,
                    t.ReDoneBatchIdEnd,
                    t.LoadedBatchIdMax,
                    t.Hold,
                    t.HoldReason,
                    t.HoldChangedTime
            )
        THEN UPDATE
        SET         t.SprocVersion             = s.SprocVersion,
                    t.OperationScope           = s.OperationScope,
                    t.TransformOrder           = s.TransformOrder,
                    t.TransformPriority        = s.TransformPriority,
                    t.IntervalMinutes          = s.IntervalMinutes,
                    t.TransformDefInstallDate  = s.TransformDefInstallDate,
                    --t.TriggerBatchIdMin        = s.TriggerBatchIdMin, -- there is no scenario where this value will be changed on udpate
                    --t.TriggerBatchIdMax        = s.TriggerBatchIdMax, -- there is no scenario where this value will be changed on udpate
                    --t.DoingBatchIdEnd          = s.DoingBatchIdEnd, -- there is no scenario where this value will be changed on udpate
                    --t.DoneBatchIdEnd           = s.DoneBatchIdEnd, -- there is no scenario where this value will be changed on udpate
                    t.DoingBatchCreateDateTime = s.DoingBatchCreateDateTime,
                    t.ReTriggerBatchIdMax      = s.ReTriggerBatchIdMax,
                    t.ReDoingBatchIdEnd        = s.ReDoingBatchIdEnd,
                    t.ReDoneBatchIdEnd         = s.ReDoneBatchIdEnd,
                    t.LoadedBatchIdMax         = s.LoadedBatchIdMax
                    --t.Hold                     = s.Hold, -- there is no scenario where this value will be changed on udpate
                    --t.HoldReason               = s.HoldReason, -- there is no scenario where this value will be changed on udpate
                    --t.HoldChangedTime          = s.HoldChangedTime -- there is no scenario where this value will be changed on udpate
        WHEN NOT MATCHED BY TARGET THEN
        INSERT  (
                PartitionId,
                TriggerTableName,
                TriggerOperation,
                TargetTableName,
                TargetOperation,
                SProcName,
                SprocVersion,
                OperationScope,
                TransformOrder,
                TransformPriority,
                IntervalMinutes,
                TransformDefInstallDate,
                TriggerBatchIdMin,
                TriggerBatchIdMax,
                DoingBatchIdEnd,
                DoneBatchIdEnd,
                DoingBatchCreateDateTime,
                ReTriggerBatchIdMax,
                ReDoingBatchIdEnd,
                ReDoneBatchIdEnd,
                LoadedBatchIdMax,
                Hold,
                HoldReason,
                HoldChangedTime
                )
        VALUES  (
                s.PartitionId,
                s.TriggerTableName,
                s.TriggerOperation,
                s.TargetTableName,
                s.TargetOperation,
                s.SProcName,
                s.SprocVersion,
                s.OperationScope,
                s.TransformOrder,
                s.TransformPriority,
                s.IntervalMinutes,
                s.TransformDefInstallDate,
                s.TriggerBatchIdMin,
                s.TriggerBatchIdMax,
                s.DoingBatchIdEnd,
                s.DoneBatchIdEnd,
                s.DoingBatchCreateDateTime,
                s.ReTriggerBatchIdMax,
                s.ReDoingBatchIdEnd,
                s.ReDoneBatchIdEnd,
                s.LoadedBatchIdMax,
                s.Hold,
                s.HoldReason,
                s.HoldChangedTime
                )
        WHEN NOT MATCHED BY SOURCE THEN
        DELETE
        OPTION(OPTIMIZE FOR (@partitionId UNKNOWN));

        IF (@@ROWCOUNT > 0)
        BEGIN
            -- initialize trigger max for new definitions/streams that don't have it
            -- for late arriving transform definitions - not for stream updates
            -- necessary to make sure processing starts even without new data arriving

            -- first try grabbing the triggers from existing transform states
            UPDATE  t
            SET     t.TriggerBatchIdMin = 0,
                    t.TriggerBatchIdMax = p.TriggerBatchIdMax
            FROM    AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
            CROSS APPLY
            (
                SELECT  MAX(TriggerBatchIdMax) AS TriggerBatchIdMax
                FROM    AnalyticsInternal.tbl_TransformState p
                WHERE   p.PartitionId = @partitionId
                        AND p.TriggerTableName = t.TriggerTableName
                        AND p.TriggerOperation = t.TriggerOperation
            ) p
            WHERE   t.PartitionId = @partitionId
                    AND t.TriggerBatchIdMax IS NULL
                    AND p.TriggerBatchIdMax IS NOT NULL
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            -- now try grabbing the triggers from batches
            ;WITH NewTriggers AS
            (
                SELECT  t.PartitionId,
                        t.TriggerTableName,
                        t.TriggerOperation,
                        t.TargetTableName,
                        t.TargetOperation,
                        t.SProcName,
                        0 AS TriggerBatchIdMin,
                        MAX(tb.BatchId) AS TriggerBatchIdMax
                FROM    AnalyticsInternal.tbl_TransformState t
                JOIN    AnalyticsInternal.tbl_Batch tb WITH (READPAST)
                ON      tb.PartitionId = t.PartitionId
                        AND tb.TableName = t.TriggerTableName
                        AND (
                            (tb.Inserted = 1 AND t.TriggerOperation IN ('merge', 'insert', 'replace'))
                            OR
                            (tb.Updated = 1 AND t.TriggerOperation IN ('merge', 'update', 'replace'))
                            OR
                            (tb.Deleted = 1 AND t.TriggerOperation IN ('delete', 'replace'))
                            )
                WHERE   t.PartitionId = @partitionId
                        AND tb.Ready = 1
                        AND t.TriggerBatchIdMax IS NULL
                GROUP BY t.PartitionId,
                        t.TriggerTableName,
                        t.TriggerOperation,
                        t.TargetTableName,
                        t.TargetOperation,
                        t.SProcName
            )
            UPDATE  t
            SET     t.TriggerBatchIdMin = s.TriggerBatchIdMin,
                    t.TriggerBatchIdMax = s.TriggerBatchIdMax
            FROM    AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
            JOIN    NewTriggers s
            ON      t.PartitionId = s.PartitionId
                    AND t.TriggerTableName = s.TriggerTableName
                    AND t.TriggerOperation = s.TriggerOperation
                    AND t.TargetTableName = s.TargetTableName
                    AND t.TargetOperation = s.TargetOperation
                    AND t.SProcName = s.SProcName
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            -- initialize rework trigger max for new definitions that don't have it
            ;WITH NewReworkTriggers AS
            (
                SELECT  t.PartitionId,
                        t.TriggerTableName,
                        t.TriggerOperation,
                        t.TargetTableName,
                        t.TargetOperation,
                        t.SProcName,
                        MAX(tb.BatchId) AS ReTriggerBatchIdMax
                FROM    AnalyticsInternal.tbl_TransformState t
                JOIN    AnalyticsInternal.tbl_Batch tb WITH (READPAST)
                ON      tb.PartitionId = t.PartitionId
                        AND tb.TableName = t.TriggerTableName
                        AND tb.Invalidated = 1
                WHERE   t.PartitionId = @partitionId
                        AND tb.Ready = 1
                        AND t.ReTriggerBatchIdMax IS NULL
                GROUP BY t.PartitionId,
                        t.TriggerTableName,
                        t.TriggerOperation,
                        t.TargetTableName,
                        t.TargetOperation,
                        t.SProcName
            )
            UPDATE  t
            SET     t.ReTriggerBatchIdMax = s.ReTriggerBatchIdMax
            FROM    AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
            JOIN    NewReworkTriggers s
            ON      t.PartitionId = s.PartitionId
                    AND t.TriggerTableName = s.TriggerTableName
                    AND t.TriggerOperation = s.TriggerOperation
                    AND t.TargetTableName = s.TargetTableName
                    AND t.TargetOperation = s.TargetOperation
                    AND t.SProcName = s.SProcName
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            -- bubble down any rework resets to downstream transforms
            UPDATE  t
            SET     t.ReTriggerBatchIdMax = tpred.ReTriggerBatchIdMax
            FROM    AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
            JOIN    AnalyticsInternal.tbl_TransformState tpred
            ON      t.PartitionId = tpred.PartitionId
                    AND t.TriggerTableName = tpred.TriggerTableName
                    AND t.TransformOrder > tpred.TransformOrder
            WHERE   t.PartitionId = @partitionId
                    AND t.OperationScope = 'batch'
                    AND tpred.OperationScope = 'batch'
                    AND ISNULL(t.ReTriggerBatchIdMax, 0) < tpred.ReTriggerBatchIdMax
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

            -- update missing states based on already batched work (unlikely in most scenarios - necessary for M102 upgrade)
            ;WITH NewBatched AS
            (
                SELECT  t.PartitionId,
                        t.TriggerTableName,
                        t.TriggerOperation,
                        t.TargetTableName,
                        t.TargetOperation,
                        t.SProcName,
                        MAX(ob.OperationTriggerBatchIdEnd) AS DoingBatchIdEnd,
                        MAX(IIF(ob.OperationTriggerBatchIdStart = 0, ob.OperationTriggerBatchIdEnd, NULL)) AS ReDoingBatchIdEnd,
                        MAX(ob.CreateDateTime) AS DoingBatchCreateDateTime
                FROM    AnalyticsInternal.tbl_TransformState t
                INNER HASH JOIN AnalyticsInternal.tbl_Batch ob WITH (READPAST)
                ON      ob.PartitionId = t.PartitionId
                        AND ob.OperationTriggerTableName = t.TriggerTableName
                        AND ob.TableName = t.TargetTableName
                        AND ob.Operation = t.TargetOperation
                        AND (ob.OperationSproc = t.SprocName OR ob.OperationSproc = CONCAT(@legacySprocNamePrefix, t.SprocName))
                        AND ob.OperationSprocVersion = t.SprocVersion
                        AND ob.BatchId > ISNULL(IIF(t.DoingBatchIdEnd < t.ReDoingBatchIdEnd, t.DoingBatchIdEnd, ReDoingBatchIdEnd), 0)
                WHERE   t.PartitionId = @partitionId
                        AND (t.DoingBatchIdEnd IS NULL OR t.ReDoingBatchIdEnd IS NULL)
                GROUP BY t.PartitionId,
                        t.TriggerTableName,
                        t.TriggerOperation,
                        t.TargetTableName,
                        t.TargetOperation,
                        t.SProcName
            )
            UPDATE  t
            SET     t.DoingBatchIdEnd = ISNULL(s.DoingBatchIdEnd, t.DoingBatchIdEnd),
                    t.ReDoingBatchIdEnd = ISNULL(s.ReDoingBatchIdEnd, t.ReDoingBatchIdEnd),
                    t.DoingBatchCreateDateTime = ISNULL(s.DoingBatchCreateDateTime, t.DoingBatchCreateDateTime)
            FROM    NewBatched s
            JOIN    AnalyticsInternal.tbl_TransformState t
            ON      t.PartitionId = s.PartitionId
                    AND t.TriggerTableName = s.TriggerTableName
                    AND t.TriggerOperation = s.TriggerOperation
                    AND t.TargetTableName = s.TargetTableName
                    AND t.TargetOperation = s.TargetOperation
                    AND t.SProcName = s.SProcName
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER)

        END

    END

END

GO

