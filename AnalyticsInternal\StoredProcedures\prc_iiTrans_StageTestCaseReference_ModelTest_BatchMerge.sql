/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 65EEB78075B61DC69DB904CF109B40E086E87E9E
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestCaseReference_ModelTest_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    CREATE TABLE #Test
    (
        TestCaseReferenceId             INT                 NOT NULL,
        DataSourceId                    INT                 NOT NULL,
        TestName                        NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        TestOwner                       NVARCHAR(256)       COLLATE DATABASE_DEFAULT NULL,
        ContainerName                   NVARCHAR(512)       COLLATE DATABASE_DEFAULT NULL,
        Priority                        INT                 NULL,
        ProjectSK                       UNIQUEIDENTIFIER    NULL,
        FullyQualifiedTestName          NVARCHAR(512)       COLLATE DATABASE_DEFAULT NULL
    )

    IF (@triggerBatchIdStart > 1 AND @stateData IS NULL AND @attemptCount = 0) -- optimzed for small batches
    BEGIN
        INSERT  #Test
                (
                TestCaseReferenceId,
                DataSourceId,
                TestName,
                TestOwner,
                ContainerName,
                Priority,
                ProjectSK,
                FullyQualifiedTestName
                )
        SELECT  TOP (@batchSizeMax) WITH TIES
                TestCaseReferenceId,
                DataSourceId,
                TestCaseTitle,
                Owner,
                AutomatedTestStorage,
                Priority,
                ProjectGuid,
                AutomatedTestName
        FROM    AnalyticsStage.tbl_TestCaseReference WITH (INDEX (IX_tbl_TestCaseReference_AxBatchId))
        WHERE   PartitionId = @partitionId
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        ORDER BY TestCaseReferenceId ASC
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    END
    ELSE
    BEGIN
        -- this opimization assumes tests arrive with roughly sequential ref ids
        CREATE TABLE #ImpactedTests
        (
            TestCaseReferenceId INT NOT NULL
        )

        -- getting TestCaseReferenceId alone from clustered index is much faster
        INSERT  #ImpactedTests
        SELECT  TOP (@batchSizeMax)
                TestCaseReferenceId
        FROM    AnalyticsStage.tbl_TestCaseReference WITH (INDEX (CI_tbl_TestCaseReference))
        WHERE   PartitionId = @partitionId
                AND TestCaseReferenceId > ISNULL(@stateData, -1)
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        GROUP BY TestCaseReferenceId
        ORDER BY TestCaseReferenceId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END

        INSERT  #Test
                (
                TestCaseReferenceId,
                DataSourceId,
                TestName,
                TestOwner,
                ContainerName,
                Priority,
                ProjectSK,
                FullyQualifiedTestName
                )
        SELECT  r.TestCaseReferenceId,
                DataSourceId,
                TestCaseTitle,
                Owner,
                AutomatedTestStorage,
                Priority,
                ProjectGuid,
                AutomatedTestName
        FROM    #ImpactedTests ir
        INNER LOOP JOIN AnalyticsStage.tbl_TestCaseReference r WITH (INDEX (CI_tbl_TestCaseReference))
        ON      r.TestCaseReferenceId = ir.TestCaseReferenceId
        WHERE   r.PartitionId = @partitionId
                AND r.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        DROP TABLE #ImpactedTests
    END

    SET @endStateData = (SELECT MAX(TestCaseReferenceId) FROM #Test)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    MERGE AnalyticsModel.tbl_Test AS t
    USING #Test AS s
    ON (t.PartitionId = @partitionId AND t.TestCaseReferenceId = s.TestCaseReferenceId AND t.DataSourceId = s.DataSourceId)
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.TestName,
        s.TestOwner,
        s.ContainerName,
        s.Priority,
        s.ProjectSK,
        s.FullyQualifiedTestName
        INTERSECT
        SELECT
        t.TestName,
        t.TestOwner,
        t.ContainerName,
        t.Priority,
        t.ProjectSK,
        t.FullyQualifiedTestName
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt,
        AnalyticsBatchId = @batchId,
        t.TestName = s.TestName,
        t.TestOwner = s.TestOwner,
        t.ContainerName = s.ContainerName,
        t.Priority = s.Priority,
        t.ProjectSK = s.ProjectSK,
        t.FullyQualifiedTestName = s.FullyQualifiedTestName
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId,
        AnalyticsBatchId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        TestCaseReferenceId,
        DataSourceId,
        TestName,
        TestOwner,
        ContainerName,
        Priority,
        ProjectSK,
        FullyQualifiedTestName
    )
    VALUES (
        @partitionId,
        @batchId,
        @batchDt,
        @batchDt,
        s.TestCaseReferenceId,
        s.DataSourceId,
        s.TestName,
        s.TestOwner,
        s.ContainerName,
        s.Priority,
        s.ProjectSK,
        s.FullyQualifiedTestName
    )
    OUTPUT $action INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), LOOP JOIN);

    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    DROP TABLE #Test

    RETURN 0
END

GO

