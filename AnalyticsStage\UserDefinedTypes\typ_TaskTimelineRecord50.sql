CREATE TYPE [AnalyticsStage].[typ_TaskTimelineRecord50] AS TABLE (
    [ProjectGuid]               UNIQUEIDENTIFIER   NOT NULL,
    [PipelineType]              NVARCHAR (260)     NOT NULL,
    [TimelineRecordGuid]        UNIQUEIDENTIFIER   NOT NULL,
    [ParentRecordGuid]          UNIQUEIDENTIFIER   NULL,
    [TimelineId]                INT                NULL,
    [PlanId]                    INT                NULL,
    [Type]                      NVARCHAR (400)     NULL,
    [Name]                      NVARCHAR (400)     NULL,
    [RefName]                   NVARCHAR (400)     NULL,
    [ChangeId]                  INT                NULL,
    [Order]                     INT                NULL,
    [ResultCode]                NVARCHAR (400)     NULL,
    [WorkerName]                NVARCHAR (400)     NULL,
    [LogPath]                   NVARCHAR (400)     NULL,
    [LogLineCount]              INT                NULL,
    [StartTime]                 DATETIMEOFFSET (7) NULL,
    [FinishTime]                DATETIMEOFFSET (7) NULL,
    [PlanFinishTime]            DATETIMEOFFSET (7) NULL,
    [State]                     INT                NULL,
    [Result]                    INT                NULL,
    [TaskDefinitionReferenceId] INT                NULL,
    [Identifier]                NVARCHAR (256)     NULL,
    [Attempt]                   INT                NULL,
    [IsJobFailureDueToTask]     BIT                NULL,
    [StageIdentifier]           NVARCHAR (256)     NULL,
    [StageName]                 NVARCHAR (400)     NULL,
    [PhaseIdentifier]           NVARCHAR (256)     NULL,
    [PhaseName]                 NVARCHAR (400)     NULL,
    [JobIdentifier]             NVARCHAR (256)     NULL,
    [JobName]                   NVARCHAR (400)     NULL);


GO

GRANT EXECUTE
    ON TYPE::[AnalyticsStage].[typ_TaskTimelineRecord50] TO [VSODIAGROLE];


GO

