/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: E905E3320E5ACC426CE4F318C1C0D4D8BD9D49D6
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_ModelWorkItemLinkHistory_ModelWorkItem_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #TriggerWorkItemId
    (
         WorkItemId INT NOT NULL PRIMARY KEY,
         MinRevisedDate DATETIMEOFFSET NOT NULL
    )

    IF (@triggerBatchIdEnd - @triggerBatchIdStart < 1000)
    BEGIN
        INSERT  #TriggerWorkItemId
        SELECT  TOP (@batchSizeMax)
                TargetWorkItemId AS WorkItemId,
                MIN(CreatedDate) AS MinRevisedDate
        FROM    AnalyticsModel.tbl_WorkItemLinkHistory WITH (INDEX (IX_tbl_WorkItemLinkHistory_AxBatchId))
        WHERE   PartitionId = @partitionId
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND TargetWorkItemId >= ISNULL(@stateData + 1, 0)
                AND LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
        GROUP BY TargetWorkItemId
        ORDER BY TargetWorkItemId ASC
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END
    ELSE
    BEGIN
        INSERT  #TriggerWorkItemId
        SELECT  TOP (@batchSizeMax)
                TargetWorkItemId AS WorkItemId,
                MIN(CreatedDate) AS MinRevisedDate
        FROM    AnalyticsModel.tbl_WorkItemLinkHistory WITH (INDEX (IX_tbl_WorkItemLinkHistory_TargetWorkItemIdDeletedDate))
        WHERE   PartitionId = @partitionId
                AND AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                AND TargetWorkItemId >= ISNULL(@stateData + 1, 0)
                AND LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
        GROUP BY TargetWorkItemId
        ORDER BY TargetWorkItemId ASC
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));
    END

    DECLARE @updatedLinkCount INT = @@ROWCOUNT

    BEGIN TRAN

    IF (@updatedLinkCount > 10)
    BEGIN
        UPDATE  r
        SET     AnalyticsUpdatedDate = @batchDt,
                AnalyticsBatchId = @batchId,
                ParentWorkItemId = l.SourceWorkItemId
        FROM    #TriggerWorkItemId AS t
        INNER HASH JOIN AnalyticsModel.tbl_WorkItemHistory AS r WITH(INDEX(CL_AnalyticsModel_tbl_WorkItemHistory))
        ON      t.WorkItemId = r.WorkItemId
                AND t.MinRevisedDate < r.RevisedDate
        LEFT JOIN AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = r.PartitionId
                AND l.TargetWorkItemId = r.WorkItemId
                -- This predicate assumes that there exists at most one hierarchy link at any point in time.
                AND r.RevisedDate > l.CreatedDate
                AND r.RevisedDate <= l.DeletedDate
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
        WHERE   r.PartitionId = @partitionId
                AND NOT EXISTS
                (
                    SELECT  r.ParentWorkItemId
                    INTERSECT
                    SELECT  l.SourceWorkItemId
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        SET @updatedCount = @@ROWCOUNT

        UPDATE  r
        SET     AnalyticsUpdatedDate = @batchDt,
                AnalyticsBatchId = @batchId,
                ParentWorkItemId = l.SourceWorkItemId
        FROM    #TriggerWorkItemId AS t
        INNER HASH JOIN AnalyticsModel.tbl_WorkItem AS r WITH(INDEX(CL_AnalyticsModel_tbl_WorkItem))
        ON      t.WorkItemId = r.WorkItemId
        LEFT JOIN AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = r.PartitionId
                AND l.TargetWorkItemId = r.WorkItemId
                -- This predicate assumes that there exists at most one hierarchy link at any point in time.
                AND l.DeletedDate = '9999-01-01'
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
        WHERE   r.PartitionId = @partitionId
                AND NOT EXISTS
                (
                    SELECT  r.ParentWorkItemId
                    INTERSECT
                    SELECT  l.SourceWorkItemId
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        SET @updatedCount += @@ROWCOUNT
    END
    ELSE
    BEGIN
        UPDATE  r
        SET     AnalyticsUpdatedDate = @batchDt,
                AnalyticsBatchId = @batchId,
                ParentWorkItemId = l.SourceWorkItemId
        FROM    #TriggerWorkItemId AS t
        JOIN    AnalyticsModel.tbl_WorkItemHistory AS r WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemIdRev)) -- allow use of row lock on NCI vs CCI segments
        ON      t.WorkItemId = r.WorkItemId
                AND t.MinRevisedDate < r.RevisedDate
        LEFT JOIN AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = r.PartitionId
                AND l.TargetWorkItemId = r.WorkItemId
                -- This predicate assumes that there exists at most one hierarchy link at any point in time.
                AND r.RevisedDate > l.CreatedDate
                AND r.RevisedDate <= l.DeletedDate
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
        WHERE   r.PartitionId = @partitionId
                AND NOT EXISTS
                (
                    SELECT  r.ParentWorkItemId
                    INTERSECT
                    SELECT  l.SourceWorkItemId
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        SET @updatedCount = @@ROWCOUNT

        UPDATE  r
        SET     AnalyticsUpdatedDate = @batchDt,
                AnalyticsBatchId = @batchId,
                ParentWorkItemId = l.SourceWorkItemId
        FROM    #TriggerWorkItemId AS t
        JOIN    AnalyticsModel.tbl_WorkItem AS r WITH (INDEX (IX_tbl_WorkItem_WorkItemIdRev)) -- allow use of row lock on NCI vs CCI segments
        ON      t.WorkItemId = r.WorkItemId
        LEFT JOIN AnalyticsModel.tbl_WorkItemLinkHistory AS l
        ON      l.PartitionId = r.PartitionId
                AND l.TargetWorkItemId = r.WorkItemId
                -- This predicate assumes that there exists at most one hierarchy link at any point in time.
                AND l.DeletedDate = '9999-01-01'
                AND l.LinkTypeReferenceName = 'System.LinkTypes.Hierarchy-Forward'
        WHERE   r.PartitionId = @partitionId
                AND NOT EXISTS
                (
                    SELECT  r.ParentWorkItemId
                    INTERSECT
                    SELECT  l.SourceWorkItemId
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

        SET @updatedCount += @@ROWCOUNT
    END

    COMMIT TRAN

    SET @insertedCount = 0
    SET @deletedCount = 0

    SET @endStateData = (SELECT MAX(WorkItemId) FROM #TriggerWorkItemId)
    SET @complete = IIF((SELECT COUNT(*) FROM #TriggerWorkItemId) = @batchSizeMax, 0, 1)

    DROP TABLE #TriggerWorkItemId

    RETURN 0
END

GO

