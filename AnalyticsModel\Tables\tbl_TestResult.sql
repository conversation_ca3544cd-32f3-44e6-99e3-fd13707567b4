CREATE TABLE [AnalyticsModel].[tbl_TestResult] (
    [PartitionId]          INT                NOT NULL,
    [AnalyticsCreatedDate] DATETIME           NOT NULL,
    [AnalyticsUpdatedDate] DATETIME           NOT NULL,
    [AnalyticsBatchId]     BIGINT             NOT NULL,
    [TestResultSK]         BIGINT             IDENTITY (1, 1) NOT NULL,
    [TestRunId]            INT                NOT NULL,
    [TestResultId]         INT                NOT NULL,
    [ProjectSK]            UNIQUEIDENTIFIER   NULL,
    [TestRunSK]            INT                NOT NULL,
    [TestSK]               INT                NULL,
    [ReleaseSK]            INT                NULL,
    [ReleaseEnvironmentSK] INT                NULL,
    [BuildSK]              INT                NULL,
    [BranchSK]             INT                NULL,
    [Outcome]              TINYINT            NULL,
    [StartedDate]          DATETIMEOFFSET (0) NULL,
    [StartedDateSK]        INT                NULL,
    [CompletedDate]        DATETIMEOFFSET (0) NULL,
    [CompletedDateSK]      INT                NULL,
    [DurationSeconds]      DECIMAL (18, 3)    NULL,
    [Workflow]             TINYINT            NULL,
    [TestRunType]          TINYINT            NULL,
    [DataSourceId]         TINYINT            NOT NULL,
    [ReleasePipelineSK]    INT                NULL,
    [ReleaseStageSK]       INT                NULL,
    [BuildPipelineSK]      INT                NULL
) ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_TestResult_TestResultId]
    ON [AnalyticsModel].[tbl_TestResult]([PartitionId] ASC, [TestRunId] ASC, [TestResultId] ASC, [DataSourceId] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_TestResult]
    ON [AnalyticsModel].[tbl_TestResult]
    ON [scheme_AnalyticsTest] ([PartitionId]);


GO

