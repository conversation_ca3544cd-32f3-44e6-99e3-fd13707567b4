/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D11BB69E4C62806F2A6B6398CF849FCBC342BC81
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageWorkItemRevision_ModelTestPoint_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #ChangedWorkItems
    (
        WorkItemId   INT NOT NULL PRIMARY KEY,
        Revision     INT NOT NULL
    )

    -- First get all the changed work item revisions along with their types
    -- Here we are sub-batching just on work item IDs and not (WorkItemId, Revision) touple. Hence adding WITH TIES too.
    INSERT #ChangedWorkItems (WorkItemId, Revision)
    SELECT s.System_Id, MAX(s.System_Rev)
    FROM (
             SELECT TOP (@batchSizeMax) WITH TIES wir.System_Id, wir.System_Rev
             FROM AnalyticsStage.tbl_WorkItemRevision wir WITH (INDEX (IX_tbl_WorkItemRevision_AxBatchIdChanged), FORCESEEK)
             WHERE wir.PartitionId = @partitionId
                   AND wir.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                   AND wir.System_Id > ISNULL(@stateData, 0)
             ORDER BY wir.System_Id
         ) s
    GROUP BY s.System_Id
    OPTION   (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete     = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT TOP 1 WorkItemId FROM #ChangedWorkItems ORDER BY WorkItemId DESC)

    -- Update test point details
    UPDATE t
    SET    AnalyticsUpdatedDate      = @batchDt,
           AnalyticsBatchId          = @batchId,
           AssignedToUserSK          = AnalyticsInternal.func_GetUserSKFromWITPerson(wir.System_AssignedTo),
           Priority                  = wir.Microsoft_VSTS_Common_Priority,
           AutomationStatus          = wir.Microsoft_VSTS_TCM_AutomationStatus,
           HasWorkItemProperties     = 1
    FROM   #ChangedWorkItems s
    INNER LOOP JOIN AnalyticsModel.tbl_TestPoint t WITH (INDEX (IX_tbl_TestPoint_TestCaseId))
    ON     t.PartitionId        = @partitionId
           AND t.TestCaseId     = s.WorkItemId
    INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision wir WITH (INDEX (CI_tbl_WorkItemRevision))
    ON     wir.PartitionId     = @partitionId
           AND wir.System_Id   = s.WorkItemId
           AND wir.System_Rev  = s.Revision
    WHERE  NOT EXISTS
           (
               SELECT
               t.AssignedToUserSK,
               t.Priority,
               t.AutomationStatus,
               t.HasWorkItemProperties
               INTERSECT
               SELECT
               AnalyticsInternal.func_GetUserSKFromWITPerson(wir.System_AssignedTo),
               wir.Microsoft_VSTS_Common_Priority,
               wir.Microsoft_VSTS_TCM_AutomationStatus,
               1
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN), FORCE ORDER);

    SET @updatedCount  = @@ROWCOUNT

    DROP TABLE #ChangedWorkItems

    RETURN 0
END

GO

