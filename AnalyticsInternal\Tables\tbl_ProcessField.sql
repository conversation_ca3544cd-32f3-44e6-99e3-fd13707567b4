CREATE TABLE [AnalyticsInternal].[tbl_ProcessField] (
    [PartitionId]          INT              NOT NULL,
    [AnalyticsCreatedDate] DATETIME         NOT NULL,
    [AnalyticsUpdatedDate] DATETIME         NOT NULL,
    [AnalyticsBatchId]     BIGINT           NOT NULL,
    [ProcessFieldSK]       INT              IDENTITY (1, 1) NOT NULL,
    [ProcessId]            UNIQUEIDENTIFIER NOT NULL,
    [Name]                 NVARCHAR (256)   NOT NULL,
    [ReferenceName]        NVARCHAR (256)   NOT NULL,
    [SourceFieldName]      NVARCHAR (256)   NOT NULL,
    [Description]          NVARCHAR (1024)  NULL,
    [FieldType]            NVARCHAR (50)    NOT NULL,
    [IsSystem]             BIT              NOT NULL,
    [IsDeleted]            BIT              NOT NULL,
    [IsHistoryEnabled]     BIT              NOT NULL,
    [IsPerson]             BIT              NOT NULL,
    [SourceKeyFieldName]   NVARCHAR (256)   NULL,
    [ModelTableName]       NVARCHAR (50)    NULL,
    [ModelColumnName]      NVARCHAR (50)    NULL
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_ProcessField_ProcessId_SourceFieldName]
    ON [AnalyticsInternal].[tbl_ProcessField]([PartitionId] ASC, [ProcessId] ASC, [SourceFieldName] ASC) WHERE ([IsDeleted]=(0));


GO

CREATE CLUSTERED INDEX [IX_tbl_ProcessField_ProcessId]
    ON [AnalyticsInternal].[tbl_ProcessField]([PartitionId] ASC, [ProcessId] ASC, [ReferenceName] ASC);


GO

CREATE UNIQUE NONCLUSTERED INDEX [PK_tbl_ProcessField]
    ON [AnalyticsInternal].[tbl_ProcessField]([PartitionId] ASC, [ProcessFieldSK] ASC);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_ProcessField_ProcessId_ReferenceName]
    ON [AnalyticsInternal].[tbl_ProcessField]([PartitionId] ASC, [ProcessId] ASC, [ReferenceName] ASC) WHERE ([IsDeleted]=(0));


GO

