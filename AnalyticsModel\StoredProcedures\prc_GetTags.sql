/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 3A0978004270BFD9219ACD5B6BF74E88DA096821
-- query Tags by Id
CREATE PROCEDURE AnalyticsModel.prc_GetTags
    @partitionId     INT,
    @tagIds         typ_GuidTable READONLY
AS
BEGIN

    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    BEGIN
        SELECT  ProjectSK,
                TagId,
                TagName
        FROM    AnalyticsModel.tbl_Tag a
        JOIN    @tagIds tagIds
        ON      a.TagId = tagIds.Id
        WHERE   a.PartitionId = @partitionId
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

END

GO

