/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: CCCB18591E5D2EE236C1F07B9F5F5D1080EE2865
--=================================
--Report the number of uncorrected batches
--=================================
CREATE PROCEDURE AnalyticsInternal.prc_DQ_Batch_UncorrectedFailedTransformCount
    @partitionId INT,
    @name VARCHAR(256),
    @latencyExclusionSeconds INT,
    @previousEndDate DATETIME
AS
BEGIN
    -- make sure ReworkBatchIds reflect latest rework
    EXEC AnalyticsInternal.prc_ValidateFailedBatches @partitionId, @batchId = NULL

    DECLARE @now DATETIME = GETUTCDATE();
    DECLARE @result AnalyticsInternal.typ_DataQualityResult3

    INSERT  @result (Scope, TargetTable, RunDate, StartDate, EndDate, ExpectedValue, ActualValue, KpiValue, Failed)
    SELECT  d.SprocName,
            d.TargetTableName,
            @now,
            '1900-01-01' AS StartDate,
            @now AS EndDate,
            0 AS ExpectedValue,
            COUNT(b.OperationSproc) AS ActualValue,
            COUNT(b.OperationSproc) AS KpiValue,
            IIF(COUNT(b.OperationSproc) > 0, 1, 0) AS Failed
    FROM    AnalyticsInternal.tbl_TransformDefinition d
    LEFT JOIN AnalyticsInternal.tbl_Batch b WITH (READPAST)
    ON      b.PartitionId = @partitionId
            AND b.OperationTriggerTableName = d.TriggerTableName
            AND b.TableName = d.TargetTableName
            AND b.Operation = d.TargetOperation
            AND b.OperationSproc = d.SProcName
            AND b.Failed = 1
            AND b.ReworkBatchId IS NULL
    GROUP BY d.SprocName, d.TargetTableName
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    EXEC AnalyticsInternal.prc_iRecordDataQuality
        @partitionId,
        @name,
        @result

    RETURN 0
END

GO

