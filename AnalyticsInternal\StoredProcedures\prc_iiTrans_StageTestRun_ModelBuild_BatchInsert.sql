/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 86DD6C79C1DCF8534FC444920FD26576A10DB46E
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTestRun_ModelBuild_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    CREATE TABLE #Build
    (
        BuildId                         INT                 NOT NULL,
        BuildDefinitionId               INT                 NULL
    )

    INSERT  #Build (BuildId, BuildDefinitionId)
    SELECT  BuildId,
            MAX(BuildDefinitionId) AS BuildPipelineId -- BuildDefinitionId has a default values in the ops store (0). It's rare but old data may have a mix of default and valid data for a single build.
    FROM    AnalyticsStage.tbl_TestRun WITH (INDEX (IX_tbl_TestRun_AxBatchIdChanged), FORCESEEK)
    WHERE   PartitionId = @partitionId
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND BuildId IS NOT NULL
    GROUP BY BuildId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    INSERT AnalyticsModel.tbl_Build
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        BuildId,
        BuildPipelineId,
        ProjectSK
    )
    SELECT  TOP (@batchSizeMax) @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            s.BuildId,
            s.BuildDefinitionId AS BuildPipelineId, -- Can set null (placeholder) once build transforms on everywhere
            NULL
    FROM    #Build AS s
    LEFT JOIN AnalyticsModel.tbl_Build AS t
    ON      t.PartitionId = @partitionId
            AND t.BuildId = s.BuildId
    WHERE   t.PartitionId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT
    SET @complete = IIF(@insertedCount < @batchSizeMax, 1, 0)

    DROP TABLE #Build

    RETURN 0
END

GO

