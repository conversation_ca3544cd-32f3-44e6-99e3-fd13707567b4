/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 2E943E1336CCCB1A902D8C34E331917ADCF53B43
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_Any_ModelBuildTaskResult_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    --Get the collection time zone
    DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

    DECLARE @popuplateJobSk BIT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'Target.PopulatePipelineJobSk'),0)
    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)
    SET @batchSizeMax = AnalyticsInternal.func_AdjustReattemptedBatchSize(@batchSizeMax, @attemptCount, @subBatchCount, @lastFailedSubBatchCount, @failedCount)

    CREATE TABLE #ImpactedBuild
    (
        ProjectSK        UNIQUEIDENTIFIER NOT NULL,
        BuildId          INT              NOT NULL,
        PlanId           INT              NOT NULL,
    )

    IF (@popuplateJobSk = 0)
    BEGIN
        IF (@triggerTableName = 'Build')
        BEGIN
            INSERT  #ImpactedBuild (ProjectSK, BuildId, PlanId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    sb.ProjectGuid,
                    sb.BuildId,
                    stp.PlanId
            FROM    AnalyticsStage.tbl_Build sb
            JOIN    AnalyticsStage.tbl_TaskPlan stp
            ON      stp.PartitionId = @partitionId
                    AND stp.ProjectGuid = sb.ProjectGuid
                    AND stp.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                    AND stp.PlanGuid = sb.PlanId
            WHERE   sb.PartitionId = @partitionId
                    AND sb.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND sb.BuildId > ISNULL(@stateData, -1)
            ORDER BY sb.BuildId
            OPTION (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(BuildId) FROM #ImpactedBuild)
        END
        ELSE IF (@triggerTableName = 'TaskPlan')
        BEGIN
            INSERT  #ImpactedBuild (ProjectSK, BuildId, PlanId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    stp.ProjectGuid,
                    sb.BuildId,
                    stp.PlanId
            FROM    AnalyticsStage.tbl_TaskPlan stp
            JOIN    AnalyticsStage.tbl_Build sb
            ON      sb.PartitionId = @partitionId
                    AND sb.ProjectGuid = stp.ProjectGuid
                    AND sb.PlanId = stp.PlanGuid
            WHERE   stp.PartitionId = @partitionId
                    AND stp.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                    AND stp.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND stp.PlanId > ISNULL(@stateData, -1)
            ORDER BY stp.PlanId
            OPTION (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(PlanId) FROM #ImpactedBuild)
        END

        CREATE TABLE #TaskResult
        (
            ProjectSK                       UNIQUEIDENTIFIER    NULL,
            BuildId                         INT                 NULL,
            BuildPipelineSK                 INT                 NULL,
            BuildPipelineTaskSK             INT                 NULL,
            BranchSK                        INT                 NULL,
            BuildQueuedDateSK               INT                 NULL,
            BuildStartedDateSK              INT                 NULL,
            BuildCompletedDateSK            INT                 NULL,
            PlanId                          INT                 NULL,
            TimelineId                      INT                 NULL,
            TimelineRecordId                UNIQUEIDENTIFIER    NULL,
            ActivityStartedDateSK           INT                 NULL,
            ActivityStartedDate             DATETIMEOFFSET(0)   NULL,
            ActivityCompletedDateSK         INT                 NULL,
            ActivityCompletedDate           DATETIMEOFFSET(0)   NULL,
            TaskDisplayName                 NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
            TaskLogPath                     NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
            TaskOutcome                     TINYINT             NULL,
            SucceededCount                  INT                 NOT NULL,
            SucceededWithIssuesCount        INT                 NOT NULL,
            FailedCount                     INT                 NOT NULL,
            CanceledCount                   INT                 NOT NULL,
            SkippedCount                    INT                 NOT NULL,
            AbandonedCount                  INT                 NOT NULL,
            ActivityDurationSeconds         DECIMAL(18,3)       NULL,
            BuildOutcome                    TINYINT             NULL,
            TaskDefinitionReferenceId       INT                 NULL
        )

        INSERT #TaskResult
        (
            ProjectSK,
            BuildId,
            BuildPipelineSK,
            BuildPipelineTaskSK,
            BranchSK,
            BuildQueuedDateSK,
            BuildStartedDateSK,
            BuildCompletedDateSK,
            PlanId,
            TimelineId,
            TimelineRecordId,
            ActivityStartedDateSK,
            ActivityStartedDate,
            ActivityCompletedDateSK,
            ActivityCompletedDate,
            TaskDisplayName,
            TaskLogPath,
            TaskOutcome,
            SucceededCount,
            SucceededWithIssuesCount,
            FailedCount,
            CanceledCount,
            SkippedCount,
            AbandonedCount,
            ActivityDurationSeconds,
            BuildOutcome,
            TaskDefinitionReferenceId
        )
        SELECT  sttr.ProjectGuid,
                sb.BuildId,
                mbp.BuildPipelineSK,
                mbpt.BuildPipelineTaskSK,
                mbb.BranchSK,
                AnalyticsInternal.func_GenDateSK(sb.QueueTime AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(sb.StartTime AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(sb.FinishTime AT TIME ZONE @timeZone),
                sttr.PlanId,
                sttr.TimelineId,
                sttr.TimelineRecordGuid,
                AnalyticsInternal.func_GenDateSK(sttr.StartTime AT TIME ZONE @timeZone),
                sttr.StartTime AT TIME ZONE @timeZone,
                AnalyticsInternal.func_GenDateSK(sttr.FinishTime AT TIME ZONE @timeZone),
                sttr.FinishTime AT TIME ZONE @timeZone,
                sttr.Name,
                sttr.LogPath,
                sttr.Result,
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 0, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 1, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 2, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 3, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 4, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 5, 1, 0)),
                DATEDIFF_BIG(millisecond, sttr.StartTime, sttr.FinishTime) / 1000.0,
                sb.result,
                sttr.TaskDefinitionReferenceId
        FROM    #ImpactedBuild ib
        JOIN    AnalyticsStage.tbl_Build sb
        ON      sb.BuildId = ib.BuildId
        JOIN    AnalyticsStage.tbl_TaskPlan stp
        ON      stp.PartitionId = @partitionId
                AND stp.ProjectGuid = ib.ProjectSK
                AND stp.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                AND stp.PlanId = ib.PlanId
        JOIN    AnalyticsStage.tbl_TaskTimelineRecord sttr
        ON      sttr.PartitionId = @partitionId
                AND sttr.ProjectGuid = stp.ProjectGuid
                AND sttr.PipelineType = stp.PipelineType
                AND sttr.PlanId = stp.PlanId
                AND sttr.[Type] = 'Task'        -- Only considering Timeline record of type 'Task'. Other records are of type: Build, Process, Stage, Phase, Job
        LEFT JOIN AnalyticsStage.tbl_TaskDefinitionReference stdr
        ON      stdr.PartitionId = @partitionId
                AND stdr.ProjectGuid = sttr.ProjectGuid
                AND stdr.PipelineType = sttr.PipelineType
                AND stdr.TaskDefinitionReferenceId = sttr.TaskDefinitionReferenceId
        LEFT JOIN AnalyticsModel.tbl_BuildPipelineTask mbpt
        ON      mbpt.PartitionId = @partitionId
                AND mbpt.ProjectSK = stdr.ProjectGuid
                AND mbpt.TaskDefinitionId = stdr.TaskDefinitionGuid
                AND mbpt.TaskDefinitionVersion = stdr.TaskDefinitionVersion
        JOIN    AnalyticsModel.tbl_BuildPipeline mbp
        ON      mbp.PartitionId = @partitionId
                AND mbp.BuildPipelineId = sb.DefinitionId
        JOIN    AnalyticsModel.tbl_Branch mbb
        ON      mbb.PartitionId = @partitionId
                AND mbb.RepositoryId = sb.RepositoryId
                AND mbb.BranchName = sb.BranchName
        WHERE   sb.PartitionId = @partitionId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- Delete Timeline recods for which there has to task defn associated but task defn reference has not arrived. These will be inserted with trigger on StageTaskDefinitionReference.
        DELETE  tr
        FROM    #TaskResult tr
        WHERE   TaskDefinitionReferenceId IS NOT NULL
                AND BuildPipelineTaskSK IS NULL

        INSERT AnalyticsModel.tbl_BuildTaskResult
        (
                  PartitionId
                , AnalyticsCreatedDate
                , AnalyticsUpdatedDate
                , AnalyticsBatchId
                , ProjectSK
                , BuildId
                , BuildPipelineSK
                , BuildPipelineTaskSK
                , BranchSK
                , BuildQueuedDateSK
                , BuildStartedDateSK
                , BuildCompletedDateSK
                , PlanId
                , TimelineId
                , TimelineRecordId
                , ActivityStartedDateSK
                , ActivityStartedDate
                , ActivityCompletedDateSK
                , ActivityCompletedDate
                , TaskDisplayName
                , TaskLogPath
                , TaskOutcome
                , SucceededCount
                , SucceededWithIssuesCount
                , FailedCount
                , CanceledCount
                , SkippedCount
                , AbandonedCount
                , ActivityDurationSeconds
                , BuildOutcome
        )
        SELECT    @partitionId
                , @batchDt
                , @batchDt
                , @batchId
                , s.ProjectSK
                , s.BuildId
                , s.BuildPipelineSK
                , s.BuildPipelineTaskSK
                , s.BranchSK
                , s.BuildQueuedDateSK
                , s.BuildStartedDateSK
                , s.BuildCompletedDateSK
                , s.PlanId
                , s.TimelineId
                , s.TimelineRecordId
                , s.ActivityStartedDateSK
                , s.ActivityStartedDate
                , s.ActivityCompletedDateSK
                , s.ActivityCompletedDate
                , s.TaskDisplayName
                , s.TaskLogPath
                , s.TaskOutcome
                , s.SucceededCount
                , s.SucceededWithIssuesCount
                , s.FailedCount
                , s.CanceledCount
                , s.SkippedCount
                , s.AbandonedCount
                , s.ActivityDurationSeconds
                , s.BuildOutcome
        FROM    #TaskResult s
        LEFT JOIN AnalyticsModel.tbl_BuildTaskResult mbtr
        ON      mbtr.PartitionId = @partitionId
                AND mbtr.ProjectSK = s.ProjectSK
                AND mbtr.PlanId = s.PlanId
                AND mbtr.TimelineId = s.TimelineId
                AND mbtr.TimelineRecordId = s.TimelineRecordId
        WHERE   mbtr.PartitionId IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @insertedCount = @@ROWCOUNT

        DROP TABLE #ImpactedBuild
        DROP TABLE #TaskResult
    END
    ELSE
    BEGIN
        IF (@triggerTableName = 'Build')
        BEGIN
            INSERT  #ImpactedBuild (ProjectSK, BuildId, PlanId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    sb.ProjectGuid,
                    sb.BuildId,
                    stp.PlanId
            FROM    AnalyticsStage.tbl_Build sb
            JOIN    AnalyticsStage.tbl_TaskPlan stp
            ON      stp.PartitionId = @partitionId
                    AND stp.ProjectGuid = sb.ProjectGuid
                    AND stp.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                    AND stp.PlanGuid = sb.PlanId
            WHERE   sb.PartitionId = @partitionId
                    AND sb.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND sb.BuildId > ISNULL(@stateData, -1)
            ORDER BY sb.BuildId
            OPTION (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(BuildId) FROM #ImpactedBuild)
        END
        ELSE IF (@triggerTableName = 'TaskPlan')
        BEGIN
            INSERT  #ImpactedBuild (ProjectSK, BuildId, PlanId)
            SELECT  TOP(@batchSizeMax) WITH TIES
                    stp.ProjectGuid,
                    sb.BuildId,
                    stp.PlanId
            FROM    AnalyticsStage.tbl_TaskPlan stp
            JOIN    AnalyticsStage.tbl_Build sb
            ON      sb.PartitionId = @partitionId
                    AND sb.ProjectGuid = stp.ProjectGuid
                    AND sb.PlanId = stp.PlanGuid
            WHERE   stp.PartitionId = @partitionId
                    AND stp.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                    AND stp.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
                    AND stp.PlanId > ISNULL(@stateData, -1)
            ORDER BY stp.PlanId
            OPTION (FORCE ORDER, OPTIMIZE FOR (@partitionId UNKNOWN))

            SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
            SET @endStateData = (SELECT MAX(PlanId) FROM #ImpactedBuild)
        END

        CREATE TABLE #TimelineRecordResult
        (
            ProjectSK                       UNIQUEIDENTIFIER    NULL,
            BuildId                         INT                 NULL,
            BuildPipelineSK                 INT                 NULL,
            BuildPipelineTaskSK             INT                 NULL,
            BranchSK                        INT                 NULL,
            BuildQueuedDateSK               INT                 NULL,
            BuildStartedDateSK              INT                 NULL,
            BuildCompletedDateSK            INT                 NULL,
            PlanId                          INT                 NULL,
            TimelineId                      INT                 NULL,
            TimelineRecordId                UNIQUEIDENTIFIER    NULL,
            ActivityStartedDateSK           INT                 NULL,
            ActivityStartedDate             DATETIMEOFFSET(0)   NULL,
            ActivityCompletedDateSK         INT                 NULL,
            ActivityCompletedDate           DATETIMEOFFSET(0)   NULL,
            TaskDisplayName                 NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
            TaskLogPath                     NVARCHAR(400)       COLLATE DATABASE_DEFAULT NULL,
            TaskOutcome                     TINYINT             NULL,
            SucceededCount                  INT                 NOT NULL,
            SucceededWithIssuesCount        INT                 NOT NULL,
            FailedCount                     INT                 NOT NULL,
            CanceledCount                   INT                 NOT NULL,
            SkippedCount                    INT                 NOT NULL,
            AbandonedCount                  INT                 NOT NULL,
            ActivityDurationSeconds         DECIMAL(18,3)       NULL,
            BuildOutcome                    TINYINT             NULL,
            TaskDefinitionReferenceId       INT                 NULL,
            PipelineJobSk                   INT                 NULL
        )

        INSERT #TimelineRecordResult
        (
            ProjectSK,
            BuildId,
            BuildPipelineSK,
            BuildPipelineTaskSK,
            BranchSK,
            BuildQueuedDateSK,
            BuildStartedDateSK,
            BuildCompletedDateSK,
            PlanId,
            TimelineId,
            TimelineRecordId,
            ActivityStartedDateSK,
            ActivityStartedDate,
            ActivityCompletedDateSK,
            ActivityCompletedDate,
            TaskDisplayName,
            TaskLogPath,
            TaskOutcome,
            SucceededCount,
            SucceededWithIssuesCount,
            FailedCount,
            CanceledCount,
            SkippedCount,
            AbandonedCount,
            ActivityDurationSeconds,
            BuildOutcome,
            TaskDefinitionReferenceId,
            PipelineJobSk
        )
        SELECT  sttr.ProjectGuid,
                sb.BuildId,
                mbp.BuildPipelineSK,
                mbpt.BuildPipelineTaskSK,
                mbb.BranchSK,
                AnalyticsInternal.func_GenDateSK(sb.QueueTime AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(sb.StartTime AT TIME ZONE @timeZone),
                AnalyticsInternal.func_GenDateSK(sb.FinishTime AT TIME ZONE @timeZone),
                sttr.PlanId,
                sttr.TimelineId,
                sttr.TimelineRecordGuid,
                AnalyticsInternal.func_GenDateSK(sttr.StartTime AT TIME ZONE @timeZone),
                sttr.StartTime AT TIME ZONE @timeZone,
                AnalyticsInternal.func_GenDateSK(sttr.FinishTime AT TIME ZONE @timeZone),
                sttr.FinishTime AT TIME ZONE @timeZone,
                IIF(sttr.[Type] = 'Task', sttr.Name , 'Job Failure For Agent'), -- other allowed type, other than task is failed Job only,
                sttr.LogPath,
                sttr.Result,
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 0, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 1, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 2, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 3, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 4, 1, 0)),
                IIF(sttr.Result IS NULL, 0, IIF(sttr.Result = 5, 1, 0)),
                DATEDIFF_BIG(millisecond, sttr.StartTime, sttr.FinishTime) / 1000.0,
                sb.result,
                sttr.TaskDefinitionReferenceId,
                mpj.PipelineJobSk
        FROM    #ImpactedBuild ib
        JOIN    AnalyticsStage.tbl_Build sb  WITH (INDEX(CI_tbl_Build))
        ON      sb.BuildId = ib.BuildId
        JOIN    AnalyticsStage.tbl_TaskPlan stp WITH (INDEX(CI_tbl_TaskPlan))
        ON      stp.PartitionId = @partitionId
                AND stp.ProjectGuid = ib.ProjectSK
                AND stp.PipelineType = 'Build'       -- Since transform for Build pipeline tasks.
                AND stp.PlanId = ib.PlanId
        JOIN    AnalyticsStage.tbl_TaskTimelineRecord sttr WITH (INDEX(CI_tbl_TaskTimelineRecord))
        ON      sttr.PartitionId = @partitionId
                AND sttr.ProjectGuid = stp.ProjectGuid
                AND sttr.PipelineType = stp.PipelineType
                AND sttr.PlanId = stp.PlanId
                AND sttr.[Type] IN  ('Task', 'Job')        -- Only considering Timeline record of type 'Task' and 'Job'. Other records are of type: Build, Process, Stage, Phase
        LEFT JOIN AnalyticsStage.tbl_TaskDefinitionReference stdr WITH (INDEX(CI_tbl_TaskDefinitionReference))
        ON      stdr.PartitionId = @partitionId
                AND stdr.ProjectGuid = sttr.ProjectGuid
                AND stdr.PipelineType = sttr.PipelineType
                AND stdr.TaskDefinitionReferenceId = sttr.TaskDefinitionReferenceId
        LEFT JOIN AnalyticsModel.tbl_BuildPipelineTask mbpt
        ON      mbpt.PartitionId = @partitionId
                AND mbpt.ProjectSK = stdr.ProjectGuid
                AND mbpt.TaskDefinitionId = stdr.TaskDefinitionGuid
                AND mbpt.TaskDefinitionVersion = stdr.TaskDefinitionVersion
        JOIN    AnalyticsModel.tbl_BuildPipeline mbp
        ON      mbp.PartitionId = @partitionId
                AND mbp.BuildPipelineId = sb.DefinitionId
        JOIN    AnalyticsModel.tbl_Branch mbb
        ON      mbb.PartitionId = @partitionId
                AND mbb.RepositoryId = sb.RepositoryId
                AND mbb.BranchName = sb.BranchName
        LEFT JOIN AnalyticsModel.tbl_PipelineJob mpj
        ON      mpj.PartitionId = sttr.PartitionId
                AND mpj.BuildPipelineId = sb.DefinitionId
                AND (mpj.StageIdentifier = sttr.StageIdentifier OR (mpj.StageIdentifier IS NULL AND sttr.StageIdentifier IS NULL))
                AND (mpj.PhaseIdentifier = sttr.PhaseIdentifier OR (mpj.PhaseIdentifier IS NULL AND sttr.PhaseIdentifier IS NULL))
                AND (mpj.JobIdentifier = IIF(Sttr.[Type] = 'Job', sttr.Identifier, sttr.JobIdentifier)  OR (mpj.JobIdentifier IS NULL AND sttr.JobIdentifier IS NULL))
        WHERE   sb.PartitionId = @partitionId
                 AND
                    ( sttr.[Type] = 'Task'
                        OR (
                            Sttr.[Type] = 'Job'
                            AND mpj.PartitionId IS NOT NULL
                            AND sttr.Result = 2
                            AND (
                                  sttr.IsJobFailureDueToTask = 0
                                  OR sttr.IsJobFailureDueToTask IS NULL
                                )
                          )
                      )
                  AND (stdr.TaskDefinitionReferenceId IS NULL OR stdr.AnalyticsBatchIdChanged <= @triggerBatchIdEnd)
                  AND sb.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
                  AND stp.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
                  AND sttr.AnalyticsBatchIdChanged <= @triggerBatchIdEnd
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- Delete Timeline recods for which there has to task defn associated but task defn reference has not arrived. These will be inserted with trigger on StageTaskDefinitionReference.
        DELETE  tr
        FROM    #TimelineRecordResult tr
        WHERE   TaskDefinitionReferenceId IS NOT NULL
                AND BuildPipelineTaskSK IS NULL

        INSERT AnalyticsModel.tbl_BuildTaskResult
        (
                  PartitionId
                , AnalyticsCreatedDate
                , AnalyticsUpdatedDate
                , AnalyticsBatchId
                , ProjectSK
                , BuildId
                , BuildPipelineSK
                , BuildPipelineTaskSK
                , BranchSK
                , BuildQueuedDateSK
                , BuildStartedDateSK
                , BuildCompletedDateSK
                , PlanId
                , TimelineId
                , TimelineRecordId
                , ActivityStartedDateSK
                , ActivityStartedDate
                , ActivityCompletedDateSK
                , ActivityCompletedDate
                , TaskDisplayName
                , TaskLogPath
                , TaskOutcome
                , SucceededCount
                , SucceededWithIssuesCount
                , FailedCount
                , CanceledCount
                , SkippedCount
                , AbandonedCount
                , ActivityDurationSeconds
                , BuildOutcome
                , PipelineJobSk
        )
        SELECT    @partitionId
                , @batchDt
                , @batchDt
                , @batchId
                , s.ProjectSK
                , s.BuildId
                , s.BuildPipelineSK
                , s.BuildPipelineTaskSK
                , s.BranchSK
                , s.BuildQueuedDateSK
                , s.BuildStartedDateSK
                , s.BuildCompletedDateSK
                , s.PlanId
                , s.TimelineId
                , s.TimelineRecordId
                , s.ActivityStartedDateSK
                , s.ActivityStartedDate
                , s.ActivityCompletedDateSK
                , s.ActivityCompletedDate
                , s.TaskDisplayName
                , s.TaskLogPath
                , s.TaskOutcome
                , s.SucceededCount
                , s.SucceededWithIssuesCount
                , s.FailedCount
                , s.CanceledCount
                , s.SkippedCount
                , s.AbandonedCount
                , s.ActivityDurationSeconds
                , s.BuildOutcome
                , s.PipelineJobSk
        FROM    #TimelineRecordResult s
        LEFT JOIN AnalyticsModel.tbl_BuildTaskResult mbtr
        ON      mbtr.PartitionId = @partitionId
                AND mbtr.ProjectSK = s.ProjectSK
                AND mbtr.PlanId = s.PlanId
                AND mbtr.TimelineId = s.TimelineId
                AND mbtr.TimelineRecordId = s.TimelineRecordId
        WHERE   mbtr.PartitionId IS NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        SET @insertedCount = @@ROWCOUNT

        DROP TABLE #ImpactedBuild
        DROP TABLE #TimelineRecordResult
    END
    RETURN 0
END

GO

