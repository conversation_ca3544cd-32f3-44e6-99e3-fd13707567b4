/******************************************************************************************************
** Warning: the contents of this function are critical to its functioning.
** Modifying the contents of this function could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 9C70E338D3CCDEB2F5BCB5BCA111381CB4781999
------------------------------------------
-- Returns physical parititons metadata --
------------------------------------------
CREATE FUNCTION AnalyticsInternal.func_iGetTableMaintenanceDefinitions()
RETURNS @mainTables TABLE
    (
        TableShortName NVARCHAR(50),
        ApplyMaxBatchFilter BIT,
        HasIdentity BIT,
        PartitionScheme NVARCHAR(256),
        KeyColumn NVARCHAR(50),
        MatchPredicate  NVARCHAR(256),
        TableName AS 'AnalyticsModel.' + TableShortName,
        IndexName AS 'CL_AnalyticsModel_' + TableShortName,
        TransformTableName NVARCHAR(256),
        OptimizeBatchCalculation    BIT DEFAULT 0,
        InsertOnlyOptimization    BIT DEFAULT 0,
        TableMaintenanceId          INT
    )
BEGIN
    INSERT INTO @mainTables
    VALUES ('tbl_WorkItem', 1, 0, 'scheme_AnalyticsWorkItem', 'WorkItemRevisionSK', 's.WorkItemId = t.WorkItemId AND s.Revision = t.Revision',  'Model.WorkItem', 0, 0, NULL),
           ('tbl_WorkItemHistory', 0, 0, 'scheme_AnalyticsWorkItem', 'WorkItemRevisionSK', 's.WorkItemId = t.WorkItemId AND s.Revision = t.Revision',  'Model.WorkItem', 0, 0, NULL),
           ('tbl_TestResult', 0, 1, 'scheme_AnalyticsTest', 'TestResultSK', 's.TestResultSK = t.TestResultSK',  'Model.TestResult', 0, 1, NULL),
           ('tbl_TestRun', 0, 1, 'scheme_AnalyticsTest', 'TestRunSK', 's.TestRunSK = t.TestRunSK',  'Model.TestRun', 0, 0, NULL),
           ('tbl_Test', 0, 1, 'scheme_AnalyticsTest', 'TestSK', 's.TestSK = t.TestSK',  'Model.Test', 0, 0, NULL),
           ('tbl_TestResultDaily', 0, 1, 'scheme_AnalyticsTest', 'TestResultDailySK', 's.TestResultDailySK = t.TestResultDailySK',  'Model.TestResultDaily', 0, 0, NULL),
           ('tbl_Build', 0, 1, 'scheme_AnalyticsBuild', 'BuildSK', 's.BuildSK = t.BuildSK', 'Model.Build', 0, 0, NULL),
           ('tbl_BuildTaskResult', 0, 1, 'scheme_AnalyticsBuild', 'BuildTaskResultSK', 's.BuildTaskResultSK = t.BuildTaskResultSK', 'Model.BuildActivity', 0, 0, NULL)

    INSERT INTO @mainTables
    SELECT  t.Name,
            0 AS ApplyMaxBatchFilter,
            0 AS HasIdentity,
            'scheme_AnalyticsWorkItem' AS PartitionScheme,
            'WorkItemRevisionSK'       AS KeyColumn,
            's.WorkItemId = t.WorkItemId AND s.Revision = t.Revision' AS  MatchPredicate,
            'Model.WorkItem' AS TransformTableName,
            0 AS OptimizeBatchCalculation,
            0 AS InsertOnlyOptimization,
            NULL AS TableMaintenanceId
    FROM    sys.tables t
    JOIN    sys.indexes i
    ON      t.object_id= i.object_id
    WHERE   t.name LIKE 'tbl_WorkItemRevisionCustom[0-9][0-9]'
            AND schema_id = SCHEMA_ID('AnalyticsModel') AND i.type = 5
    RETURN
END

GO

