CREATE TABLE [AnalyticsModel].[tbl_WorkItemBoardLocation] (
    [PartitionId]          INT      NOT NULL,
    [AnalyticsCreatedDate] DATETIME NOT NULL,
    [AnalyticsUpdatedDate] DATETIME NOT NULL,
    [AnalyticsBatchId]     BIGINT   NOT NULL,
    [WorkItemRevisionSK]   INT      NOT NULL,
    [BoardLocationSK]      INT      NOT NULL
);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_WorkItemBoardLocation]
    ON [AnalyticsModel].[tbl_WorkItemBoardLocation]([PartitionId] ASC, [WorkItemRevisionSK] ASC, [BoardLocationSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE NONCLUSTERED INDEX [IX_tbl_WorkItemBoardLocation_BoardLocationSK]
    ON [AnalyticsModel].[tbl_WorkItemBoardLocation]([PartitionId] ASC, [BoardLocationSK] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

