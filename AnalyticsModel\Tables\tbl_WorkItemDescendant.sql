CREATE TABLE [AnalyticsModel].[tbl_WorkItemDescendant] (
    [PartitionId]          INT           NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7) NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7) NOT NULL,
    [AnalyticsBatchId]     BIGINT        NOT NULL,
    [WorkItemId]           INT           NOT NULL,
    [DescendantWorkItemId] INT           NOT NULL,
    [Depth]                INT           NOT NULL
);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_WorkItemDescendant]
    ON [AnalyticsModel].[tbl_WorkItemDescendant]([PartitionId] ASC, [WorkItemId] ASC, [DescendantWorkItemId] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_tbl_WorkItemDescendant_Reverse]
    ON [AnalyticsModel].[tbl_WorkItemDescendant]([PartitionId] ASC, [DescendantWorkItemId] ASC, [WorkItemId] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

