/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 087A35B7DEE8EFC4E66BB1A1704C062B123E752F
CREATE PROCEDURE AnalyticsInternal.prc_iGetWorkItemSubBatchIdsFromModel
    @partitionId INT,
    @workItemIdStart BIGINT,
    @batchSizeMax INT,
    @subBatchWorkItemIdStart BIGINT OUTPUT,
    @subBatchWorkItemIdEnd BIGINT OUTPUT,
    @complete BIT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    SET @subBatchWorkItemIdStart = ISNULL(@workItemIdStart, 0)

    -- Get Ids for up to @batchSizeMax => we will deal with ~@batchSizeMax revisions not @batchSizeMax*by arbitrary number
    DECLARE @endStateData BIGINT
    SELECT @endStateData = MAX(WorkItemId)
    FROM
    (
        SELECT  TOP (@batchSizeMax) WorkItemId
        FROM    AnalyticsModel.tbl_WorkItemHistory
        WHERE   PartitionId = @partitionId
                AND WorkItemId >= @subBatchWorkItemIdStart
        ORDER   BY WorkItemId
    ) T
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN, @subBatchWorkItemIdStart = 0))

    -- make sure there is no more left in tbl_WorkItem
    IF (@endStateData IS NULL)
    BEGIN
        SELECT @endStateData = MAX(WorkItemId)
        FROM
        (
            SELECT  TOP (@batchSizeMax) WorkItemId
            FROM    AnalyticsModel.tbl_WorkItem
            WHERE   PartitionId = @partitionId
                    AND WorkItemId >= @subBatchWorkItemIdStart
            ORDER   BY WorkItemId
        ) T
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN, @subBatchWorkItemIdStart = 0))
    END

    SET @subBatchWorkItemIdEnd = ISNULL(@endStateData, 0)

    DECLARE @maxWorkItemId INT
    SELECT  @maxWorkItemId = MAX(WorkItemId)
    FROM    AnalyticsModel.tbl_WorkItemHistory
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    DECLARE @maxWorkItemId2 INT
    SELECT  @maxWorkItemId2 = MAX(WorkItemId)
    FROM    AnalyticsModel.tbl_WorkItem
    WHERE   PartitionId = @partitionId
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = IIF(@subBatchWorkItemIdEnd >= ISNULL(@maxWorkItemId, 0) AND @subBatchWorkItemIdEnd >= ISNULL(@maxWorkItemId2, 0), 1, 0)
END

GO

