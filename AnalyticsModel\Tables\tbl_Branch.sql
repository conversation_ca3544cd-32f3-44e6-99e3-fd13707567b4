CREATE TABLE [AnalyticsModel].[tbl_Branch] (
    [PartitionId]          INT              NOT NULL,
    [AnalyticsCreatedDate] DATETIME         NOT NULL,
    [AnalyticsUpdatedDate] DATETIME         NOT NULL,
    [AnalyticsBatchId]     BIGINT           NOT NULL,
    [BranchSK]             INT              IDENTITY (1, 1) NOT NULL,
    [RepositoryId]         NVARCHAR (400)   NOT NULL,
    [BranchName]           NVARCHAR (400)   COLLATE Latin1_General_BIN2 NOT NULL,
    [ProjectSK]            UNIQUEIDENTIFIER NULL,
    [RepositoryVstsId]     UNIQUEIDENTIFIER NULL,
    [RepositoryUrl]        NVARCHAR (400)   NULL
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_tbl_Branch_BranchName]
    ON [AnalyticsModel].[tbl_Branch]([PartitionId] ASC, [RepositoryId] ASC, [BranchName] ASC);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_Branch]
    ON [AnalyticsModel].[tbl_Branch]([PartitionId] ASC, [BranchSK] ASC);


GO

