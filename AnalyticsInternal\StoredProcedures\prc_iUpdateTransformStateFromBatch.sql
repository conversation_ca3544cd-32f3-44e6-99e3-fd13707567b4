/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: D34897C1D03DB826B5F97EC25403F4C3539BEE00
CREATE PROCEDURE AnalyticsInternal.prc_iUpdateTransformState<PERSON><PERSON><PERSON><PERSON>
    @partitionId    INT,
    @triggerBatchId INT,
    @tableLoading   BIT -- is this batch part of the target table's loading phase
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @triggerTableName VARCHAR(64)
    DECLARE @targetOperation VARCHAR(10)
    DECLARE @sproc VARCHAR(256)
    DECLARE @triggerBatchIdStart BIGINT
    DECLARE @triggerBatchIdEnd BIGINT
    DECLARE @targetTableName VARCHAR(64)
    DECLARE @inserted BIT
    DECLARE @updated BIT
    DECLARE @deleted BIT
    DECLARE @invalidated BIT
    DECLARE @ready BIT
    DECLARE @failed BIT
    DECLARE @analyticsProviderShardId INT
    DECLARE @analyticsStreamId INT

    -- this is here to ensure defintion changes are reflected in the TransformState table
    -- could be done as host level servicing to avoid this call
    -- but the definitionsOnly option should be quick
    EXEC AnalyticsInternal.prc_iEnsureTransformState @partitionId, @definitionsOnly = 1

    -- get batch information
    SELECT
        @triggerTableName = OperationTriggerTableName,
        @targetOperation = Operation,
        @sproc = OperationSproc,
        @triggerBatchIdStart = OperationTriggerBatchIdStart,
        @triggerBatchIdEnd = OperationTriggerBatchIdEnd,
        @targetTableName = TableName,
        @inserted = Inserted,
        @updated = Updated,
        @deleted = Deleted,
        @invalidated = Invalidated,
        @ready = Ready,
        @failed = Failed,
        @analyticsProviderShardId = AnalyticsProviderShardId,
        @analyticsStreamId = AnalyticsStreamId
    FROM AnalyticsInternal.tbl_Batch tb WITH (READPAST)
    WHERE PartitionId = @partitionId
        AND BatchId = @triggerBatchId
        AND Ready = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- update trigger IDs
    IF (@ready = 1)
    BEGIN
        -- match all transforms triggered by the batch and update the TriggerBatchIdMax and DoingBatchIdEnd
        -- The update attributes of the batch (inserted, updated, deleted) are matched to the TriggerOperation of the transforms for the trigger table
        UPDATE t
        SET t.TriggerBatchIdMax = IIF(@triggerBatchId > ISNULL(t.TriggerBatchIdMax, 0), @triggerBatchId, t.TriggerBatchIdMax),
            t.TriggerBatchIdMin = IIF(@triggerBatchId < ISNULL(t.TriggerBatchIdMin, **********), @triggerBatchId, t.TriggerBatchIdMin),
            t.DoingBatchIdEnd = IIF(@triggerBatchId <= ISNULL(t.DoingBatchIdEnd, 0), @triggerBatchId - 1, t.DoingBatchIdEnd)
        FROM AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
        WHERE t.PartitionId = @partitionId
            AND t.TriggerTableName = @targetTableName
            AND (
                (@inserted = 1 AND t.TriggerOperation IN ('merge', 'insert', 'replace'))
                OR
                (@updated = 1 AND t.TriggerOperation IN ('merge', 'update', 'replace'))
                OR
                (@deleted = 1 AND t.TriggerOperation IN ('delete', 'replace'))
                )
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        -- if this batch is from loading phase, update the loaded batch id end of the triggered transforms
        IF (@tableLoading = 1)
        BEGIN
            UPDATE t
            SET t.LoadedBatchIdMax = IIF(@triggerBatchId > ISNULL(t.LoadedBatchIdMax, 0) AND @tableLoading = 1, @triggerBatchId, t.LoadedBatchIdMax)
            FROM AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
            WHERE t.PartitionId = @partitionId
                AND t.TriggerTableName = @targetTableName
                AND (
                    (@inserted = 1 AND t.TriggerOperation IN ('merge', 'insert', 'replace'))
                    OR
                    (@updated = 1 AND t.TriggerOperation IN ('merge', 'update', 'replace'))
                    OR
                    (@deleted = 1 AND t.TriggerOperation IN ('delete', 'replace'))
                    )
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
        END

        -- now match any transforms triggered by an invalidate batch, and update the ReTriggerBatchIdMax
        UPDATE t
        SET t.ReTriggerBatchIdMax = @triggerBatchId
        FROM AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
        WHERE t.PartitionId = @partitionId
            AND t.TriggerTableName = @targetTableName
            AND @invalidated = 1
            AND @triggerBatchId > ISNULL(t.ReTriggerBatchIdMax, 0)
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
    END

    -- update done IDs and loaded status
    IF (@ready = 1 OR @failed = 1)
    BEGIN
        -- update this transform's done batch id and loaded status, regardless of if transform succeeded or failed
        UPDATE t
        SET t.DoneBatchIdEnd = @triggerBatchIdEnd
        FROM AnalyticsInternal.tbl_TransformState t WITH (ROWLOCK)
        WHERE t.PartitionId = @partitionId
            AND t.TriggerTableName = @triggerTableName
            AND t.targetOperation = @targetOperation
            AND t.SProcName = @sproc
            AND t.TargetTableName = @targetTableName
    END

END

GO

