/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A5ACD3AC0D4CD3113A37E8F4367B27CB4F6A9084
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageProject_ModelProject_BatchMerge
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10),
        IsDeleted BIT
    );

    ;WITH Src AS
    (
        SELECT  p.PartitionId,
                p.ProjectGuid AS ProjectSK,
                ProjectName,
                ISNULL(p.IsDeleted, 0) AS IsDeleted,
                p.ProcessId,
                pr.Name            AS ProcessName,
                pr.ReferenceName   AS ProcessReferenceName,
                pr.Description     AS ProcessDescription,
                pr.Version         AS ProcessVersion,
                pr.ProcessStatus   AS ProcessStatus,
                pr.IsSystem        AS IsSystemProcess,
                teamField.ReferenceName AS TeamFieldReferenceName
                -- TODO: Add support for ProcessLevelX attributes
        FROM    AnalyticsStage.tbl_Project p
        LEFT JOIN AnalyticsStage.tbl_Process pr
        ON      pr.PartitionId = p.PartitionId
                AND pr.ProcessId = p.ProcessId
        OUTER APPLY
                (
                SELECT  tf.Item.value('ReferenceName[1]','NVARCHAR(256)') AS ReferenceName
                FROM    pr.TypeFields.nodes('//Item') AS tf(Item)
                WHERE   tf.Item.value('Type[1]','NVARCHAR(70)') = 'Team'
                ) teamField
        WHERE   p.PartitionId = @partitionId
    )
    MERGE TOP (@batchSizeMax) AnalyticsModel.tbl_Project AS t
    USING Src AS s
    ON (t.PartitionId = s.PartitionId
        AND (t.ProjectSK = s.ProjectSK)
        )
    WHEN MATCHED AND NOT EXISTS (
        SELECT
        s.ProjectName
        , s.IsDeleted
        , s.ProjectSK
        , s.ProcessId
        , s.ProcessName
        , s.ProcessReferenceName
        , s.ProcessDescription
        , s.ProcessVersion
        , s.ProcessStatus
        , s.IsSystemProcess
        , s.TeamFieldReferenceName
        INTERSECT
        SELECT
        t.ProjectName
        , t.IsDeleted
        , t.ProjectSK
        , t.ProcessId
        , t.ProcessName
        , t.ProcessReferenceName
        , t.ProcessDescription
        , t.ProcessVersion
        , t.ProcessStatus
        , t.IsSystemProcess
        , t.TeamFieldReferenceName
        ) THEN
    UPDATE SET
        AnalyticsUpdatedDate = @batchDt
        , AnalyticsBatchId = @batchId
        , ProjectName = s.ProjectName
        , IsDeleted = s.IsDeleted
        , ProjectSK = s.ProjectSK
        , ProcessId = s.ProcessId
        , ProcessName           = s.ProcessName
        , ProcessReferenceName  = s.ProcessReferenceName
        , ProcessDescription    = s.ProcessDescription
        , ProcessVersion        = s.ProcessVersion
        , ProcessStatus         = s.ProcessStatus
        , IsSystemProcess       = s.IsSystemProcess
        , TeamFieldReferenceName = s.TeamFieldReferenceName
    WHEN NOT MATCHED BY TARGET THEN
    INSERT (
        PartitionId
        , AnalyticsBatchId
        , AnalyticsCreatedDate
        , AnalyticsUpdatedDate
        , ProjectSK
        , ProjectName
        , IsDeleted
        , ProcessId
        , ProcessName
        , ProcessReferenceName
        , ProcessDescription
        , ProcessVersion
        , ProcessStatus
        , IsSystemProcess
        , TeamFieldReferenceName
        )
    VALUES (
        s.PartitionId
        , @batchId
        , @batchDt
        , @batchDt
        , s.ProjectSK
        , s.ProjectName
        , s.IsDeleted
        , s.ProcessId
        , s.ProcessName
        , s.ProcessReferenceName
        , s.ProcessDescription
        , s.ProcessVersion
        , s.ProcessStatus
        , s.IsSystemProcess
        , s.TeamFieldReferenceName
        )
    OUTPUT $action, INSERTED.IsDeleted INTO @changes
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @insertedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'INSERT')
    SET @updatedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'UPDATE')
    SET @deletedCount = (SELECT COUNT(*) FROM @changes WHERE MergeAction = 'DELETE')

    IF (@insertedCount > 0)
    BEGIN
        EXEC prc_iiSendNotification @partitionId, 'FCA37BCC-5502-408C-8E4D-B73A3DCFA21B'
    END
    ELSE IF (@updatedCount > 0) -- batches that both insert and soft-delete projects only need to trigger one notification
    BEGIN
        -- check if any of the projects in the batch are soft-deleted
        IF EXISTS (SELECT 1
                   FROM @changes
                   WHERE MergeAction = 'UPDATE'
                       AND IsDeleted = 1
        )
        BEGIN
            EXEC prc_iiSendNotification @partitionId, 'BF5C56C8-849B-4BC3-A604-83128F921352'
        END
    END

    RETURN 0
END

GO

