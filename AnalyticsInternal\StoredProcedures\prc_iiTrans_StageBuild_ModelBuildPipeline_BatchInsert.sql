/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 008B2C6B205C0609C246E9B6B82BBFF2848DDAE8
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageBuild_ModelBuild<PERSON><PERSON>eline_BatchInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    CREATE TABLE #BuildPipeline
    (
        BuildDefinitionId   INT NULL,
    )

    INSERT  #BuildPipeline (BuildDefinitionId)
    SELECT  DISTINCT DefinitionId
    FROM    AnalyticsStage.tbl_Build
    WHERE   PartitionId = @partitionId
            AND AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
            AND DefinitionId IS NOT NULL -- a build could be staged with a null definition id
    OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- insert only placeholder, let StageBuildDefinition be the authority
    INSERT  AnalyticsModel.tbl_BuildPipeline
    (
        PartitionId,
        AnalyticsCreatedDate,
        AnalyticsUpdatedDate,
        AnalyticsBatchId,
        BuildPipelineId,
        ProjectSK,
        IsDeleted
    )
    SELECT  TOP(@batchSizeMax) @partitionId,
            @batchDt,
            @batchDt,
            @batchId,
            s.BuildDefinitionId AS BuildPipeline,
            NULL AS ProjectSK,
            0 AS IsDeleted
    FROM    #BuildPipeline AS s
    LEFT JOIN AnalyticsModel.tbl_BuildPipeline AS t
    ON      t.PartitionId = @partitionId
            AND t.BuildPipelineId = s.BuildDefinitionId
    WHERE   t.PartitionId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @insertedCount = @@ROWCOUNT
    SET @complete = IIF(@insertedCount < @batchSizeMax, 1, 0)

    DROP TABLE #BuildPipeline

    RETURN 0
END

GO

