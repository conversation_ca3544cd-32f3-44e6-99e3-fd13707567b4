/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: FF82C7FF632B967E5200579CCC860E1ADD3E0B44
CREATE PROCEDURE AnalyticsInternal.prc_GetPartitionId
    @partitionScheme NVARCHAR(128),
    @partitionNumber INT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    SELECT CAST(value as INT)
    FROM   sys.partition_range_values prv
    JOIN   sys.partition_schemes ps
    ON     prv.function_id = ps.function_id
    WHERE  ps.name = @partitionScheme
    AND    boundary_id = @partitionNumber
END

GO

