/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: C02517742B04DB2418DDDD62C6F4B645D55944B7
CREATE PROCEDURE AnalyticsInternal.prc_iCreateStreams
    @partitionId INT,
    @data AnalyticsInternal.typ_CreateStreamsData READONLY, -- Key = TableName, Value = ProviderShardId
    @enabled BIT,
    @keysOnly BIT,
    @now DATETIME -- Use when called as a step of another stored procedure
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)
    DECLARE @newStreams TABLE
    (
        TableName                VARCHAR(64)     NOT NULL,
        AnalyticsProviderShardId INT             NOT NULL,
        AnalyticsStreamId        INT             NOT NULL,
        Maintenance              BIT             NOT NULL,
        MaintenanceReason        NVARCHAR(255)   NULL
    )

    INSERT  AnalyticsInternal.tbl_TableProviderShardStream
            (
            PartitionId,
            TableName,
            AnalyticsProviderShardId,
            AnalyticsStreamId,
            [Priority],
            [Enabled],
            [Current],
            CreateTime,
            LoadedTime,
            UpdateTime,
            Maintenance,
            Disposed,
            InitialContentVersion,
            LatestContentVersion,
            KeysOnly,
            MaintenanceReason,
            MaintenanceChangedTime
            )
    OUTPUT  INSERTED.TableName, INSERTED.AnalyticsProviderShardId, INSERTED.AnalyticsStreamId, INSERTED.Maintenance, INSERTED.MaintenanceReason
    INTO    @newStreams
    SELECT  @partitionId,
            d.TableName,
            d.ProviderShardId,
            ISNULL(MAX(tpss.AnalyticsStreamId), 0) + 1,
            MAX(d.[Priority]),
            @enabled,
            0,
            @now,
            NULL,
            @now,
            0, -- Insert as if not in maintainence mode
            0,
            NULL,
            NULL,
            ISNULL(@keysOnly, 0),
            NULL,
            NULL
    FROM    @data AS d
    LEFT OUTER JOIN AnalyticsInternal.tbl_TableProviderShardStream AS tpss
    ON      tpss.TableName = d.TableName
            AND tpss.AnalyticsProviderShardId = d.ProviderShardId
            AND tpss.PartitionId = @partitionId
    GROUP BY d.TableName, d.ProviderShardId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- It's possible through direct servicing on a table that maintenance can be set, without setting a maintenance reason
    -- We'll default to inheriting the maintenance reason, but in the case that maintenance is set without a reason, we will default to this reason.
    DECLARE @UnknownMaintenanceReason NVARCHAR(255) = 'Unknown Maintenance Reason'

    -- If any partitions *other* than the input partition are *not* set to
    -- maintenance for this table, then the stream should *not* be in mainteannce mode
    -- however
    -- If other streams for the input partition are set to maintenance mode
    -- for this table, then the stream should be in maintenance mode
    UPDATE  ns
    SET     Maintenance = ISNULL(crossAcct.Maintenance, ISNULL(crossShard.Maintenance, 0)),
            MaintenanceReason = ISNULL(crossAcct.MaintenanceReason, crossShard.MaintenanceReason)
    FROM    @newStreams AS ns
    OUTER APPLY
    (
            -- will scan the minimum number of rows from index to determine no maintenance
            SELECT TOP 1 Maintenance
            FROM    AnalyticsInternal.tbl_TableProviderShardStream ts
            WHERE   PartitionId > 0
                    AND ts.TableName = ns.TableName
                    AND Maintenance = 0
                    AND PartitionId <> @partitionId
    ) crossAcctNoMaint
    OUTER APPLY
    (
            -- only runs if we know there are maintenance rows
            SELECT TOP 1 Maintenance,
                    ISNULL(MaintenanceReason, @UnknownMaintenanceReason) MaintenanceReason
            FROM    AnalyticsInternal.tbl_TableProviderShardStream ts
            WHERE   PartitionId > 0
                    AND ts.TableName = ns.TableName
                    AND Maintenance = 1
                    AND PartitionId <> @partitionId
                    AND crossAcctNoMaint.Maintenance IS NULL
            ORDER BY MaintenanceReason desc
    ) crossAcct
    OUTER APPLY
    (
            SELECT TOP 1 Maintenance,
                    ISNULL(MaintenanceReason, @UnknownMaintenanceReason) AS MaintenanceReason
            FROM    AnalyticsInternal.tbl_TableProviderShardStream ts
            WHERE   PartitionId = @partitionId
                    AND ts.TableName = ns.TableName
                    AND Maintenance = 1
            ORDER BY MaintenanceReason desc
    ) crossShard
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    UPDATE  t
    SET     Maintenance = s.Maintenance,
            MaintenanceReason = s.MaintenanceReason,
            MaintenanceChangedTime = SYSUTCDATETIME()
    FROM    AnalyticsInternal.tbl_TableProviderShardStream t
    JOIN    @newStreams AS s
    ON      t.PartitionId = @partitionId
            AND s.TableName = t.TableName
            AND s.AnalyticsProviderShardId = t.AnalyticsProviderShardId
            AND s.AnalyticsStreamId = t.AnalyticsStreamId
    WHERE   t.Maintenance = 0 --stream isn't already in maintenance
            AND s.Maintenance = 1
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

END

GO

