/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: B2E77F6AA7A3208B144100C46FABF87072DBC555
CREATE PROCEDURE AnalyticsInternal.prc_iTrans_StageWorkItemRevision_ModelWorkItem_BatchMerge_FromInsert
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    DECLARE @endStateOut VARCHAR(10)
    DECLARE @endStateDataOut BIGINT
    DECLARE @completeOut BIT
    DECLARE @insertedCountOut INT
    DECLARE @updatedCountOut INT
    DECLARE @deletedCountOut INT

    EXEC AnalyticsInternal.prc_iMergeWorkItemCore
        @partitionId,
        @batchId,
        @batchDt,
        @settings,
        @triggerTableName,
        'insert',
        @triggerBatchIdStart,
        @triggerBatchIdEnd,
        @state,
        @stateData,
        @attemptCount,
        @subBatchCount,
        @lastFailedSubBatchCount,
        @failedCount,
        @endState = @endStateOut OUTPUT,
        @endStateData = @endStateDataOut OUTPUT,
        @complete = @completeOut OUTPUT,
        @insertedCount = @insertedCountOut OUTPUT,
        @updatedCount = @updatedCountOut OUTPUT,
        @deletedCount = @deletedCountOut OUTPUT

    SET @endState = @endStateOut
    SET @endStateData = @endStateDataOut
    SET @complete = @completeOut
    SET @insertedCount = @insertedCountOut
    SET @updatedCount = @updatedCountOut
    SET @deletedCount = @deletedCountOut

    RETURN 0
END

GO

