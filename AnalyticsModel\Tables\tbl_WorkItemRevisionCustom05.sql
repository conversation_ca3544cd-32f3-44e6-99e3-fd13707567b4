CREATE TABLE [AnalyticsModel].[tbl_WorkItemRevisionCustom05] (
    [PartitionId]          INT                NOT NULL,
    [AnalyticsCreatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate] DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]     BIGINT             NOT NULL,
    [WorkItemId]           INT                NOT NULL,
    [Revision]             INT                NOT NULL,
    [WorkItemRevisionSK]   INT                NOT NULL,
    [String0801]           NVARCHAR (256)     NULL,
    [String0802]           NVARCHAR (256)     NULL,
    [String0803]           NVARCHAR (256)     NULL,
    [String0804]           NVARCHAR (256)     NULL,
    [String0805]           NVARCHAR (256)     NULL,
    [String0806]           NVARCHAR (256)     NULL,
    [String0807]           NVARCHAR (256)     NULL,
    [String0808]           NVARCHAR (256)     NULL,
    [String0809]           NVARCHAR (256)     NULL,
    [String0810]           NVARCHAR (256)     NULL,
    [String0811]           NVARCHAR (256)     NULL,
    [String0812]           NVARCHAR (256)     NULL,
    [String0813]           NVARCHAR (256)     NULL,
    [String0814]           NVARCHAR (256)     NULL,
    [String0815]           NVARCHAR (256)     NULL,
    [String0816]           NVARCHAR (256)     NULL,
    [String0817]           NVARCHAR (256)     NULL,
    [String0818]           NVARCHAR (256)     NULL,
    [String0819]           NVARCHAR (256)     NULL,
    [String0820]           NVARCHAR (256)     NULL,
    [String0821]           NVARCHAR (256)     NULL,
    [String0822]           NVARCHAR (256)     NULL,
    [String0823]           NVARCHAR (256)     NULL,
    [String0824]           NVARCHAR (256)     NULL,
    [String0825]           NVARCHAR (256)     NULL,
    [String0826]           NVARCHAR (256)     NULL,
    [String0827]           NVARCHAR (256)     NULL,
    [String0828]           NVARCHAR (256)     NULL,
    [String0829]           NVARCHAR (256)     NULL,
    [String0830]           NVARCHAR (256)     NULL,
    [String0831]           NVARCHAR (256)     NULL,
    [String0832]           NVARCHAR (256)     NULL,
    [String0833]           NVARCHAR (256)     NULL,
    [String0834]           NVARCHAR (256)     NULL,
    [String0835]           NVARCHAR (256)     NULL,
    [String0836]           NVARCHAR (256)     NULL,
    [String0837]           NVARCHAR (256)     NULL,
    [String0838]           NVARCHAR (256)     NULL,
    [String0839]           NVARCHAR (256)     NULL,
    [String0840]           NVARCHAR (256)     NULL,
    [String0841]           NVARCHAR (256)     NULL,
    [String0842]           NVARCHAR (256)     NULL,
    [String0843]           NVARCHAR (256)     NULL,
    [String0844]           NVARCHAR (256)     NULL,
    [String0845]           NVARCHAR (256)     NULL,
    [String0846]           NVARCHAR (256)     NULL,
    [String0847]           NVARCHAR (256)     NULL,
    [String0848]           NVARCHAR (256)     NULL,
    [String0849]           NVARCHAR (256)     NULL,
    [String0850]           NVARCHAR (256)     NULL,
    [String0851]           NVARCHAR (256)     NULL,
    [String0852]           NVARCHAR (256)     NULL,
    [String0853]           NVARCHAR (256)     NULL,
    [String0854]           NVARCHAR (256)     NULL,
    [String0855]           NVARCHAR (256)     NULL,
    [String0856]           NVARCHAR (256)     NULL,
    [String0857]           NVARCHAR (256)     NULL,
    [String0858]           NVARCHAR (256)     NULL,
    [String0859]           NVARCHAR (256)     NULL,
    [String0860]           NVARCHAR (256)     NULL,
    [String0861]           NVARCHAR (256)     NULL,
    [String0862]           NVARCHAR (256)     NULL,
    [String0863]           NVARCHAR (256)     NULL,
    [String0864]           NVARCHAR (256)     NULL,
    [String0865]           NVARCHAR (256)     NULL,
    [String0866]           NVARCHAR (256)     NULL,
    [String0867]           NVARCHAR (256)     NULL,
    [String0868]           NVARCHAR (256)     NULL,
    [String0869]           NVARCHAR (256)     NULL,
    [String0870]           NVARCHAR (256)     NULL,
    [String0871]           NVARCHAR (256)     NULL,
    [String0872]           NVARCHAR (256)     NULL,
    [String0873]           NVARCHAR (256)     NULL,
    [String0874]           NVARCHAR (256)     NULL,
    [String0875]           NVARCHAR (256)     NULL,
    [String0876]           NVARCHAR (256)     NULL,
    [String0877]           NVARCHAR (256)     NULL,
    [String0878]           NVARCHAR (256)     NULL,
    [String0879]           NVARCHAR (256)     NULL,
    [String0880]           NVARCHAR (256)     NULL,
    [String0881]           NVARCHAR (256)     NULL,
    [String0882]           NVARCHAR (256)     NULL,
    [String0883]           NVARCHAR (256)     NULL,
    [String0884]           NVARCHAR (256)     NULL,
    [String0885]           NVARCHAR (256)     NULL,
    [String0886]           NVARCHAR (256)     NULL,
    [String0887]           NVARCHAR (256)     NULL,
    [String0888]           NVARCHAR (256)     NULL,
    [String0889]           NVARCHAR (256)     NULL,
    [String0890]           NVARCHAR (256)     NULL,
    [String0891]           NVARCHAR (256)     NULL,
    [String0892]           NVARCHAR (256)     NULL,
    [String0893]           NVARCHAR (256)     NULL,
    [String0894]           NVARCHAR (256)     NULL,
    [String0895]           NVARCHAR (256)     NULL,
    [String0896]           NVARCHAR (256)     NULL,
    [String0897]           NVARCHAR (256)     NULL,
    [String0898]           NVARCHAR (256)     NULL,
    [String0899]           NVARCHAR (256)     NULL,
    [String0900]           NVARCHAR (256)     NULL,
    [String0901]           NVARCHAR (256)     NULL,
    [String0902]           NVARCHAR (256)     NULL,
    [String0903]           NVARCHAR (256)     NULL,
    [String0904]           NVARCHAR (256)     NULL,
    [String0905]           NVARCHAR (256)     NULL,
    [String0906]           NVARCHAR (256)     NULL,
    [String0907]           NVARCHAR (256)     NULL,
    [String0908]           NVARCHAR (256)     NULL,
    [String0909]           NVARCHAR (256)     NULL,
    [String0910]           NVARCHAR (256)     NULL,
    [String0911]           NVARCHAR (256)     NULL,
    [String0912]           NVARCHAR (256)     NULL,
    [String0913]           NVARCHAR (256)     NULL,
    [String0914]           NVARCHAR (256)     NULL,
    [String0915]           NVARCHAR (256)     NULL,
    [String0916]           NVARCHAR (256)     NULL,
    [String0917]           NVARCHAR (256)     NULL,
    [String0918]           NVARCHAR (256)     NULL,
    [String0919]           NVARCHAR (256)     NULL,
    [String0920]           NVARCHAR (256)     NULL,
    [String0921]           NVARCHAR (256)     NULL,
    [String0922]           NVARCHAR (256)     NULL,
    [String0923]           NVARCHAR (256)     NULL,
    [String0924]           NVARCHAR (256)     NULL,
    [String0925]           NVARCHAR (256)     NULL,
    [String0926]           NVARCHAR (256)     NULL,
    [String0927]           NVARCHAR (256)     NULL,
    [String0928]           NVARCHAR (256)     NULL,
    [String0929]           NVARCHAR (256)     NULL,
    [String0930]           NVARCHAR (256)     NULL,
    [String0931]           NVARCHAR (256)     NULL,
    [String0932]           NVARCHAR (256)     NULL,
    [String0933]           NVARCHAR (256)     NULL,
    [String0934]           NVARCHAR (256)     NULL,
    [String0935]           NVARCHAR (256)     NULL,
    [String0936]           NVARCHAR (256)     NULL,
    [String0937]           NVARCHAR (256)     NULL,
    [String0938]           NVARCHAR (256)     NULL,
    [String0939]           NVARCHAR (256)     NULL,
    [String0940]           NVARCHAR (256)     NULL,
    [String0941]           NVARCHAR (256)     NULL,
    [String0942]           NVARCHAR (256)     NULL,
    [String0943]           NVARCHAR (256)     NULL,
    [String0944]           NVARCHAR (256)     NULL,
    [String0945]           NVARCHAR (256)     NULL,
    [String0946]           NVARCHAR (256)     NULL,
    [String0947]           NVARCHAR (256)     NULL,
    [String0948]           NVARCHAR (256)     NULL,
    [String0949]           NVARCHAR (256)     NULL,
    [String0950]           NVARCHAR (256)     NULL,
    [String0951]           NVARCHAR (256)     NULL,
    [String0952]           NVARCHAR (256)     NULL,
    [String0953]           NVARCHAR (256)     NULL,
    [String0954]           NVARCHAR (256)     NULL,
    [String0955]           NVARCHAR (256)     NULL,
    [String0956]           NVARCHAR (256)     NULL,
    [String0957]           NVARCHAR (256)     NULL,
    [String0958]           NVARCHAR (256)     NULL,
    [String0959]           NVARCHAR (256)     NULL,
    [String0960]           NVARCHAR (256)     NULL,
    [String0961]           NVARCHAR (256)     NULL,
    [String0962]           NVARCHAR (256)     NULL,
    [String0963]           NVARCHAR (256)     NULL,
    [String0964]           NVARCHAR (256)     NULL,
    [String0965]           NVARCHAR (256)     NULL,
    [String0966]           NVARCHAR (256)     NULL,
    [String0967]           NVARCHAR (256)     NULL,
    [String0968]           NVARCHAR (256)     NULL,
    [String0969]           NVARCHAR (256)     NULL,
    [String0970]           NVARCHAR (256)     NULL,
    [String0971]           NVARCHAR (256)     NULL,
    [String0972]           NVARCHAR (256)     NULL,
    [String0973]           NVARCHAR (256)     NULL,
    [String0974]           NVARCHAR (256)     NULL,
    [String0975]           NVARCHAR (256)     NULL,
    [String0976]           NVARCHAR (256)     NULL,
    [String0977]           NVARCHAR (256)     NULL,
    [String0978]           NVARCHAR (256)     NULL,
    [String0979]           NVARCHAR (256)     NULL,
    [String0980]           NVARCHAR (256)     NULL,
    [String0981]           NVARCHAR (256)     NULL,
    [String0982]           NVARCHAR (256)     NULL,
    [String0983]           NVARCHAR (256)     NULL,
    [String0984]           NVARCHAR (256)     NULL,
    [String0985]           NVARCHAR (256)     NULL,
    [String0986]           NVARCHAR (256)     NULL,
    [String0987]           NVARCHAR (256)     NULL,
    [String0988]           NVARCHAR (256)     NULL,
    [String0989]           NVARCHAR (256)     NULL,
    [String0990]           NVARCHAR (256)     NULL,
    [String0991]           NVARCHAR (256)     NULL,
    [String0992]           NVARCHAR (256)     NULL,
    [String0993]           NVARCHAR (256)     NULL,
    [String0994]           NVARCHAR (256)     NULL,
    [String0995]           NVARCHAR (256)     NULL,
    [String0996]           NVARCHAR (256)     NULL,
    [String0997]           NVARCHAR (256)     NULL,
    [String0998]           NVARCHAR (256)     NULL,
    [String0999]           NVARCHAR (256)     NULL,
    [String1000]           NVARCHAR (256)     NULL,
    [Integer0201]          BIGINT             NULL,
    [Integer0202]          BIGINT             NULL,
    [Integer0203]          BIGINT             NULL,
    [Integer0204]          BIGINT             NULL,
    [Integer0205]          BIGINT             NULL,
    [Integer0206]          BIGINT             NULL,
    [Integer0207]          BIGINT             NULL,
    [Integer0208]          BIGINT             NULL,
    [Integer0209]          BIGINT             NULL,
    [Integer0210]          BIGINT             NULL,
    [Integer0211]          BIGINT             NULL,
    [Integer0212]          BIGINT             NULL,
    [Integer0213]          BIGINT             NULL,
    [Integer0214]          BIGINT             NULL,
    [Integer0215]          BIGINT             NULL,
    [Integer0216]          BIGINT             NULL,
    [Integer0217]          BIGINT             NULL,
    [Integer0218]          BIGINT             NULL,
    [Integer0219]          BIGINT             NULL,
    [Integer0220]          BIGINT             NULL,
    [Integer0221]          BIGINT             NULL,
    [Integer0222]          BIGINT             NULL,
    [Integer0223]          BIGINT             NULL,
    [Integer0224]          BIGINT             NULL,
    [Integer0225]          BIGINT             NULL,
    [Integer0226]          BIGINT             NULL,
    [Integer0227]          BIGINT             NULL,
    [Integer0228]          BIGINT             NULL,
    [Integer0229]          BIGINT             NULL,
    [Integer0230]          BIGINT             NULL,
    [Integer0231]          BIGINT             NULL,
    [Integer0232]          BIGINT             NULL,
    [Integer0233]          BIGINT             NULL,
    [Integer0234]          BIGINT             NULL,
    [Integer0235]          BIGINT             NULL,
    [Integer0236]          BIGINT             NULL,
    [Integer0237]          BIGINT             NULL,
    [Integer0238]          BIGINT             NULL,
    [Integer0239]          BIGINT             NULL,
    [Integer0240]          BIGINT             NULL,
    [Integer0241]          BIGINT             NULL,
    [Integer0242]          BIGINT             NULL,
    [Integer0243]          BIGINT             NULL,
    [Integer0244]          BIGINT             NULL,
    [Integer0245]          BIGINT             NULL,
    [Integer0246]          BIGINT             NULL,
    [Integer0247]          BIGINT             NULL,
    [Integer0248]          BIGINT             NULL,
    [Integer0249]          BIGINT             NULL,
    [Integer0250]          BIGINT             NULL,
    [Double0201]           FLOAT (53)         NULL,
    [Double0202]           FLOAT (53)         NULL,
    [Double0203]           FLOAT (53)         NULL,
    [Double0204]           FLOAT (53)         NULL,
    [Double0205]           FLOAT (53)         NULL,
    [Double0206]           FLOAT (53)         NULL,
    [Double0207]           FLOAT (53)         NULL,
    [Double0208]           FLOAT (53)         NULL,
    [Double0209]           FLOAT (53)         NULL,
    [Double0210]           FLOAT (53)         NULL,
    [Double0211]           FLOAT (53)         NULL,
    [Double0212]           FLOAT (53)         NULL,
    [Double0213]           FLOAT (53)         NULL,
    [Double0214]           FLOAT (53)         NULL,
    [Double0215]           FLOAT (53)         NULL,
    [Double0216]           FLOAT (53)         NULL,
    [Double0217]           FLOAT (53)         NULL,
    [Double0218]           FLOAT (53)         NULL,
    [Double0219]           FLOAT (53)         NULL,
    [Double0220]           FLOAT (53)         NULL,
    [Double0221]           FLOAT (53)         NULL,
    [Double0222]           FLOAT (53)         NULL,
    [Double0223]           FLOAT (53)         NULL,
    [Double0224]           FLOAT (53)         NULL,
    [Double0225]           FLOAT (53)         NULL,
    [Double0226]           FLOAT (53)         NULL,
    [Double0227]           FLOAT (53)         NULL,
    [Double0228]           FLOAT (53)         NULL,
    [Double0229]           FLOAT (53)         NULL,
    [Double0230]           FLOAT (53)         NULL,
    [Double0231]           FLOAT (53)         NULL,
    [Double0232]           FLOAT (53)         NULL,
    [Double0233]           FLOAT (53)         NULL,
    [Double0234]           FLOAT (53)         NULL,
    [Double0235]           FLOAT (53)         NULL,
    [Double0236]           FLOAT (53)         NULL,
    [Double0237]           FLOAT (53)         NULL,
    [Double0238]           FLOAT (53)         NULL,
    [Double0239]           FLOAT (53)         NULL,
    [Double0240]           FLOAT (53)         NULL,
    [Double0241]           FLOAT (53)         NULL,
    [Double0242]           FLOAT (53)         NULL,
    [Double0243]           FLOAT (53)         NULL,
    [Double0244]           FLOAT (53)         NULL,
    [Double0245]           FLOAT (53)         NULL,
    [Double0246]           FLOAT (53)         NULL,
    [Double0247]           FLOAT (53)         NULL,
    [Double0248]           FLOAT (53)         NULL,
    [Double0249]           FLOAT (53)         NULL,
    [Double0250]           FLOAT (53)         NULL,
    [DateTime0201]         DATETIMEOFFSET (7) NULL,
    [DateTime0202]         DATETIMEOFFSET (7) NULL,
    [DateTime0203]         DATETIMEOFFSET (7) NULL,
    [DateTime0204]         DATETIMEOFFSET (7) NULL,
    [DateTime0205]         DATETIMEOFFSET (7) NULL,
    [DateTime0206]         DATETIMEOFFSET (7) NULL,
    [DateTime0207]         DATETIMEOFFSET (7) NULL,
    [DateTime0208]         DATETIMEOFFSET (7) NULL,
    [DateTime0209]         DATETIMEOFFSET (7) NULL,
    [DateTime0210]         DATETIMEOFFSET (7) NULL,
    [DateTime0211]         DATETIMEOFFSET (7) NULL,
    [DateTime0212]         DATETIMEOFFSET (7) NULL,
    [DateTime0213]         DATETIMEOFFSET (7) NULL,
    [DateTime0214]         DATETIMEOFFSET (7) NULL,
    [DateTime0215]         DATETIMEOFFSET (7) NULL,
    [DateTime0216]         DATETIMEOFFSET (7) NULL,
    [DateTime0217]         DATETIMEOFFSET (7) NULL,
    [DateTime0218]         DATETIMEOFFSET (7) NULL,
    [DateTime0219]         DATETIMEOFFSET (7) NULL,
    [DateTime0220]         DATETIMEOFFSET (7) NULL,
    [DateTime0221]         DATETIMEOFFSET (7) NULL,
    [DateTime0222]         DATETIMEOFFSET (7) NULL,
    [DateTime0223]         DATETIMEOFFSET (7) NULL,
    [DateTime0224]         DATETIMEOFFSET (7) NULL,
    [DateTime0225]         DATETIMEOFFSET (7) NULL,
    [DateTime0226]         DATETIMEOFFSET (7) NULL,
    [DateTime0227]         DATETIMEOFFSET (7) NULL,
    [DateTime0228]         DATETIMEOFFSET (7) NULL,
    [DateTime0229]         DATETIMEOFFSET (7) NULL,
    [DateTime0230]         DATETIMEOFFSET (7) NULL,
    [DateTime0231]         DATETIMEOFFSET (7) NULL,
    [DateTime0232]         DATETIMEOFFSET (7) NULL,
    [DateTime0233]         DATETIMEOFFSET (7) NULL,
    [DateTime0234]         DATETIMEOFFSET (7) NULL,
    [DateTime0235]         DATETIMEOFFSET (7) NULL,
    [DateTime0236]         DATETIMEOFFSET (7) NULL,
    [DateTime0237]         DATETIMEOFFSET (7) NULL,
    [DateTime0238]         DATETIMEOFFSET (7) NULL,
    [DateTime0239]         DATETIMEOFFSET (7) NULL,
    [DateTime0240]         DATETIMEOFFSET (7) NULL,
    [DateTime0241]         DATETIMEOFFSET (7) NULL,
    [DateTime0242]         DATETIMEOFFSET (7) NULL,
    [DateTime0243]         DATETIMEOFFSET (7) NULL,
    [DateTime0244]         DATETIMEOFFSET (7) NULL,
    [DateTime0245]         DATETIMEOFFSET (7) NULL,
    [DateTime0246]         DATETIMEOFFSET (7) NULL,
    [DateTime0247]         DATETIMEOFFSET (7) NULL,
    [DateTime0248]         DATETIMEOFFSET (7) NULL,
    [DateTime0249]         DATETIMEOFFSET (7) NULL,
    [DateTime0250]         DATETIMEOFFSET (7) NULL,
    [Boolean0201]          BIT                NULL,
    [Boolean0202]          BIT                NULL,
    [Boolean0203]          BIT                NULL,
    [Boolean0204]          BIT                NULL,
    [Boolean0205]          BIT                NULL,
    [Boolean0206]          BIT                NULL,
    [Boolean0207]          BIT                NULL,
    [Boolean0208]          BIT                NULL,
    [Boolean0209]          BIT                NULL,
    [Boolean0210]          BIT                NULL,
    [Boolean0211]          BIT                NULL,
    [Boolean0212]          BIT                NULL,
    [Boolean0213]          BIT                NULL,
    [Boolean0214]          BIT                NULL,
    [Boolean0215]          BIT                NULL,
    [Boolean0216]          BIT                NULL,
    [Boolean0217]          BIT                NULL,
    [Boolean0218]          BIT                NULL,
    [Boolean0219]          BIT                NULL,
    [Boolean0220]          BIT                NULL,
    [Boolean0221]          BIT                NULL,
    [Boolean0222]          BIT                NULL,
    [Boolean0223]          BIT                NULL,
    [Boolean0224]          BIT                NULL,
    [Boolean0225]          BIT                NULL,
    [Boolean0226]          BIT                NULL,
    [Boolean0227]          BIT                NULL,
    [Boolean0228]          BIT                NULL,
    [Boolean0229]          BIT                NULL,
    [Boolean0230]          BIT                NULL,
    [Boolean0231]          BIT                NULL,
    [Boolean0232]          BIT                NULL,
    [Boolean0233]          BIT                NULL,
    [Boolean0234]          BIT                NULL,
    [Boolean0235]          BIT                NULL,
    [Boolean0236]          BIT                NULL,
    [Boolean0237]          BIT                NULL,
    [Boolean0238]          BIT                NULL,
    [Boolean0239]          BIT                NULL,
    [Boolean0240]          BIT                NULL,
    [Boolean0241]          BIT                NULL,
    [Boolean0242]          BIT                NULL,
    [Boolean0243]          BIT                NULL,
    [Boolean0244]          BIT                NULL,
    [Boolean0245]          BIT                NULL,
    [Boolean0246]          BIT                NULL,
    [Boolean0247]          BIT                NULL,
    [Boolean0248]          BIT                NULL,
    [Boolean0249]          BIT                NULL,
    [Boolean0250]          BIT                NULL,
    [Identity0201]         UNIQUEIDENTIFIER   NULL,
    [Identity0202]         UNIQUEIDENTIFIER   NULL,
    [Identity0203]         UNIQUEIDENTIFIER   NULL,
    [Identity0204]         UNIQUEIDENTIFIER   NULL,
    [Identity0205]         UNIQUEIDENTIFIER   NULL,
    [Identity0206]         UNIQUEIDENTIFIER   NULL,
    [Identity0207]         UNIQUEIDENTIFIER   NULL,
    [Identity0208]         UNIQUEIDENTIFIER   NULL,
    [Identity0209]         UNIQUEIDENTIFIER   NULL,
    [Identity0210]         UNIQUEIDENTIFIER   NULL,
    [Identity0211]         UNIQUEIDENTIFIER   NULL,
    [Identity0212]         UNIQUEIDENTIFIER   NULL,
    [Identity0213]         UNIQUEIDENTIFIER   NULL,
    [Identity0214]         UNIQUEIDENTIFIER   NULL,
    [Identity0215]         UNIQUEIDENTIFIER   NULL,
    [Identity0216]         UNIQUEIDENTIFIER   NULL,
    [Identity0217]         UNIQUEIDENTIFIER   NULL,
    [Identity0218]         UNIQUEIDENTIFIER   NULL,
    [Identity0219]         UNIQUEIDENTIFIER   NULL,
    [Identity0220]         UNIQUEIDENTIFIER   NULL,
    [Identity0221]         UNIQUEIDENTIFIER   NULL,
    [Identity0222]         UNIQUEIDENTIFIER   NULL,
    [Identity0223]         UNIQUEIDENTIFIER   NULL,
    [Identity0224]         UNIQUEIDENTIFIER   NULL,
    [Identity0225]         UNIQUEIDENTIFIER   NULL,
    [Identity0226]         UNIQUEIDENTIFIER   NULL,
    [Identity0227]         UNIQUEIDENTIFIER   NULL,
    [Identity0228]         UNIQUEIDENTIFIER   NULL,
    [Identity0229]         UNIQUEIDENTIFIER   NULL,
    [Identity0230]         UNIQUEIDENTIFIER   NULL,
    [Identity0231]         UNIQUEIDENTIFIER   NULL,
    [Identity0232]         UNIQUEIDENTIFIER   NULL,
    [Identity0233]         UNIQUEIDENTIFIER   NULL,
    [Identity0234]         UNIQUEIDENTIFIER   NULL,
    [Identity0235]         UNIQUEIDENTIFIER   NULL,
    [Identity0236]         UNIQUEIDENTIFIER   NULL,
    [Identity0237]         UNIQUEIDENTIFIER   NULL,
    [Identity0238]         UNIQUEIDENTIFIER   NULL,
    [Identity0239]         UNIQUEIDENTIFIER   NULL,
    [Identity0240]         UNIQUEIDENTIFIER   NULL,
    [Identity0241]         UNIQUEIDENTIFIER   NULL,
    [Identity0242]         UNIQUEIDENTIFIER   NULL,
    [Identity0243]         UNIQUEIDENTIFIER   NULL,
    [Identity0244]         UNIQUEIDENTIFIER   NULL,
    [Identity0245]         UNIQUEIDENTIFIER   NULL,
    [Identity0246]         UNIQUEIDENTIFIER   NULL,
    [Identity0247]         UNIQUEIDENTIFIER   NULL,
    [Identity0248]         UNIQUEIDENTIFIER   NULL,
    [Identity0249]         UNIQUEIDENTIFIER   NULL,
    [Identity0250]         UNIQUEIDENTIFIER   NULL
) ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_AnalyticsModel_tbl_WorkItemRevisionCustom05_WorkItemRevisionSK]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom05]([PartitionId] ASC, [WorkItemRevisionSK] ASC) WITH (DATA_COMPRESSION = PAGE ON PARTITIONS (1), DATA_COMPRESSION = PAGE ON PARTITIONS (2), DATA_COMPRESSION = PAGE ON PARTITIONS (3), DATA_COMPRESSION = PAGE ON PARTITIONS (4), DATA_COMPRESSION = PAGE ON PARTITIONS (5), DATA_COMPRESSION = PAGE ON PARTITIONS (6), DATA_COMPRESSION = PAGE ON PARTITIONS (7), DATA_COMPRESSION = PAGE ON PARTITIONS (8), DATA_COMPRESSION = PAGE ON PARTITIONS (9), DATA_COMPRESSION = PAGE ON PARTITIONS (10), DATA_COMPRESSION = PAGE ON PARTITIONS (11))
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

CREATE CLUSTERED COLUMNSTORE INDEX [CL_AnalyticsModel_tbl_WorkItemRevisionCustom05]
    ON [AnalyticsModel].[tbl_WorkItemRevisionCustom05]
    ON [scheme_AnalyticsWorkItem] ([PartitionId]);


GO

