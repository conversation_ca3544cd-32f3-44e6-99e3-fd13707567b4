/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 8C4194E8C33DF3E1AB7347E9DF1C6255655108B5
CREATE PROCEDURE AnalyticsInternal.prc_MaintainColumnStores
   @partitionId INT,
   @partitionScheme NVARCHAR(256),
   @operation  NVARCHAR(10), --Possible values SPLIT, MERGE, SORT
   @tableName NVARCHAR(255) = NULL, -- Table to sort
   @withIndexRebuild BIT = 0
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON
    SET DEADLOCK_PRIORITY HIGH -- Maintenance is expensive in case of deadlock kill transform that will be retried

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)
    DECLARE @errorMessage NVARCHAR(255)
    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @mainTables TABLE
    (
        TableShortName NVARCHAR(50),
        ApplyMaxBatchFilter BIT,
        HasIdentity BIT,
        PartitionScheme NVARCHAR(256),
        KeyColumn NVARCHAR(50),
        MatchPredicate  NVARCHAR(256),
        TableName NVARCHAR(256),
        IndexName NVARCHAR(256),
        TransformTableName NVARCHAR(256),
        OptimizeBatchCalculation    BIT,
        InsertOnlyOptimization      BIT,
        TableMaintenanceId          INT
    )

    INSERT INTO @mainTables
    SELECT * FROM AnalyticsInternal.func_iGetTableMaintenanceDefinitions()

    IF NOT EXISTS(SELECT * FROM @mainTables WHERE PartitionScheme = @partitionScheme)
    BEGIN
        SET @status = 1670026
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @partitionScheme)
        RETURN
    END

    IF @operation NOT IN( 'SPLIT', 'MERGE', 'SORT')
    BEGIN
        SET @status = 1670014
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @operation)
        RETURN
    END

    IF @tableName IS NOT NULL AND @operation != 'SORT'
    BEGIN
        SET @status = 1670021
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @operation)
        RETURN
    END

    IF @operation = 'SORT' AND @tableName IS NULL
    BEGIN
       SET @status = 1670023
       SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @operation)
       RETURN
    END

    IF @operation = 'SORT' AND @tableName IS NOT NULL
    BEGIN
        IF NOT EXISTS (SELECT * FROM @mainTables WHERE TableShortName = @tableName)
        BEGIN
            SET @status = 1670022
            SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @operation)
            RETURN
        END

        -- Keep only specified table
        DELETE FROM @mainTables WHERE TableShortName <> @tableName
    END

    DELETE FROM @mainTables WHERE PartitionScheme <> @partitionScheme

    DECLARE @newLine CHAR(2) = CHAR(10)+CHAR(13)
    DECLARE @originalPartitionFunctionId INT
    DECLARE @originalPartitionFunctionName NVARCHAR(256)

    SELECT @originalPartitionFunctionId = pf.function_id,
           @originalPartitionFunctionName = pf.name
    FROM   sys.partition_functions pf
    JOIN   sys.partition_schemes ps
    ON     ps.function_id=pf.function_id
    WHERE  ps.name = @partitionScheme

    DECLARE @hasRange BIT = IIF( EXISTS (SELECT * FROM sys.partition_range_values WHERE value = @partitionId  AND function_id = @originalPartitionFunctionId), 1, 0)
    -- We are using LEFT partition range
    -- When Partition function is defined as (B1,B2,B3,....BN) it means that we have following partitions
    -- 1. PartitionId <= B1
    -- 2. PartitionId > B1 AND PartitionId <= B2
    -- i. PartitionId > Bi AND PartitionId <= Bi+1
    -- For SPLIT or SORT on @partitionId = P we are operating on partition i where Bi < P AND P < Bi+1
    -- For MERGE on @partitionId = P we are operating on partitions i, i+1 where Bi < P AND P = Bi+1
    -- Prepare two partition functions/schemes: one with split on P and one without
    -- With ranges (Bi,P, Bi+1)  for split schema and (Bi, Bi+1) for merge schema
    -- For SORT operation they both will be just (Bi, Bi+1)
    -- We will have ranges for split schema 1 (inf,Bi], 2 (Bi,P], 3 (p, Bi+1] 4 (Bi+1, inf)
    -- We will have ranges for merge schema 1 (inf,Bi], 2 (Bi,Bi+1], 3 (Bi+1, inf)
    -- Migration looks like
    -- 1. [Long operation] Copy data with sorting to temp table with split for SPLIT/SORT or merge schema for MERGE
    -- 2. [Fast metadata operation] Switch partition(s) from original table to old table with merge for SPLIT/SORT or split schema for MERGE
    --    It's opposite schema to which we are using for copying data and it must be compatible with original table.
    -- 3. [Fast metadata operation] Split or merge original partition schema. We could do that, because it's no data between Bi and Bi+1 in original table
    -- 4. [Fast metadata operation] Switch partition(s) from temp table to original table

    -- Checks that SPLIT or MERGE is possible
    IF @operation = 'SPLIT' AND  @hasRange = 1
    BEGIN
        SET @status = 1670015
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @partitionId)
        RETURN
    END
    ELSE IF @operation = 'MERGE' AND  @hasRange = 0
    BEGIN
        SET @status = 1670016
        SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @partitionId)
        RETURN
    END

    -- Figure out range boundaries for split/merge functions
    DECLARE @valuesTable TABLE
    (
       value INT PRIMARY KEY
    )

    INSERT INTO @valuesTable
    SELECT Boundary
    FROM
    (
       SELECT MAX(CAST(value AS INT)) AS Boundary
       FROM sys.partition_range_values
       WHERE value < @partitionId
          AND function_id = @originalPartitionFunctionId
       UNION
       SELECT IIF( @operation != 'SORT', @partitionId, NULL)
       UNION
       SELECT MIN(CAST(value AS INT))
       FROM sys.partition_range_values
       WHERE (value > @partitionId OR (@operation = 'SORT' AND value = @partitionId))
          AND function_id = @originalPartitionFunctionId
    ) T
    WHERE Boundary IS NOT NULL

    -- Usually we are dealing with partition 2 and 3 in temp/old tables, except the case when we are dealing with first partition in original table
    DECLARE @firstPartitionIdInTemp INT = (SELECT COUNT(*) FROM @valuesTable ) - IIF( @operation != 'SORT', 1, 0)

    -- Get involved partitions range
    DECLARE @startPartitionId INT
    DECLARE @endPartitionId INT

    SELECT @startPartitionId = ISNULL(MAX(CAST(value AS INT)) + 1, 0)
    FROM sys.partition_range_values
    WHERE value < @partitionId
       AND function_id = @originalPartitionFunctionId

    SELECT @endPartitionId = MIN(CAST(value AS INT))
    FROM sys.partition_range_values
    WHERE (value > @partitionId OR (@operation = 'SORT' AND value = @partitionId))
       AND function_id = @originalPartitionFunctionId

    SELECT 'Range:', @startPartitionId, @endPartitionId

    -- When we in last partition we need to extend range and shift partition numbers
    IF @endPartitionId IS NULL
    BEGIN
        SET @endPartitionId = 2147483647
        SET @firstPartitionIdInTemp += 1
    END

    -- Check for existing operations and wait or restart if needed
    DECLARE @continue BIT = 0
    IF ((SELECT COUNT(*) FROM @mainTables)
    =
    (SELECT COUNT(*) FROM AnalyticsInternal.tbl_TableMaintenance
     WHERE  TableName IN (SELECT TableShortName FROM @mainTables)
            AND StartPartitionId = @startPartitionId
            AND EndPartitionId = @endPartitionId
            AND IsActive = 1
            AND Operation = @operation))
    BEGIN
        PRINT 'Exact match. We will continue previous operation'
        SET @continue = 1
    END
    ELSE
    BEGIN
        WHILE(EXISTS(SELECT * FROM AnalyticsInternal.tbl_TableMaintenance
                     WHERE   TableName IN (SELECT TableShortName FROM @mainTables)
                             AND IsActive = 1))
        BEGIN
            WAITFOR DELAY '00:00:30'
            PRINT 'Waiting for existing operations'
        END
    END

    DECLARE @splitPartitionId INT = IIF (@operation = 'SPLIT', @partitionId, NULL)

    IF (@continue = 0)
    BEGIN
        DECLARE @funcCmd NVARCHAR(MAX) =
        (
           SELECT value AS P
           FROM @valuesTable
           ORDER BY value
           FOR XML PATH('')
        )

        SET @funcCmd = REPLACE(REPLACE(REPLACE(@funcCmd,'</P><P>',','),'<P>',''),'</P>','')

        DECLARE @cmd NVARCHAR(MAX) = 'CREATE PARTITION FUNCTION ' + @originalPartitionFunctionName + '_Split(INT) AS RANGE LEFT FOR VALUES('+ @funcCmd + ');' + @newLine

        SET @cmd += 'CREATE PARTITION SCHEME ' + @partitionScheme +'_Split AS PARTITION ' +@originalPartitionFunctionName+ '_Split ALL TO ([PRIMARY])'

        --PRINT @cmd

        EXEC (@cmd)

        SET @funcCmd =
        (
           SELECT value AS P
           FROM @valuesTable
           WHERE value != @partitionId OR @operation = 'SORT'
           ORDER BY value
           FOR XML PATH('')
        )

        SET @funcCmd = REPLACE(REPLACE(REPLACE(@funcCmd,'</P><P>',','),'<P>',''),'</P>','')

        SET @cmd = 'CREATE PARTITION FUNCTION ' + @originalPartitionFunctionName + '_Merge(INT) AS RANGE LEFT FOR VALUES('+ @funcCmd + ');' + @newLine

        SET @cmd += 'CREATE PARTITION SCHEME ' + @partitionScheme +'_Merge AS PARTITION ' +@originalPartitionFunctionName+ '_Merge ALL TO ([PRIMARY])'

        --PRINT @cmd

        EXEC (@cmd)

        DECLARE @splitPartitionScheme NVARCHAR(256) = @partitionScheme +'_Split'
        DECLARE @mergePartitionScheme NVARCHAR(256) = @partitionScheme +'_Merge'

        DECLARE @tempPartitionScheme NVARCHAR(256) = IIF(@operation = 'SPLIT', @splitPartitionScheme, @mergePartitionScheme)
        DECLARE @oldPartitionScheme NVARCHAR(256) = IIF(@operation = 'SPLIT', @mergePartitionScheme, @splitPartitionScheme)

        DECLARE @allTablesCursor CURSOR
        DECLARE @workTableName NVARCHAR(256)
        DECLARE @indexName NVARCHAR(256)
        SET @allTablesCursor = CURSOR LOCAL FORWARD_ONLY STATIC FOR
        (
            SELECT  TableName, NULL FROM @mainTables
        )

        OPEN @allTablesCursor
        FETCH NEXT FROM @allTablesCursor INTO @workTableName, @indexName;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            DECLARE @tempTable NVARCHAR(256) = @workTableName + '_Temp'
            DECLARE @oldTable NVARCHAR(256) = @workTableName + '_Old'

            SELECT @workTableName, @tempTable, @tempPartitionScheme

            EXEC AnalyticsInternal.prc_CloneTable @workTableName, @tempTable, @tempPartitionScheme, @stripIdentityAttribute = 0
            EXEC AnalyticsInternal.prc_CloneTable @workTableName, @oldTable, @oldPartitionScheme, @stripIdentityAttribute = 0

           FETCH NEXT FROM @allTablesCursor INTO @workTableName, @indexName;
        END

        CLOSE @allTablesCursor
        DEALLOCATE @allTablesCursor
    END

    IF (@continue = 0)
    BEGIN
        INSERT INTO AnalyticsInternal.tbl_TableMaintenance
        SELECT  TableShortName,
                @operation,
                @startPartitionId,
                @endPartitionId,
                1 as IsActive,
                CONCAT(@operation,' for scheme: ', @partitionScheme, ' table: ', @tableName, ' partitionId: ', @partitionId),
                SYSUTCDATETIME()
        FROM @mainTables
    END

    UPDATE t
    SET    TableMaintenanceId = m.TableMaintenanceId
    FROM   @mainTables t
    JOIN   AnalyticsInternal.tbl_TableMaintenance m
    ON     t.TableShortName = m.TableName
           AND IsActive = 1

    SELECT * FROM @mainTables

    -- TESTPATTERN: ACTIVATED

    DECLARE @holdReason NVARCHAR(255) = CONCAT(@operation, ' for scheme: ', @partitionScheme, ' table: ', ISNULL(@tableName, 'All'), ' partition: ', @partitionId)

    -- We have temp and old tables. Let's copy data
    DECLARE @mainTablesCursor CURSOR
    DECLARE @mainTableName NVARCHAR(256)
    DECLARE @maintenanceId INT
    DECLARE @result INT

    -- Phase 1: Online copy
    -- Copy data for all involved tables first (take awhile)
    SET @mainTablesCursor = CURSOR LOCAL FORWARD_ONLY STATIC FOR
    SELECT  TableName, TableMaintenanceId FROM @mainTables
    OPEN @mainTablesCursor
    FETCH NEXT FROM @mainTablesCursor INTO @mainTableName,  @maintenanceId;
    WHILE @@FETCH_STATUS = 0
    BEGIN
       -- Prepare tables
       EXEC AnalyticsInternal.prc_iPopulateMaintenancePartitions  @maintenanceId = @maintenanceId, @tableName = @mainTableName, @startPartitionId = @startPartitionId, @endPartitionId = @endPartitionId, @splitPartitionId = @splitPartitionId

       -- Copy data
       EXECUTE @result = AnalyticsInternal.prc_iCloneDataOnline @maintenanceId, @mainTableName
       IF (@result = -1)
       BEGIN
            RETURN
       END

       FETCH NEXT FROM @mainTablesCursor INTO @mainTableName,  @maintenanceId;
    END
    CLOSE @mainTablesCursor
    DEALLOCATE @mainTablesCursor

    -- Phase 2: Stop transform and do leftovers
    SET @mainTablesCursor = CURSOR LOCAL FORWARD_ONLY STATIC FOR
    SELECT  TableName, TableMaintenanceId FROM @mainTables
    OPEN @mainTablesCursor
    FETCH NEXT FROM @mainTablesCursor INTO @mainTableName,  @maintenanceId;
    WHILE @@FETCH_STATUS = 0
    BEGIN
       EXECUTE @result = AnalyticsInternal.prc_iProcessRecentBatchesOffline @maintenanceId, @mainTableName, @holdReason
       IF (@result = -1)
       BEGIN
            RETURN
       END

       FETCH NEXT FROM @mainTablesCursor INTO @mainTableName,  @maintenanceId;
    END
    CLOSE @mainTablesCursor
    DEALLOCATE @mainTablesCursor

    -- Sleep to let TM process copied data to avoid locks
    EXEC AnalyticsInternal.prc_iSleepIfTMBusy

    -- Check that number of records in Original table and TEMP is the same
    -- We are assuming that all staging and transformations were stopped for entire table
    DECLARE @sizeCheckCommand NVARCHAR(MAX) = ''
    DECLARE @countInTemp BIGINT
    DECLARE @countInOld  BIGINT
    DECLARE @firstSwapDone BIT = 0

    SET @mainTablesCursor = CURSOR LOCAL FORWARD_ONLY STATIC FOR
    SELECT  TableName FROM @mainTables

    OPEN @mainTablesCursor
    WHILE (1 = 1)
    BEGIN
        FETCH NEXT FROM @mainTablesCursor INTO @mainTableName;

        IF @@FETCH_STATUS <> 0
        BEGIN
            BREAK
        END

       SET @sizeCheckCommand = 'SELECT @countInTemp = COUNT_BIG(*) FROM '+ @mainTableName + '_Temp'
       EXEC sp_executesql @sizeCheckCommand, N'@countInTemp BIGINT OUTPUT', @countInTemp = @countInTemp OUTPUT

       SET @sizeCheckCommand = 'SELECT @countInOld = COUNT_BIG(*) FROM '+ @mainTableName + ' WHERE PartitionId BETWEEN @startPartitionId AND @endPartitionId'
       EXEC sp_executesql @sizeCheckCommand, N'@startPartitionId INT, @endPartitionId INT, @countInOld BIGINT OUTPUT',
            @startPartitionId = @startPartitionId, @endPartitionId = @endPartitionId, @countInOld = @countInOld OUTPUT

       IF  @countInTemp != @countInOld
       BEGIN
          IF @countInOld = 0
          BEGIN
            -- Source table is empty => possible already did first swap, but previous run died
            -- Checking that it's the case
            SET @sizeCheckCommand = 'SELECT @countInOld = COUNT_BIG(*) FROM '+ @mainTableName + '_Old'
            EXEC sp_executesql @sizeCheckCommand, N'@countInOld BIGINT OUTPUT', @countInOld = @countInOld OUTPUT

            IF @countInTemp = @countInOld
            BEGIN
                SET @firstSwapDone = 1
                CONTINUE
            END
          END

          SET @status = 1670018
          SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @mainTableName, @countInOld, @countInTemp)
          RETURN
       END
    END

    CLOSE @mainTablesCursor
    DEALLOCATE @mainTablesCursor

    -- Data was copied to temp tables
    -- Let's switch from original to old
    DECLARE @toOldPartitions TABLE
    (
       SourcePartitionId INT,
       DestinationPartitionId INT
    )

    DECLARE @fromTempPartitions TABLE
    (
       SourcePartitionId INT,
       DestinationPartitionId INT
    )

    DECLARE @maxBoundary INT = (SELECT MAX(boundary_id) FROM sys.partition_range_values WHERE function_id = @originalPartitionFunctionId)

    IF @operation = 'SPLIT'
    BEGIN
       INSERT INTO @toOldPartitions
       SELECT TOP(1) boundary_id, @firstPartitionIdInTemp
       FROM sys.partition_range_values
       WHERE value > @partitionId
          AND function_id = @originalPartitionFunctionId
    END
    ELSE IF @operation = 'MERGE'
    BEGIN
       INSERT INTO @toOldPartitions
       SELECT boundary_id, @firstPartitionIdInTemp
       FROM sys.partition_range_values
       WHERE value = @partitionId
          AND function_id = @originalPartitionFunctionId

       INSERT INTO @toOldPartitions
       SELECT SourcePartitionId + 1, DestinationPartitionId + 1
       FROM @toOldPartitions
    END
    ELSE IF @operation = 'SORT'
    BEGIN
       INSERT INTO @toOldPartitions
       SELECT TOP(1) boundary_id, @firstPartitionIdInTemp
       FROM sys.partition_range_values
       WHERE (value > @partitionId OR (@operation = 'SORT' AND value = @partitionId))
          AND function_id = @originalPartitionFunctionId
    END

    IF NOT EXISTS (SELECT * FROM @toOldPartitions)
    BEGIN
        INSERT INTO @toOldPartitions
        SELECT @maxBoundary + 1, @firstPartitionIdInTemp
    END

    DECLARE @switchCommand NVARCHAR(MAX) = ''

    -- Switch main table to old table
    IF @firstSwapDone = 0
    BEGIN
        SELECT @switchCommand += 'ALTER TABLE '+TableName+' SWITCH PARTITION ' + CAST(SourcePartitionId AS VARCHAR(5))
              +' TO  '+TableName+'_Old PARTITION ' + CAST(DestinationPartitionId AS VARCHAR(5)) + @newLine
        FROM @toOldPartitions
        CROSS JOIN @mainTables

        EXEC(@switchCommand)
    END

    -- Double checking counts after partition switch
    SET @sizeCheckCommand = ''

    SET @mainTablesCursor = CURSOR LOCAL FORWARD_ONLY STATIC FOR
    SELECT  TableName FROM @mainTables

    OPEN @mainTablesCursor
    FETCH NEXT FROM @mainTablesCursor INTO @mainTableName;
    WHILE @@FETCH_STATUS = 0
    BEGIN
       SET @sizeCheckCommand = 'SELECT @countInTemp = COUNT_BIG(*) FROM '+ @mainTableName + '_Temp'
       EXEC sp_executesql @sizeCheckCommand, N'@countInTemp BIGINT OUTPUT', @countInTemp = @countInTemp OUTPUT

       SET @sizeCheckCommand = 'SELECT @countInOld = COUNT_BIG(*) FROM '+ @mainTableName + '_Old'
       EXEC sp_executesql @sizeCheckCommand, N'@countInOld BIGINT OUTPUT', @countInOld = @countInOld OUTPUT

       IF  @countInTemp != @countInOld
       BEGIN
          SET @status = 1670018
          SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @mainTableName, @countInOld, @countInTemp)
          RETURN
       END
       FETCH NEXT FROM @mainTablesCursor INTO @mainTableName;
    END

    CLOSE @mainTablesCursor
    DEALLOCATE @mainTablesCursor

    -- We don't have data between Bi and Bi+1
    -- Let's apply SPLIT/MERGE for original partition function
    DECLARE @splitCommand NVARCHAR(MAX)
    IF @operation = 'SPLIT'
    BEGIN
       SET @splitCommand = 'ALTER PARTITION FUNCTION ' + @originalPartitionFunctionName + '() SPLIT RANGE('+ CAST(@partitionId AS VARCHAR(10)) +')'
    END
    ELSE IF @operation = 'MERGE'
    BEGIN
       SET @splitCommand = 'ALTER PARTITION FUNCTION ' + @originalPartitionFunctionName + '() MERGE RANGE('+ CAST(@partitionId AS VARCHAR(10)) +')'
    END
    PRINT @splitCommand

    DECLARE @escalatedTables TABLE
    (
        EscalatedTableName VARCHAR(64)
    )

    IF @splitCommand IS NOT NULL
    BEGIN
       -- SPLIT or MERGE require X lock on all tables involved it could cause deadlocks
       -- We set priority to high to survive, but in case if SQL decided something else retrying
       DECLARE @attempts INT = 3
       WHILE @attempts > 0
       BEGIN
           BEGIN TRY
                -- TESTPATTERN: SPLIT
                EXEC(@splitCommand)
                SET @attempts = 0
                BREAK
           END TRY
           BEGIN CATCH
                IF ERROR_NUMBER() = 1205 -- deadlock
                BEGIN
                    SET @attempts -=1
                    IF (@attempts = 0)
                    BEGIN
                        -- We used to many retries, rethrow the error
                        THROW;
                    END
                    ELSE
                    BEGIN
                        -- We got deadlock, because transformations other partitions of the same tables are still running
                        -- Stopping transformations for everyone, it should be pretty brief period
                        DECLARE @transformTableName NVARCHAR(50)
                        SET @allTablesCursor = CURSOR LOCAL FORWARD_ONLY STATIC FOR
                        (
                            SELECT  TransformTableName FROM @mainTables
                        )

                        OPEN @allTablesCursor

                        WHILE(1 = 1)
                        BEGIN
                            FETCH NEXT FROM @allTablesCursor INTO @transformTableName;

                            IF @@FETCH_STATUS <> 0
                            BEGIN
                                BREAK
                            END

                            EXEC AnalyticsInternal.prc_iSetTransformHold @hold=1, @targetTableName= @transformTableName, @reason = 'Escalate to table level due to deadlock'
                            INSERT INTO @escalatedTables (EscalatedTableName) VALUES(@transformTableName)
                        END

                        CLOSE @allTablesCursor
                        DEALLOCATE @allTablesCursor

                        WAITFOR DELAY '00:00:15'
                    END
                END
                ELSE
                BEGIN
                    SET @attempts = 0;
                    THROW;
                END
           END CATCH
       END
    END

    -- Partition function is compatible with required scheme
    -- Let's switch temp to original
    IF @operation = 'SPLIT'
    BEGIN
       INSERT INTO @fromTempPartitions
       SELECT @firstPartitionIdInTemp, boundary_id
       FROM sys.partition_range_values
       WHERE value = @partitionId
          AND function_id = @originalPartitionFunctionId

       INSERT INTO @fromTempPartitions
       SELECT SourcePartitionId + 1, DestinationPartitionId + 1
       FROM @fromTempPartitions
    END
    ELSE IF @operation = 'MERGE'
    BEGIN
       INSERT INTO @fromTempPartitions
       SELECT TOP(1) @firstPartitionIdInTemp, boundary_id
       FROM sys.partition_range_values
       WHERE value > @partitionId
          AND function_id = @originalPartitionFunctionId
    END
    ELSE IF @operation = 'SORT'
    BEGIN
       INSERT INTO @fromTempPartitions
       SELECT TOP(1) @firstPartitionIdInTemp, boundary_id
       FROM sys.partition_range_values
       WHERE (value > @partitionId OR (@operation = 'SORT' AND value = @partitionId))
          AND function_id = @originalPartitionFunctionId
    END

    IF NOT EXISTS(SELECT * FROM @fromTempPartitions)
    BEGIN
        INSERT INTO @fromTempPartitions
        SELECT @firstPartitionIdInTemp, @maxBoundary + IIF(@operation = 'SORT', 1,0)
    END

    SET @switchCommand =''

    -- Switch main temp table to real table
    SELECT @switchCommand += 'ALTER TABLE '+TableName+'_Temp SWITCH PARTITION ' + CAST(SourcePartitionId AS VARCHAR(5))
          +' TO  '+TableName+' PARTITION ' + CAST(DestinationPartitionId AS VARCHAR(5)) + @newLine
    FROM @fromTempPartitions
    CROSS JOIN (SELECT TableName FROM @mainTables) T

    --PRINT @switchCommand

    EXEC(@switchCommand)

    -- Add Next section to the partition schema
    SET @switchCommand = 'ALTER PARTITION SCHEME ' +@partitionScheme+' NEXT USED [PRIMARY]' -- TODO: figure out another partition
    EXEC(@switchCommand)

    -- Clean up
    DECLARE @dropCommand NVARCHAR(MAX) = ''

    IF OBJECT_ID('prc_DisableRls') IS NOT NULL
    BEGIN
        SELECT @dropCommand += 'EXEC prc_DisableRls @schemaName = ''AnalyticsModel'', @tableName = ''' + TableShortName + '_Temp''' + @newLine
                              +'EXEC prc_DisableRls @schemaName = ''AnalyticsModel'', @tableName = ''' + TableShortName + '_Old''' + @newLine
        FROM (SELECT TableShortName, TableName FROM @mainTables) T
    END

    SELECT @dropCommand += 'DROP TABLE IF EXISTS ' + TableName + '_Temp ' + @newLine
                          +'DROP TABLE IF EXISTS ' + TableName + '_Old' + @newLine
    FROM (SELECT TableShortName, TableName FROM @mainTables) T

    SET @dropCommand += '
    DROP PARTITION SCHEME ' + @partitionScheme+'_Split;
    DROP PARTITION FUNCTION '+@originalPartitionFunctionName+'_Split;

    DROP PARTITION SCHEME ' + @partitionScheme+'_Merge;
    DROP PARTITION FUNCTION '+@originalPartitionFunctionName+'_Merge;
    '
    EXEC (@dropCommand)

    -- Release Holds Taken During Offline Processing
    SET @holdReason = CONCAT('End hold for ', @holdReason)

    DECLARE @heldTablesCursor CURSOR
    SET @heldTablesCursor = CURSOR LOCAL FORWARD_ONLY FOR
        SELECT  DISTINCT t.TransformTableName, MIN(p.StartPartitionId) AS StartPartitionId, MAX(p.EndPartitionId) AS EndPartitionId
        FROM    AnalyticsInternal.tbl_TableMaintenance m
        LEFT JOIN AnalyticsInternal.tbl_TableMaintenancePartition p
        ON      p.TableMaintenanceId = m.TableMaintenanceId
        JOIN    @mainTables t
        ON      t.TableMaintenanceId = m.TableMaintenanceId
        GROUP BY t.TransformTableName, t.TableMaintenanceId -- To match holds taken by prc_iProcessRecentBatchesOffline

    OPEN @heldTablesCursor

    DECLARE @heldTableName VARCHAR(64)
    DECLARE @heldStartPartitionId INT
    DECLARE @heldEndPartitionId INT

    WHILE(1 = 1)
    BEGIN
        FETCH NEXT FROM @heldTablesCursor INTO @heldTableName, @heldStartPartitionId, @heldEndPartitionId

        IF @@FETCH_STATUS <> 0
        BEGIN
            BREAK
        END

        EXEC AnalyticsInternal.prc_iSetTransformHold @hold = 0, @targetTableName = @heldTableName, @reason = @holdReason, @firstPartitionId = @heldStartPartitionId, @lastPartitionId = @heldEndPartitionId
    END

    CLOSE @heldTablesCursor
    DEALLOCATE @heldTablesCursor

    -- Release Escalated Holds
    SET @holdReason = 'End hold for escalate to table level due to deadlock.'

    DECLARE @escalatedTablesCursor CURSOR
    SET @escalatedTablesCursor = CURSOR LOCAL FORWARD_ONLY FOR
        SELECT  EscalatedTableName
        FROM    @escalatedTables

    OPEN @escalatedTablesCursor

    DECLARE @escalatedTabledName VARCHAR(64)

    WHILE(1 = 1)
    BEGIN
        FETCH NEXT FROM @escalatedTablesCursor INTO @escalatedTabledName

        IF @@FETCH_STATUS <> 0
        BEGIN
            BREAK
        END

        EXEC AnalyticsInternal.prc_iSetTransformHold @hold = 0, @targetTableName = @escalatedTabledName, @reason = @holdReason
    END

    CLOSE @escalatedTablesCursor
    DEALLOCATE @escalatedTablesCursor

    -- Update maintenance state
    UPDATE m
    SET    IsActive = 0
    FROM   AnalyticsInternal.tbl_TableMaintenance m
    JOIN   @mainTables t
    ON     t.TableMaintenanceId = m.TableMaintenanceId

    RETURN 0
END

GO

