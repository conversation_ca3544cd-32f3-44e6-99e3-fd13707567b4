/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 5F2989480C8829A501A317BADCA029A443AB4552
CREATE PROCEDURE AnalyticsInternal.prc_ReorganizePartition
    @tableName NVARCHAR(128),
    @columnStoreIndexName NVARCHAR(128),
    @partitionNumber INT,
    @compress BIT
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    SET DEADLOCK_PRIORITY LOW

    DECLARE @cmd NVARCHAR(MAX) = ''

    BEGIN

        SET @cmd = 'ALTER INDEX ' + @columnStoreIndexName +' ON ' + @tableName + ' REORGANIZE PARTITION = ' + CONVERT(NVARCHAR(12), @partitionNumber)

        IF(@compress = 1)
            SET @cmd = @cmd +' WITH (COMPRESS_ALL_ROW_GROUPS = ON)'

        EXEC (@cmd)

    END

END

GO

