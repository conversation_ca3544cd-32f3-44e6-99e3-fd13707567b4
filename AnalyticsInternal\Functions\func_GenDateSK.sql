/******************************************************************************************************
** Warning: the contents of this function are critical to its functioning.
** Modifying the contents of this function could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 5754DB23F27AEF84F3AFAF2F809E4C926FC4217D
CREATE FUNCTION AnalyticsInternal.func_GenDateSK(@date DATETIMEOFFSET)
RETURNS INT
AS
BEGIN
    RETURN IIF(YEAR(@date) >= 9999, NULL, YEAR(@date) * 10000 + MONTH(@date) * 100 + DAY(@date))
END

GO

GRANT EXECUTE
    ON OBJECT::[AnalyticsInternal].[func_GenDateSK] TO [VSODIAGROLE]
    AS [dbo];


GO

