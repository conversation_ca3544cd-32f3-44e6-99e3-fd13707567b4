CREATE PROCEDURE [dbo].[prc_DevShiftWorkItemDates]
    @partitionId INT,
    @workItemIds NVARCHAR(MAX) = NULL, -- comma-separated list, NULL for all
    @shiftDays INT = 0,
    @shiftHours INT = 0,
    @shiftMinutes INT = 0,
    @dryRun BIT = 1 -- 1 for preview, 0 for actual execution
AS
BEGIN
    SET NOCOUNT ON
    SET XACT_ABORT ON

    DECLARE @timeShift DATETIME2 = DATEADD(MINUTE, @shiftMinutes, 
                                  DATEADD(HOUR, @shiftHours, 
                                  DATEADD(DAY, @shiftDays, '1900-01-01')))
    DECLARE @shiftInterval BIGINT = DATEDIFF_BIG(SECOND, '1900-01-01', @timeShift)

    -- Create temp table for work item IDs
    CREATE TABLE #WorkItemIds (WorkItemId INT PRIMARY KEY)
    
    IF @workItemIds IS NULL
    BEGIN
        INSERT #WorkItemIds
        SELECT DISTINCT Id FROM dbo.tbl_WorkItemCoreLatest 
        WHERE PartitionId = @partitionId
    END
    ELSE
    BEGIN
        INSERT #WorkItemIds
        SELECT value FROM STRING_SPLIT(@workItemIds, ',')
        WHERE ISNUMERIC(value) = 1
    END

    IF @dryRun = 1
    BEGIN
        -- Preview changes
        SELECT 'WorkItemCoreLatest' AS TableName, COUNT(*) AS RecordsToUpdate
        FROM dbo.tbl_WorkItemCoreLatest w
        JOIN #WorkItemIds wi ON w.Id = wi.WorkItemId
        WHERE w.PartitionId = @partitionId

        UNION ALL

        SELECT 'WorkItemCoreWere' AS TableName, COUNT(*)
        FROM dbo.tbl_WorkItemCoreWere w
        JOIN #WorkItemIds wi ON w.Id = wi.WorkItemId
        WHERE w.PartitionId = @partitionId

        RETURN 0
    END

    BEGIN TRANSACTION

    -- Update current work items
    UPDATE w
    SET CreatedDate = DATEADD(SECOND, @shiftInterval, CreatedDate),
        ChangedDate = DATEADD(SECOND, @shiftInterval, ChangedDate),
        AuthorizedDate = CASE WHEN AuthorizedDate IS NOT NULL 
                             THEN DATEADD(SECOND, @shiftInterval, AuthorizedDate) 
                             ELSE NULL END,
        RevisedDate = CASE WHEN RevisedDate < '9999-01-01' 
                          THEN DATEADD(SECOND, @shiftInterval, RevisedDate) 
                          ELSE RevisedDate END
    FROM dbo.tbl_WorkItemCoreLatest w
    JOIN #WorkItemIds wi ON w.Id = wi.WorkItemId
    WHERE w.PartitionId = @partitionId

    -- Update work item history
    UPDATE w
    SET CreatedDate = DATEADD(SECOND, @shiftInterval, CreatedDate),
        ChangedDate = DATEADD(SECOND, @shiftInterval, ChangedDate),
        AuthorizedDate = CASE WHEN AuthorizedDate IS NOT NULL 
                             THEN DATEADD(SECOND, @shiftInterval, AuthorizedDate) 
                             ELSE NULL END,
        RevisedDate = CASE WHEN RevisedDate < '9999-01-01' 
                          THEN DATEADD(SECOND, @shiftInterval, RevisedDate) 
                          ELSE RevisedDate END
    FROM dbo.tbl_WorkItemCoreWere w
    JOIN #WorkItemIds wi ON w.Id = wi.WorkItemId
    WHERE w.PartitionId = @partitionId

    -- Update custom field history
    UPDATE c
    SET AddedDate = DATEADD(SECOND, @shiftInterval, AddedDate),
        RemovedDate = CASE WHEN RemovedDate < '9999-01-01' 
                          THEN DATEADD(SECOND, @shiftInterval, RemovedDate) 
                          ELSE RemovedDate END
    FROM dbo.tbl_WorkItemCustomWere c
    JOIN #WorkItemIds wi ON c.ID = wi.WorkItemId
    WHERE c.PartitionId = @partitionId

    -- Update file attachments
    UPDATE f
    SET AddedDate = DATEADD(SECOND, @shiftInterval, AddedDate),
        RemovedDate = CASE WHEN RemovedDate < '9999-01-01' 
                          THEN DATEADD(SECOND, @shiftInterval, RemovedDate) 
                          ELSE RemovedDate END
    FROM dbo.WorkItemFiles f
    JOIN #WorkItemIds wi ON f.ID = wi.WorkItemId
    WHERE f.PartitionId = @partitionId

    COMMIT TRANSACTION

    SELECT 'Date shift completed successfully' AS Result
END