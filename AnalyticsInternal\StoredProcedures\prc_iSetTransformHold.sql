/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 0693D6590B7FBDB13466E4BF9B2325230A942CC5
CREATE PROCEDURE AnalyticsInternal.prc_iSetTransformHold
    @hold BIT,
    @reason NVARCHAR(255),
    @targetTableName VARCHAR(64) = NULL,
    @firstPartitionId INT = NULL,
    @lastPartitionId INT = NULL,
    @wait BIT = 1 -- Wait for active transforms to end by default
AS
BEGIN
    DECLARE @now DATETIME2 = SYSUTCDATETIME()
    SET @hold = ISNULL(@hold, 0)

    IF (@firstPartitionId IS NULL)
    BEGIN
        -- set the default hold value for accounts yet to create transform state
        UPDATE  AnalyticsInternal.tbl_TransformDefinition
        SET     Hold = @hold,
                HoldChangedTime = @now,
                HoldReason = @reason
        WHERE   (@targetTableName IS NULL OR TargetTableName = @targetTableName)
                AND (Hold <> @hold OR Hold IS NULL)

        DECLARE @partitionIdStart INT = 0
        DECLARE @completed TABLE (PartitionId INT)

        WHILE (1 = 1)
        BEGIN
            DELETE @completed

            ;WITH TState AS
            (
                SELECT  TOP (100) WITH TIES * -- limit number of partitionId touched to avoid excessive locks
                FROM    AnalyticsInternal.tbl_TransformState
                WHERE   PartitionId > @partitionIdStart
                        AND ISNULL(@targetTableName, TargetTableName) = TargetTableName
                        AND (Hold <> @hold OR Hold IS NULL)
                ORDER BY PartitionId ASC
            )
            UPDATE  TState
            SET     Hold = @hold,
                    HoldChangedTime = @now,
                    HoldReason = @reason
            OUTPUT  INSERTED.PartitionId
            INTO    @completed

            IF (@@ROWCOUNT = 0 AND @partitionIdStart = 0) -- ensure one pass over whole table
            BEGIN
                BREAK
            END

            SELECT @partitionIdStart = ISNULL(MAX(PartitionId), 0) FROM @completed
        END

        IF (@hold = 1 AND @wait = 1)
        BEGIN
            -- Make sure that all transformations and staging to modified tables are done
            -- Using different SQL for case when partition isn't set to avoid parameter sniffing
            WHILE EXISTS(SELECT * FROM AnalyticsInternal.tbl_Batch
                         WHERE OperationActive = 1
                              AND PartitionId > 0
                              AND Ready = 0 AND Failed = 0 AND OperationTriggerTableName IS NOT NULL -- Care only about transfrom batches there
                              AND (TableName = @targetTableName OR @targetTableName IS NULL)
                )
            BEGIN
                -- We have active transform => sleep for 5 seconds and check again
                WAITFOR DELAY '00:00:05'
            END
        END

    END
    ELSE
    BEGIN
        -- call prc_iEnsureTransformState to make sure transform state exists
        DECLARE @partition TABLE (PartitionId INT)

        INSERT  @partition
        SELECT  DISTINCT PartitionId
        FROM    dbo.tbl_DatabasePartitionMap
        WHERE   PartitionId BETWEEN @firstPartitionId AND ISNULL(@lastPartitionId, @firstPartitionId)

        DECLARE @partitionId INT

        WHILE (1 = 1)
        BEGIN
            SELECT  TOP 1 @partitionId = PartitionId
            FROM    @partition

            IF (@@ROWCOUNT = 0)
            BEGIN
                BREAK
            END

            EXEC AnalyticsInternal.prc_iEnsureTransformState @partitionId, @definitionsOnly = 1

            DELETE  @partition
            WHERE   PartitionId = @partitionId
        END

        WHILE (1 = 1)
        BEGIN
            UPDATE  TOP (100) AnalyticsInternal.tbl_TransformState
            SET     Hold = @hold,
                    HoldChangedTime = @now,
                    HoldReason = @reason
            WHERE   PartitionId BETWEEN @firstPartitionId AND ISNULL(@lastPartitionId, @firstPartitionId)
                    AND ISNULL(@targetTableName, TargetTableName) = TargetTableName
                    AND (Hold <> @hold OR Hold IS NULL)

            IF (@@ROWCOUNT = 0)
                BREAK
        END

        IF (@hold = 1 AND @wait = 1)
        BEGIN
            -- Make sure that all transformations and staging to modified tables are done
            WHILE EXISTS(SELECT * FROM AnalyticsInternal.tbl_Batch
                         WHERE OperationActive = 1
                              AND Ready = 0 AND Failed = 0 AND OperationTriggerTableName IS NOT NULL -- Care only about transfrom batches there
                              AND PartitionId BETWEEN @firstPartitionId AND ISNULL(@lastPartitionId, @firstPartitionId)
                              AND (TableName = @targetTableName OR @targetTableName IS NULL)
                )
            BEGIN
                -- We have active transform => sleep for 5 seconds and check again
                WAITFOR DELAY '00:00:05'
            END
        END

    END

    RETURN 0
END

GO

