/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: EA34F5CEFCFEAD2B7788979039232DB011934B82
CREATE PROCEDURE AnalyticsInternal.prc_RecordTableOperation
    @partitionId    INT,
    @tableName VARCHAR(64),
    @providerShardId INT,
    @streamId        INT,
    @operation VARCHAR(10)
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)
    DECLARE @shortProcName VARCHAR(100) = OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @batchDt DATETIME = GETUTCDATE()
    DECLARE @newBatchId BIGINT

    EXEC AnalyticsInternal.prc_iCreateBatch
        @partitionId,
        @tableName,
        @providerShardId,
        @streamId,
        @operation,
        @shortProcName,
        NULL, -- sproc version
        NULL, -- operation priority
        @operationTriggerTableName = NULL,
        @operationTriggerBatchIdStart = NULL,
        @operationTriggerBatchIdEnd = NULL,
        @batchDt = @batchDt,
        @newBatchId = @newBatchId OUTPUT

    EXEC AnalyticsInternal.prc_iUpdateBatch
        @partitionId,
        @newBatchId,
        @operationState = NULL,
        @operationStateData = NULL,
        @operationActive = false,
        @ready = true,
        @failed = false,
        @incFailedCount = NULL,
        @failedMessage = NULL,
        @attemptCount = NULL,
        @incInsertedCount = NULL,
        @incUpdatedCount = NULL,
        @incDeletedCount = NULL,
        @incOperationSubBatchCount = NULL,
        @incOperationDurationMS = NULL,
        @tableLoading = 0

END

GO

