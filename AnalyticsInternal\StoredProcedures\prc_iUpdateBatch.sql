/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 59A1D0C8B2858E6143269E32C4EC34E5F660406A
CREATE PROCEDURE AnalyticsInternal.prc_iUpdateBatch
    @partitionId    INT,
    @batchId BIGINT,
    @operationState VARCHAR(10),
    @operationStateData BIGINT,
    @operationActive BIT,
    @ready BIT,
    @failed BIT,
    @failedMessage NVARCHAR(1000),
    @incFailedCount INT,
    @attemptCount INT,
    @incInsertedCount INT,
    @incUpdatedCount INT,
    @incDeletedCount INT,
    @incOperationSubBatchCount INT,
    @incOperationDurationMS INT,
    @tableLoading BIT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    DECLARE @now DATETIME = GETUTCDATE()

    UPDATE AnalyticsInternal.tbl_Batch WITH (READPAST, ROWLOCK, XLOCK)
    SET OperationState = ISNULL(@operationState, OperationState),
        OperationStateData = ISNULL(@operationStateData, OperationStateData),
        OperationActive = ISNULL(@operationActive, OperationActive),
        Ready = ISNULL(@ready, Ready),
        Failed = ISNULL(@failed, Failed),
        FailedMessage = ISNULL(@failedMessage, FailedMessage),
        AttemptCount = ISNULL(@attemptCount, AttemptCount),
        FailedCount = CASE WHEN @incFailedCount IS NOT NULL THEN ISNULL(FailedCount, 0) + @incFailedCount ELSE FailedCount END,
        InsertedCount = CASE WHEN @incInsertedCount IS NOT NULL THEN (SELECT MIN(NewCount) FROM (VALUES (CAST(ISNULL(InsertedCount, 0) AS BIGINT) + @incInsertedCount), (2147483647)) AS V(NewCount)) ELSE InsertedCount END,
        UpdatedCount = CASE WHEN @incUpdatedCount IS NOT NULL THEN (SELECT MIN(NewCount) FROM (VALUES (CAST(ISNULL(UpdatedCount, 0) AS BIGINT) + @incUpdatedCount), (2147483647)) AS V(NewCount)) ELSE UpdatedCount END,
        DeletedCount = CASE WHEN @incDeletedCount IS NOT NULL THEN (SELECT MIN(NewCount) FROM (VALUES (CAST(ISNULL(DeletedCount, 0) AS BIGINT) + @incDeletedCount), (2147483647)) AS V(NewCount)) ELSE DeletedCount END,
        Inserted =
            CASE WHEN ISNULL(@incInsertedCount, InsertedCount) IS NOT NULL THEN IIF(@incInsertedCount > 0 OR InsertedCount > 0, 1, 0)
                 WHEN ISNULL(@incUpdatedCount, UpdatedCount) IS NULL AND ISNULL(@incDeletedCount, DeletedCount) IS NULL AND Operation IN ('replace', 'insert', 'merge', 'reprocess') THEN 1
                 ELSE 0
            END,
        Updated =
            CASE WHEN ISNULL(@incUpdatedCount, UpdatedCount) IS NOT NULL THEN IIF(@incUpdatedCount > 0 OR UpdatedCount > 0, 1, 0)
                 WHEN ISNULL(@incInsertedCount, InsertedCount) IS NULL AND ISNULL(@incDeletedCount, DeletedCount) IS NULL AND Operation IN ('replace', 'update', 'merge', 'reprocess') THEN 1
                 ELSE 0
            END,
        Deleted =
            CASE WHEN ISNULL(@incDeletedCount, DeletedCount) IS NOT NULL THEN IIF(@incDeletedCount > 0 OR DeletedCount > 0, 1, 0)
                 WHEN ISNULL(@incUpdatedCount, UpdatedCount) IS NULL AND ISNULL(@incInsertedCount, InsertedCount) IS NULL AND Operation IN ('replace', 'delete', 'reprocess') THEN 1
                 ELSE 0
            END,
        ReadyDateTime = CASE WHEN @ready = 1 THEN ISNULL(ReadyDateTime, @now) ELSE ReadyDateTime END,
        OperationSubBatchCount = CASE WHEN @incOperationSubBatchCount IS NOT NULL THEN ISNULL(OperationSubBatchCount, 0) + @incOperationSubBatchCount ELSE OperationSubBatchCount END,
        OperationDurationMS =
            CASE WHEN @incOperationDurationMS IS NOT NULL THEN ISNULL(OperationDurationMS, 0) + @incOperationDurationMS
                 WHEN @ready = 1 AND OperationDurationMS IS NULL THEN DATEDIFF(millisecond, CreateDateTime, @now)
                 ELSE OperationDurationMS
            END,
        LastFailedOperationSubBatchCount = IIF(@failed = 1, OperationSubBatchCount, LastFailedOperationSubBatchCount)
    WHERE PartitionId = @partitionId AND BatchId = @batchId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    IF (@ready = 1 OR @failed = 1)
        EXEC AnalyticsInternal.prc_iUpdateTransformStateFromBatch @partitionId, @batchId, @tableLoading

END

GO

