/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 9703E81BFA1428274B3945B1726110B6A8340C68
CREATE PROCEDURE AnalyticsInternal.prc_CloneOrReplaceIndex
    @indexName NVARCHAR(256),
    @tableName NVARCHAR(256),
    @partitionScheme NVARCHAR(256),
    @onTableName NVARCHAR(256) = NULL
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
   SET NOCOUNT     ON
   SET XACT_ABORT  ON

   DECLARE @status INT
   DECLARE @tfError NVARCHAR(255)
   DECLARE @errorMessage NVARCHAR(255)
   DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

   DECLARE @now DATETIME = GETUTCDATE()

   DECLARE @supportCompression BIT = IIF(EXISTS(
                        SELECT  *
                        FROM    sys.indexes i
                        JOIN    sys.partitions p
                        ON      i.object_id = p.object_id
                                AND i.index_id = p.index_id
                        WHERE   i.object_id = OBJECT_ID(@tableName)
                                AND i.name = @indexName
                                AND p.data_compression_desc = 'PAGE'), 1, 0)

   DECLARE @newLine CHAR(2) = CHAR(13)+CHAR(10)
   DECLARE @isUnique BIT
   DECLARE @isClustered BIT
   DECLARE @isColumnstore BIT
   DECLARE @indexType NVARCHAR(256)
   DECLARE @supportOnline BIT

   SELECT  @isUnique = is_unique,
           @isClustered = IIF(type = 1 OR type = 5, 1,0),
           @isColumnstore = IIF(type = 5 OR type = 6, 1,0),
           @indexType = type_desc
   FROM    sys.indexes WHERE name = @indexName
           AND object_id=OBJECT_ID(@tableName)

   SET @supportOnline = IIF(@supportCompression = 1
                        AND NOT EXISTS(SELECT * FROM sys.indexes WHERE object_id=OBJECT_ID(@tableName) AND type = 5)
                        -- nCCI doesn't support online operations
                        AND NOT(@isClustered = 0 AND @isColumnstore = 1), 1, 0)

   IF @isUnique IS NULL
   BEGIN
      SET @status = 1670011
      SET @tfError = dbo.func_GetMessage(@status); RAISERROR(@tfError, 16, -1, @procedureName, @indexName, @tableName)
      RETURN
   END

   DECLARE @indexCmd NVARCHAR(MAX) = 'CREATE '

   IF @isUnique = 1
       SET @indexCmd += ' UNIQUE '

   SET @indexCmd += ' ' + @indexType + ' '

   SET @indexCmd += 'INDEX ' + QUOTENAME(@indexName) + ' ON ' + ISNULL(@onTableName,@tableName) + @newLine

   IF NOT(@isClustered = 1 AND @isColumnstore = 1)
   BEGIN
       SET @indexCmd += '('
       SELECT  @indexCmd +=  c.name +','
       FROM    sys.index_columns ic
       JOIN    sys.indexes i
       ON      ic.index_id = i.index_id
               AND ic.object_id=i.object_id
       JOIN    sys.columns c
       ON      ic.column_id=c.column_id
               AND c.object_id=i.object_id
       WHERE   i.name = @indexName
               AND i.object_id=OBJECT_ID(@tableName)
               AND (ic.is_included_column = 0 OR (@isClustered = 0 AND @isColumnstore = 1))
       ORDER BY ic.key_ordinal

       SET @indexCmd = SUBSTRING(@indexCmd, 1, LEN(@indexCmd) - 1) + ')'
   END

   IF @isClustered = 0 AND @isColumnstore = 0
   BEGIN
       DECLARE @includePart NVARCHAR(MAX) = ''
       SELECT
       @includePart += @newLine + c.name +','
       FROM sys.index_columns ic
       JOIN sys.indexes i
       ON ic.index_id=i.index_id and ic.object_id=i.object_id
       JOIN sys.columns c
       ON ic.column_id=c.column_id and c.object_id=i.object_id
       WHERE i.name=@indexName AND i.object_id=OBJECT_ID(@tableName)
           AND ic.is_included_column = 1
       ORDER BY ic.key_ordinal

       IF LEN(@includePart) > 0
           SET @indexCmd += @newLine + 'INCLUDE(' + SUBSTRING(@includePart, 1, LEN(@includePart) - 1) + ')'

       DECLARE @filterPart NVARCHAR(MAX) = NULL
       SELECT  @filterPart = filter_definition
       FROM    sys.indexes i
       WHERE   i.name=@indexName
               AND i.object_id=OBJECT_ID(@tableName)

       IF @filterPart IS NOT NULL
       BEGIN
           SET @indexCmd += ' WHERE ' + @filterPart + ' '
       END
   END

   DECLARE @withOptions NVARCHAR(MAX) = NULL

   If @onTableName IS NULL
   BEGIN
       SET @withOptions = 'DROP_EXISTING = ON'
   END

   IF @supportCompression = 1 AND @isColumnstore = 0
   BEGIN
       SET @withOptions = ISNULL(@withOptions +', ','') + 'DATA_COMPRESSION = PAGE'
   END

   IF @supportOnline = 1
   BEGIN
       SET @withOptions = ISNULL(@withOptions +', ','') + 'ONLINE = ON'
   END

   IF @withOptions IS NOT NULL
   BEGIN
        SET @indexCmd += ' WITH (' + @withOptions + ')'
   END

   IF @partitionScheme IS NOT NULL
   BEGIN
       SET @indexCmd += ' ON '+@partitionScheme +'(PartitionId)'
   END
   ELSE
   BEGIN
      SET @indexCmd += ' ON [PRIMARY]'
   END

   --PRINT @indexCmd

   EXEC (@indexCmd)

   RETURN 0
END

GO

