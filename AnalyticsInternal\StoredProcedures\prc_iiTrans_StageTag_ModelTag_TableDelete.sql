/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 123FD7A916C2AE3F39DB9D9C142A538901F861F8
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_StageTag_ModelTag_TableDelete
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 100000)

    CREATE TABLE #DeletedTags
    (
        TagSK INT NOT NULL
    )

    INSERT  #DeletedTags
    SELECT  t.TagSK
    FROM    AnalyticsModel.tbl_Tag t
    LEFT JOIN AnalyticsStage.tbl_Tag s
    ON      s.PartitionId = t.PartitionId
            AND s.TagId = t.TagId
    WHERE   t.PartitionId = @partitionId
            AND t.TagId IS NOT NULL
            AND s.TagId IS NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    -- set the name of tags that are no longer in stage to NULL
    UPDATE  TOP (@batchSizeMax) t
    SET     t.AnalyticsUpdatedDate = @batchDt,
            t.AnalyticsBatchId = @batchId,
            t.TagName = NULL
    FROM    #DeletedTags d
    JOIN    AnalyticsModel.tbl_Tag t
    ON      t.PartitionId = @partitionId
            AND t.TagSK = d.TagSK
            AND t.TagName IS NOT NULL
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @updatedCount = @@ROWCOUNT

    -- exclude those tags from deletion that still have relationships with work items
    DELETE  d
    FROM    #DeletedTags d
    JOIN    AnalyticsModel.tbl_WorkItemTag wit
    ON      wit.PartitionId = @partitionId
            AND wit.TagSK = d.TagSK
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    DELETE  TOP (@batchSizeMax) t
    FROM    AnalyticsModel.tbl_Tag t
    JOIN    #DeletedTags d
    ON      d.TagSK = t.TagSK
    WHERE   t.PartitionId = @partitionId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @deletedCount = @@ROWCOUNT

    SET @complete = IIF(@deletedCount < @batchSizeMax AND @updatedCount < @batchSizeMax, 1, 0)

    DROP TABLE #DeletedTags

    RETURN 0
END

GO

