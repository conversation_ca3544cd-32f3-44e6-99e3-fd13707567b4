CREATE TABLE [AnalyticsModel].[tbl_WorkItemDescendantHistory] (
    [PartitionId]            INT                NOT NULL,
    [AnalyticsCreatedDate]   DATETIME2 (7)      NOT NULL,
    [AnalyticsUpdatedDate]   DATETIME2 (7)      NOT NULL,
    [AnalyticsBatchId]       BIGINT             NOT NULL,
    [WorkItemId]             INT                NOT NULL,
    [DescendantWorkItemId]   INT                NOT NULL,
    [Depth]                  INT                NOT NULL,
    [CreatedDate]            DATETIMEOFFSET (7) NOT NULL,
    [DeletedDate]            DATETIMEOFFSET (7) NOT NULL,
    [CreatedDateSK]          INT                NULL,
    [DeletedDateSK]          INT                NULL,
    [IsCurrent]              BIT                NOT NULL,
    [IsLastRevisionOfPeriod] INT                NOT NULL
);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_tbl_WorkItemDescendantHistory_Current]
    ON [AnalyticsModel].[tbl_WorkItemDescendantHistory]([PartitionId] ASC, [WorkItemId] ASC, [DescendantWorkItemId] ASC) WHERE ([IsCurrent]=(1)) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_tbl_WorkItemDescendantHistory_CurrentReverse]
    ON [AnalyticsModel].[tbl_WorkItemDescendantHistory]([PartitionId] ASC, [DescendantWorkItemId] ASC, [WorkItemId] ASC) WHERE ([IsCurrent]=(1)) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE CLUSTERED INDEX [PK_tbl_WorkItemDescendantHistory]
    ON [AnalyticsModel].[tbl_WorkItemDescendantHistory]([PartitionId] ASC, [WorkItemId] ASC, [DescendantWorkItemId] ASC, [CreatedDate] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

CREATE UNIQUE NONCLUSTERED INDEX [UQ_tbl_WorkItemDescendantHistory_Reverse]
    ON [AnalyticsModel].[tbl_WorkItemDescendantHistory]([PartitionId] ASC, [DescendantWorkItemId] ASC, [WorkItemId] ASC, [CreatedDate] ASC) WITH (DATA_COMPRESSION = PAGE);


GO

