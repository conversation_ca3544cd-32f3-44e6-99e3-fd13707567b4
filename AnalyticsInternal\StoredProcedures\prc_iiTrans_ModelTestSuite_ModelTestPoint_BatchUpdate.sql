/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: 563B6B0104E24C0FFF2A7F4A13AE81D53F904C85
CREATE PROCEDURE AnalyticsInternal.prc_iiTrans_ModelTestSuite_ModelTestPoint_BatchUpdate
    @partitionId INT,
    @batchId BIGINT,
    @batchDt DATETIME2,
    @settings typ_KeyValuePairStringTable READONLY,
    @triggerTableName VARCHAR(64),
    @triggerBatchIdStart BIGINT,
    @triggerBatchIdEnd BIGINT,
    @state VARCHAR(10),
    @stateData BIGINT,
    @attemptCount INT,
    @subBatchCount INT,
    @lastFailedSubBatchCount INT,
    @failedCount INT,
    @endState VARCHAR(10) OUTPUT,
    @endStateData BIGINT OUTPUT,
    @complete BIT OUTPUT,
    @insertedCount INT OUTPUT,
    @updatedCount INT OUTPUT,
    @deletedCount INT OUTPUT
AS
BEGIN
    SET NOCOUNT     ON
    SET XACT_ABORT  ON

    DECLARE @status INT
    DECLARE @tfError NVARCHAR(255)

    DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

    IF (@@TRANCOUNT = 0)
    BEGIN
        SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
        RETURN 1670002
    END

    DECLARE @batchSizeMax INT = ISNULL((SELECT TOP 1 Value FROM @settings WHERE [Key] = 'BatchSizeMax'), 10000)

    DECLARE @changes TABLE
    (
        MergeAction NVARCHAR(10)
    );

    CREATE TABLE #ChangedTestPoints
    (
        TestPointId INT NOT NULL,
        TestSuiteId INT NOT NULL,
        TestPlanId  INT NOT NULL,
        TestSuiteSK INT NOT NULL,
        ProjectSK   UNIQUEIDENTIFIER NOT NULL,
        IsDeleted   BIT NOT NULL
    )

    INSERT #ChangedTestPoints
    (
        TestPointId,
        TestSuiteId,
        TestPlanId,
        TestSuiteSK,
        ProjectSK,
        IsDeleted
    )
    SELECT TOP (@batchSizeMax) WITH TIES
          p.TestPointId,
          p.TestSuiteId,
          p.TestPlanId,
          mts.TestSuiteSK,
          mts.ProjectSK,
          (p.IsDeleted | ~p.Enabled | mts.IsDeleted | IIF(mts.TestPlanState = 255, 1, 0)) -- Set IsDeleted for points to 1 for points lying under deleted test plans / test suites too
    FROM  AnalyticsModel.tbl_TestSuite mts WITH (INDEX (IX_tbl_TestSuite_AnalyticsBatchId), FORCESEEK)
    JOIN  AnalyticsStage.tbl_TestPoint p WITH (INDEX (IX_tbl_TestPoint_TestPlanId_TestSuiteId_TestPointId), FORCESEEK)
    ON    p.PartitionId = mts.PartitionId
          AND p.TestPlanId = mts.TestPlanId
          AND p.TestSuiteId = mts.TestSuiteId
    WHERE mts.PartitionId = @partitionId
          AND mts.AnalyticsBatchId BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
          AND mts.TestSuiteId > ISNULL(@stateData, 0)
    ORDER BY mts.TestSuiteId
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

    SET @complete = CASE WHEN @@ROWCOUNT < @batchSizeMax THEN 1 ELSE 0 END
    SET @endStateData = (SELECT MAX(TestSuiteId) FROM #ChangedTestPoints)

    UPDATE t
    SET    AnalyticsUpdatedDate = @batchDt,
           AnalyticsBatchId     = @batchId,
           ProjectSK            = s.ProjectSK,
           TestSuiteSK          = s.TestSuiteSK,
           IsDeleted            = s.IsDeleted
    FROM   #ChangedTestPoints s
    JOIN   AnalyticsModel.tbl_TestPoint t WITH (INDEX (IX_tbl_TestPoint_TestPlanId_TestPointId), FORCESEEK)
    ON     t.PartitionId = @partitionId
           AND t.TestPlanId  = s.TestPlanId
           AND t.TestPointId = s.TestPointId
    WHERE  NOT EXISTS
           (
               SELECT
               s.ProjectSK,
               s.TestSuiteSK,
               s.IsDeleted
               INTERSECT
               SELECT
               t.ProjectSK,
               t.TestSuiteSK,
               t.IsDeleted
            )
    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN));

    SET @updatedCount = @@ROWCOUNT

    DROP TABLE #ChangedTestPoints

    RETURN 0
END

GO

