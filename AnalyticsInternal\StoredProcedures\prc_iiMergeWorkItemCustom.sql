/******************************************************************************************************
** Warning: the contents of this stored procedure are critical to its functioning.
** Modifying the contents of this stored procedure could result in data corruption, performance issues,
** or other bugs in your Azure DevOps Server deployment.
** DO NOT MODIFY IT.
******************************************************************************************************/
-- Hash: A910C8C0C616D2F96C824044682FDD657300B1EA
CREATE PROCEDURE AnalyticsInternal.prc_iiMergeWorkItemCustom
    @partitionId            INT,
    @batchId                INT,
    @batchDt                DATETIME2,
    @triggerBatchIdStart    BIGINT,
    @triggerBatchIdEnd      BIGINT,
    @triggerWorkItemId      AnalyticsInternal.typ_WorkItemId READONLY
WITH EXECUTE AS 'VssfAdmin'
AS
BEGIN
        DECLARE @status INT
        DECLARE @tfError NVARCHAR(255)

        DECLARE @procedureName SYSNAME =  @@SERVERNAME + '.' + DB_NAME() + '.' + OBJECT_SCHEMA_NAME(@@PROCID) + '.' + OBJECT_NAME(@@PROCID)

        IF (@@TRANCOUNT = 0)
        BEGIN
            SET @tfError = dbo.func_GetMessage(1670002); RAISERROR(@tfError, 16, -1, @procedureName)
            RETURN 1670002
        END

        --Get the collection time zone
        DECLARE @timeZone NVARCHAR(128) = AnalyticsInternal.func_GetPartitionTimeZone(@partitionId)

        CREATE TABLE #WorkItems
        (
            WorkItemId INT NOT NULL,
            Revision INT NOT NULL,
            WorkItemRevisionSK INT NOT NULL,
            PRIMARY KEY CLUSTERED (WorkItemRevisionSK)
        )

        CREATE TABLE #ChangedWorkItems -- dupes expected, no PK
        (
            WorkItemId INT,
            Revision INT
        )

        INSERT  #WorkItems (WorkItemId, Revision, WorkItemRevisionSK)
        SELECT  s.System_Id,
                s.System_Rev,
                sk.WorkItemRevisionSK
        FROM    @triggerWorkItemId tid
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevision s WITH (INDEX (CI_tbl_WorkItemRevision))
        ON      tid.System_Id = s.System_Id
        INNER LOOP JOIN
        (
            SELECT  r.PartitionId,
                    r.WorkItemId,
                    r.Revision,
                    r.WorkItemRevisionSK
            FROM    AnalyticsModel.tbl_WorkItemHistory r WITH (INDEX (IX_tbl_WorkItemHistory_WorkItemIdRev))
            UNION ALL
            SELECT  r.PartitionId,
                    r.WorkItemId,
                    r.Revision,
                    r.WorkItemRevisionSK
            FROM    AnalyticsModel.tbl_WorkItem r WITH (INDEX (IX_tbl_WorkItem_WorkItemIdRev))
        ) sk
        ON      sk.PartitionId = s.PartitionId
                AND sk.WorkItemId = s.System_Id
                AND sk.Revision = s.System_Rev
        WHERE   s.PartitionId = @partitionId
                AND s.AnalyticsBatchIdChanged BETWEEN @triggerBatchIdStart AND @triggerBatchIdEnd
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        CREATE TABLE #ExtendedField
        (
            System_Id           INT NOT NULL,
            System_Rev          INT NOT NULL,
            WorkItemRevisionSK  INT NOT NULL,
            FieldName           NVARCHAR(256) COLLATE DATABASE_DEFAULT,
            ModelTableName      NVARCHAR(50) COLLATE DATABASE_DEFAULT,
            [Value]             SQL_VARIANT,
            PRIMARY KEY CLUSTERED (System_Id, System_Rev, FieldName)
        )
        WITH (DATA_COMPRESSION=ROW)

        INSERT  #ExtendedField (System_Id, System_Rev, WorkItemRevisionSK, FieldName, [Value])
        SELECT  x.System_Id,
                x.System_Rev,
                t.WorkItemRevisionSK,
                FieldName,
                COALESCE(CAST(ValueString AS SQL_VARIANT), CAST(ValueGuid AS SQL_VARIANT), CAST(ValueInt AS SQL_VARIANT), CAST(ValueFloat AS SQL_VARIANT), CAST(ValueDateTime AS SQL_VARIANT), CAST(CAST(ValueObject AS NVARCHAR(4000)) AS SQL_VARIANT)) AS [Value]
        FROM    #WorkItems t
        INNER LOOP JOIN AnalyticsStage.tbl_WorkItemRevisionExtended x
        ON      t.WorkItemId = x.System_Id
                AND t.Revision = x.System_Rev
        JOIN    AnalyticsInternal.tbl_Fields f
        ON      f.PartitionId = x.PartitionId
                AND f.FieldSK = x.FieldSK
        WHERE   x.PartitionId = @partitionId
        OPTION  (OPTIMIZE FOR (@partitionId UNKNOWN))

        CREATE TABLE #ProcessField
        (
            SourceFieldName     NVARCHAR(256)   COLLATE DATABASE_DEFAULT NOT NULL,
            ModelTableName      NVARCHAR(50)    COLLATE DATABASE_DEFAULT NOT NULL,
            ModelColumnName     NVARCHAR(50)    COLLATE DATABASE_DEFAULT NOT NULL,
            FieldType           NVARCHAR(50)    COLLATE DATABASE_DEFAULT NOT NULL,
            IsHistoryEnabled    BIT NOT NULL,
            PRIMARY KEY CLUSTERED (SourceFieldName ASC)
        )
        WITH (DATA_COMPRESSION=ROW)

        INSERT  #ProcessField
        SELECT  DISTINCT
                SourceFieldName,
                ModelTableName,
                ModelColumnName,
                FieldType,
                IsHistoryEnabled
        FROM    (SELECT DISTINCT FieldName FROM #ExtendedField) e
        INNER HASH JOIN AnalyticsInternal.tbl_ProcessField pf
        ON      e.FieldName = SourceFieldName
        WHERE   PartitionId = @partitionId
                AND IsSystem = 0
                AND IsDeleted = 0
                AND ModelTableName IS NOT NULL
        OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

        UPDATE x
        SET    ModelTableName = pf.ModelTableName
        FROM   #ExtendedField x
        LEFT JOIN #ProcessField pf
        ON      pf.SourceFieldName = FieldName

        -- List of tables that we need to fill
        DECLARE @CustomTables TABLE
        (
            ModelTableName NVARCHAR(50) PRIMARY KEY
        )

        INSERT  @CustomTables
        SELECT  DISTINCT ModelTableName
        FROM    #ProcessField

        CREATE TABLE #WorkItemsToUpdate
        (
            WorkItemId INT NOT NULL,
            Revision INT NOT NULL,
            WorkItemRevisionSK INT NOT NULL,
            PRIMARY KEY CLUSTERED (WorkItemRevisionSK),
            INDEX IX_IdRev NONCLUSTERED (WorkItemId, Revision)
        )

        CREATE TABLE #WorkItemsToInsert
        (
            WorkItemId INT NOT NULL,
            Revision INT NOT NULL,
            WorkItemRevisionSK INT NOT NULL,
            PRIMARY KEY CLUSTERED (WorkItemRevisionSK),
            INDEX IX_IdRev NONCLUSTERED (WorkItemId, Revision)
        )

        -- Iterate via tables and fill them (without cursors)
        WHILE EXISTS(SELECT * FROM @CustomTables)
        BEGIN
            DECLARE @customTableName NVARCHAR(50)

            SELECT  TOP(1) @customTableName = ModelTableName
            FROM    @CustomTables
            ORDER BY ModelTableName

            DECLARE @cmd NVARCHAR(MAX)
            DECLARE @newLine CHAR(1) = CHAR(10)
            DECLARE @createColumns NVARCHAR(MAX) = ''
            DECLARE @pivotSelectClause NVARCHAR(MAX) = ''
            DECLARE @pivotAlias NVARCHAR(MAX) = ''
            DECLARE @updateClause NVARCHAR(MAX) = ''
            DECLARE @columns NVARCHAR(MAX) = ''
            DECLARE @sColumns NVARCHAR(MAX) = ''
            DECLARE @sColumnsHistoryEnabled NVARCHAR(MAX) = ''
            DECLARE @tColumnsHistoryEnabled NVARCHAR(MAX) = ''

            SELECT  @createColumns += ', ' + QUOTENAME(ModelColumnName) + ' ' +
                        CASE FieldType
                            WHEN 'String'    THEN 'NVARCHAR(256) COLLATE DATABASE_DEFAULT'
                            WHEN 'Integer'   THEN 'BIGINT'
                            WHEN 'DateTime'  THEN 'DATETIMEOFFSET'
                            WHEN 'Double'    THEN 'FLOAT'
                            WHEN 'Boolean'   THEN 'BIT'
                            WHEN 'Identity'  THEN 'UNIQUEIDENTIFIER'
                        END + @newLine,
                    @pivotAlias +=  QUOTENAME(SourceFieldName) + ', ',
                    @pivotSelectClause += ', ' +
                        CASE FieldType
                            WHEN 'String'    THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS NVARCHAR(256))'
                            WHEN 'Integer'   THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS BIGINT)'
                            WHEN 'DateTime'  THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS DATETIMEOFFSET) AT TIME ZONE @timeZone'
                            WHEN 'Double'    THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS FLOAT)'
                            WHEN 'Boolean'   THEN 'TRY_CAST(' + QUOTENAME(SourceFieldName) + ' AS BIT)'
                            WHEN 'Identity'  THEN 'IIF (ISJSON(CAST(' + QUOTENAME(SourceFieldName) + ' AS NVARCHAR(4000))) > 0, AnalyticsInternal.func_GetUserSKFromWITPerson(CAST(' + QUOTENAME(SourceFieldName) + ' AS NVARCHAR(4000))), NULL)'
                        END + ' AS ' + QUOTENAME(ModelColumnName) + @newLine,
                    @updateClause += ', ' + QUOTENAME(ModelColumnName) + ' = s.' + QUOTENAME(ModelColumnName) + @newLine,
                    @columns += ', ' + QUOTENAME(ModelColumnName) + @newLine,
                    @sColumns += ', s.' + QUOTENAME(ModelColumnName) + @newLine,
                    @sColumnsHistoryEnabled += IIF(IsHistoryEnabled = 1 OR @triggerBatchIdStart <= 1, ', s.' + QUOTENAME(ModelColumnName) + @newLine, ''),
                    @tColumnsHistoryEnabled += IIF(IsHistoryEnabled = 1 OR @triggerBatchIdStart <= 1, ', t.' + QUOTENAME(ModelColumnName) + @newLine, '')
            FROM    #ProcessField
            WHERE   ModelTableName = @customTableName

            SET @pivotAlias = LEFT(@pivotAlias, LEN(@pivotAlias) - 1)

            TRUNCATE TABLE #WorkItemsToUpdate
            TRUNCATE TABLE #WorkItemsToInsert

            -- Do we have records in a particular table to be updated?
            SET @cmd = N'
            INSERT  #WorkItemsToUpdate (WorkItemId, Revision, WorkItemRevisionSK)
            SELECT  s.WorkItemId,
                    s.Revision,
                    s.WorkItemRevisionSK
            FROM    AnalyticsModel.' + QUOTENAME('tbl_' + @customTableName) +' AS t
            JOIN    #WorkItems AS s
            ON      t.PartitionId = @partitionId
                    AND t.WorkItemRevisionSK = s.WorkItemRevisionSK
            OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))'

            EXEC sp_executesql @cmd,
                    N'@partitionId INT',
                    @partitionId = @partitionId

            INSERT  #WorkItemsToInsert (WorkItemId, Revision, WorkItemRevisionSK)
            SELECT  s.WorkItemId,
                    s.Revision,
                    s.WorkItemRevisionSK
            FROM    #WorkItems s
            LEFT JOIN #WorkItemsToUpdate u
            ON      u.WorkItemRevisionSK = s.WorkItemRevisionSK
            WHERE   u.WorkItemId IS NULL

            IF (EXISTS (SELECT * FROM #WorkItemsToUpdate))
            BEGIN
                SET @cmd = '
                CREATE TABLE #NewCustom
                (
                    WorkItemRevisionSK INT NOT NULL,
                    WorkItemId INT NOT NULL,
                    Revision INT NOT NULL
                    <CREATE_COLUMNS>
                    , PRIMARY KEY CLUSTERED (WorkItemRevisionSK)
                )
                WITH (DATA_COMPRESSION=ROW)

                INSERT  #NewCustom
                SELECT  WorkItemRevisionSK,
                        WorkItemId,
                        Revision
                        <PIVOT_SELECT_CLAUSE>
                FROM
                (
                    SELECT  s.WorkItemRevisionSK,
                            s.WorkItemId,
                            s.Revision,
                            x.FieldName,
                            x.[Value]
                    FROM    #ExtendedField x
                    JOIN    #WorkItemsToUpdate s
                    ON      x.System_Id = s.WorkItemId
                            AND x.System_Rev = s.Revision
                    WHERE   x.ModelTableName = @customTableName
                ) T
                PIVOT
                (
                    MAX([Value])
                    FOR FieldName IN (<PIVOT_AS_ALIAS>)
                ) AS pvt

                CREATE TABLE #DiffCustom
                (
                    WorkItemRevisionSK INT NOT NULL,
                    SrcRowHash VARBINARY(16) NULL,
                    TgtRowHash VARBINARY(16) NULL
                    PRIMARY KEY CLUSTERED (WorkItemRevisionSK)
                )

                INSERT  #DiffCustom
                (
                    WorkItemRevisionSK,
                    TgtRowHash
                )
                SELECT  s.WorkItemRevisionSK,
                        HASHBYTES(''MD5'', (
                        SELECT 0 AS LeadColumn
                        <T_COLUMNS_HE>
                        FOR XML RAW))
                FROM    #WorkItemsToUpdate s
                INNER HASH JOIN <TABLE_NAME> t WITH (INDEX (<CLUSTERED_INDEX_NAME>))
                ON      t.WorkItemRevisionSK = s.WorkItemRevisionSK
                WHERE   t.PartitionId = @partitionId
                OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))

                UPDATE  d
                SET     SrcRowHash = HASHBYTES(''MD5'', (
                            SELECT 0 AS LeadColumn
                            <S_COLUMNS_HE>
                            FOR XML RAW))
                FROM    #DiffCustom d
                JOIN    #NewCustom AS s
                ON      s.WorkItemRevisionSK = d.WorkItemRevisionSK

                -- remove those rows which match exactly, ignoring non-history fields
                DELETE  s
                FROM    #NewCustom s
                JOIN    #DiffCustom t
                ON      s.WorkItemRevisionSK = t.WorkItemRevisionSK
                WHERE   SrcRowHash = TgtRowHash

                -- update remaining rows
                IF ((SELECT COUNT(*) FROM #NewCustom) > 150)
                BEGIN
                    UPDATE  t
                    SET     AnalyticsUpdatedDate = @batchDt
                            , AnalyticsBatchId = @batchId
                            <UPDATE_CLAUSE>
                    FROM    #NewCustom AS s
                    INNER HASH JOIN <TABLE_NAME> AS t WITH (INDEX (<CLUSTERED_INDEX_NAME>))
                    ON      t.WorkItemRevisionSK = s.WorkItemRevisionSK
                    WHERE   t.PartitionId = @partitionId
                    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
                END
                ELSE
                BEGIN
                    UPDATE  t
                    SET     AnalyticsUpdatedDate = @batchDt
                            , AnalyticsBatchId = @batchId
                            <UPDATE_CLAUSE>
                    FROM    #NewCustom AS s
                    JOIN    <TABLE_NAME> AS t WITH (INDEX (<NONCLUSTERED_INDEX_NAME>))  -- allow for row locks
                    ON      t.WorkItemRevisionSK = s.WorkItemRevisionSK
                    WHERE   t.PartitionId = @partitionId
                    OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
                END

                INSERT  #ChangedWorkItems
                SELECT  WorkItemId, Revision
                FROM    #NewCustom

                DROP TABLE #NewCustom
                DROP TABLE #DiffCustom
                '

                SET @cmd = REPLACE(@cmd,'<CREATE_COLUMNS>', @createColumns)
                SET @cmd = REPLACE(@cmd,'<PIVOT_SELECT_CLAUSE>', @pivotSelectClause)
                SET @cmd = REPLACE(@cmd,'<PIVOT_AS_ALIAS>', @pivotAlias)
                SET @cmd = REPLACE(@cmd,'<TABLE_NAME>', '[AnalyticsModel].' + QUOTENAME('tbl_' + @customTableName))
                SET @cmd = REPLACE(@cmd,'<CLUSTERED_INDEX_NAME>', 'CL_AnalyticsModel_tbl_' + @customTableName)
                SET @cmd = REPLACE(@cmd,'<NONCLUSTERED_INDEX_NAME>', 'UQ_AnalyticsModel_tbl_' + @customTableName + '_WorkItemRevisionSK')
                SET @cmd = REPLACE(@cmd,'<UPDATE_CLAUSE>', @updateClause)
                SET @cmd = REPLACE(@cmd,'<COLUMNS>',@columns)
                SET @cmd = REPLACE(@cmd,'<S_COLUMNS_HE>',@sColumnsHistoryEnabled)
                SET @cmd = REPLACE(@cmd,'<T_COLUMNS_HE>',@tColumnsHistoryEnabled)

                EXEC sp_executesql @cmd,
                        N'@partitionId INT,
                          @batchId INT,
                          @batchDt DATETIME2,
                          @timeZone NVARCHAR(128),
                          @customTableName NVARCHAR(50)',
                        @partitionId = @partitionId,
                        @batchId = @batchId,
                        @batchDt = @batchDt,
                        @timeZone = @timeZone,
                        @customTableName = @customTableName

            END

            IF EXISTS (SELECT * FROM #WorkItemsToInsert)
            BEGIN
                SET @cmd = '
                INSERT  <TABLE_NAME> (
                    PartitionId
                    , AnalyticsBatchId
                    , AnalyticsCreatedDate
                    , AnalyticsUpdatedDate
                    , WorkItemId
                    , Revision
                    , WorkItemRevisionSK
                    <COLUMNS>
                    )
                OUTPUT  INSERTED.WorkItemId,
                        INSERTED.Revision
                INTO    #ChangedWorkItems
                SELECT  @partitionId
                        , @batchId
                        , @batchDt
                        , @batchDt
                        , s.WorkItemId
                        , s.Revision
                        , s.WorkItemRevisionSK
                        <S_COLUMNS>
                FROM
                (
                    SELECT  WorkItemId
                            , Revision
                            , WorkItemRevisionSK
                            <PIVOT_SELECT_CLAUSE>
                    FROM
                    (
                        SELECT  s.WorkItemId
                                , s.Revision
                                , s.WorkItemRevisionSK
                                , x.FieldName
                                , x.[Value]
                        FROM    #ExtendedField x
                        JOIN    #WorkItemsToInsert s
                        ON      x.System_Id = s.WorkItemId
                                AND x.System_Rev = s.Revision
                        WHERE   x.ModelTableName = @customTableName
                    ) T
                    PIVOT
                    (
                        MAX(Value)
                        FOR FieldName IN (<PIVOT_AS_ALIAS>)
                    ) AS pvt
                ) AS s
                OPTION (OPTIMIZE FOR (@partitionId UNKNOWN))
                '

                SET @cmd = REPLACE(@cmd,'<PIVOT_SELECT_CLAUSE>', @pivotSelectClause)
                SET @cmd = REPLACE(@cmd,'<PIVOT_AS_ALIAS>', @pivotAlias)
                SET @cmd = REPLACE(@cmd,'<TABLE_NAME>', '[AnalyticsModel].' + QUOTENAME('tbl_' + @customTableName))
                SET @cmd = REPLACE(@cmd,'<COLUMNS>',@columns)
                SET @cmd = REPLACE(@cmd,'<S_COLUMNS>',@sColumns)

                EXEC sp_executesql @cmd,
                        N'@partitionId INT,
                          @batchId INT,
                          @batchDt DATETIME2,
                          @timeZone NVARCHAR(128),
                          @customTableName NVARCHAR(50)',
                        @partitionId = @partitionId,
                        @batchId = @batchId,
                        @batchDt = @batchDt,
                        @timeZone = @timeZone,
                        @customTableName = @customTableName

            END

            DELETE FROM @CustomTables WHERE @customTableName = ModelTableName
        END

        SELECT DISTINCT WorkItemId, Revision
        FROM #ChangedWorkItems
END

GO

